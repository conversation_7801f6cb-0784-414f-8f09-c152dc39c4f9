{"is_case_sensitive": false, "is_disable_analytics_ip": false, "wildcards_is_active": false, "disablebotclicks": false, "force_https": false, "autolink_disable_post_types": [], "is_autolink_icon": false, "is_autolink_headings": true, "uncloaked_categories": [], "links": {"go/test": {"ID": 1, "link_slug": "test", "link_status": "publish", "short_url": "go/test", "redirect_type": "307", "target_url": "#pricing", "nofollow": "1", "sponsored": "", "param_forwarding": "", "track_me": "1", "wildcards": 0, "expire": null, "dynamic_redirect": null, "cat_id": "2"}}}