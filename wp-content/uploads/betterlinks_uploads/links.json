{"is_case_sensitive": false, "is_disable_analytics_ip": false, "wildcards_is_active": false, "disablebotclicks": false, "force_https": false, "autolink_disable_post_types": [], "is_autolink_icon": false, "is_autolink_headings": true, "uncloaked_categories": [], "links": {"go/test": {"ID": 1, "link_slug": "test", "link_status": "publish", "short_url": "go/test", "redirect_type": "307", "target_url": "#pricing", "nofollow": "1", "sponsored": "", "param_forwarding": "", "track_me": "1", "wildcards": 0, "expire": null, "dynamic_redirect": null, "cat_id": "2"}, "go/campaign-landing": {"ID": 2, "link_slug": "campaign-landing", "link_status": "publish", "short_url": "go/campaign-landing", "redirect_type": "307", "target_url": "https://simple-wishlist.test/campaign/special-offer?utm_source=campaign&utm_medium=shortlink&utm_campaign=main-landing", "nofollow": "1", "sponsored": false, "param_forwarding": false, "track_me": "1", "wildcards": false, "expire": null, "dynamic_redirect": null, "cat_id": "1"}, "go/exit-offer": {"ID": 3, "link_slug": "exit-offer", "link_status": "publish", "short_url": "go/exit-offer", "redirect_type": "307", "target_url": "https://simple-wishlist.test/pricing/unlimited-lifetime?utm_source=exit-popup&utm_medium=shortlink&utm_campaign=exit-intent-offer", "nofollow": "1", "sponsored": false, "param_forwarding": false, "track_me": "1", "wildcards": false, "expire": null, "dynamic_redirect": null, "cat_id": "1"}, "go/notify-cta": {"ID": 4, "link_slug": "notify-cta", "link_status": "publish", "short_url": "go/notify-cta", "redirect_type": "307", "target_url": "https://simple-wishlist.test/get-started?utm_source=notification-bar&utm_medium=shortlink&utm_campaign=top-banner-cta", "nofollow": "1", "sponsored": false, "param_forwarding": false, "track_me": "1", "wildcards": false, "expire": null, "dynamic_redirect": null, "cat_id": "1"}, "go/email-promo": {"ID": 5, "link_slug": "email-promo", "link_status": "publish", "short_url": "go/email-promo", "redirect_type": "307", "target_url": "https://simple-wishlist.test/products/featured?utm_source=newsletter&utm_medium=email&utm_campaign=weekly-newsletter", "nofollow": "1", "sponsored": false, "param_forwarding": false, "track_me": "1", "wildcards": false, "expire": null, "dynamic_redirect": null, "cat_id": "1"}, "go/social-campaign": {"ID": 6, "link_slug": "social-campaign", "link_status": "publish", "short_url": "go/social-campaign", "redirect_type": "307", "target_url": "https://simple-wishlist.test/special-deals?utm_source=social&utm_medium=facebook&utm_campaign=social-media-blast", "nofollow": "1", "sponsored": false, "param_forwarding": false, "track_me": "1", "wildcards": false, "expire": null, "dynamic_redirect": null, "cat_id": "1"}, "go/flash-sale": {"ID": 7, "link_slug": "flash-sale", "link_status": "publish", "short_url": "go/flash-sale", "redirect_type": "307", "target_url": "https://simple-wishlist.test/flash-sale?utm_source=campaign&utm_medium=shortlink&utm_campaign=limited-time-offer", "nofollow": "1", "sponsored": false, "param_forwarding": false, "track_me": "1", "wildcards": false, "expire": null, "dynamic_redirect": null, "cat_id": "1"}, "go/free-trial": {"ID": 8, "link_slug": "free-trial", "link_status": "publish", "short_url": "go/free-trial", "redirect_type": "307", "target_url": "https://simple-wishlist.test/signup/free-trial?utm_source=campaign&utm_medium=shortlink&utm_campaign=free-trial-signup", "nofollow": "1", "sponsored": false, "param_forwarding": false, "track_me": "1", "wildcards": false, "expire": null, "dynamic_redirect": null, "cat_id": "1"}, "go/notify-bar-cta-campaign-2025": {"ID": 9, "link_slug": "notify-bar-cta-campaign-2025", "link_status": "publish", "short_url": "go/notify-bar-cta-campaign-2025", "redirect_type": "307", "target_url": "https://simple-wishlist.test/pricing?utm_source=notification-bar&utm_medium=shortlink&utm_campaign=campaign-2025", "nofollow": "1", "sponsored": false, "param_forwarding": false, "track_me": "1", "wildcards": false, "expire": null, "dynamic_redirect": null, "cat_id": "4"}, "go/exit-popup-cta-campaign-2025": {"ID": 10, "link_slug": "exit-popup-cta-campaign-2025", "link_status": "publish", "short_url": "go/exit-popup-cta-campaign-2025", "redirect_type": "307", "target_url": "https://simple-wishlist.test/special-offer?utm_source=exit-popup&utm_medium=shortlink&utm_campaign=campaign-2025", "nofollow": "1", "sponsored": false, "param_forwarding": false, "track_me": "1", "wildcards": false, "expire": null, "dynamic_redirect": null, "cat_id": "4"}, "go/pricing-single-year-campaign-2025": {"ID": 11, "link_slug": "pricing-single-year-campaign-2025", "link_status": "publish", "short_url": "go/pricing-single-year-campaign-2025", "redirect_type": "307", "target_url": "https://simple-wishlist.test/checkout/single-site-yearly?utm_source=pricing&utm_medium=shortlink&utm_campaign=campaign-2025", "nofollow": "1", "sponsored": false, "param_forwarding": false, "track_me": "1", "wildcards": false, "expire": null, "dynamic_redirect": null, "cat_id": "4"}, "go/pricing-unlimited-year-campaign-2025": {"ID": 12, "link_slug": "pricing-unlimited-year-campaign-2025", "link_status": "publish", "short_url": "go/pricing-unlimited-year-campaign-2025", "redirect_type": "307", "target_url": "https://simple-wishlist.test/checkout/unlimited-yearly?utm_source=pricing&utm_medium=shortlink&utm_campaign=campaign-2025", "nofollow": "1", "sponsored": false, "param_forwarding": false, "track_me": "1", "wildcards": false, "expire": null, "dynamic_redirect": null, "cat_id": "4"}, "go/pricing-unlimited-lifetime-campaign-2025": {"ID": 13, "link_slug": "pricing-unlimited-lifetime-campaign-2025", "link_status": "publish", "short_url": "go/pricing-unlimited-lifetime-campaign-2025", "redirect_type": "307", "target_url": "https://simple-wishlist.test/checkout/unlimited-lifetime?utm_source=pricing&utm_medium=shortlink&utm_campaign=campaign-2025", "nofollow": "1", "sponsored": false, "param_forwarding": false, "track_me": "1", "wildcards": false, "expire": null, "dynamic_redirect": null, "cat_id": "4"}, "go/pricing-agency-year-campaign-2025": {"ID": 14, "link_slug": "pricing-agency-year-campaign-2025", "link_status": "publish", "short_url": "go/pricing-agency-year-campaign-2025", "redirect_type": "307", "target_url": "https://simple-wishlist.test/checkout/agency-yearly?utm_source=pricing&utm_medium=shortlink&utm_campaign=campaign-2025", "nofollow": "1", "sponsored": false, "param_forwarding": false, "track_me": "1", "wildcards": false, "expire": null, "dynamic_redirect": null, "cat_id": "4"}, "go/pricing-agency-lifetime-campaign-2025": {"ID": 15, "link_slug": "pricing-agency-lifetime-campaign-2025", "link_status": "publish", "short_url": "go/pricing-agency-lifetime-campaign-2025", "redirect_type": "307", "target_url": "https://simple-wishlist.test/checkout/agency-lifetime?utm_source=pricing&utm_medium=shortlink&utm_campaign=campaign-2025", "nofollow": "1", "sponsored": false, "param_forwarding": false, "track_me": "1", "wildcards": false, "expire": null, "dynamic_redirect": null, "cat_id": "4"}}}