[{"link_id": 9, "browser": "Chrome", "os": "<PERSON>", "device": "desktop", "referer": "https://simple-wishlist.test/wp-admin/admin.php?page=bettercampaign-dashboard", "uri": "notify-bar-cta-campaign-2025", "click_count": 0, "visitor_id": "bl688636fb2caea", "click_order": 0, "created_at": "2025-07-27 15:22:51", "created_at_gmt": "2025-07-27 15:22:51", "rotation_target_url": "https://simple-wishlist.test/pricing?utm_source=notification-bar&utm_medium=shortlink&utm_campaign=campaign-2025", "target_url": "https://simple-wishlist.test/pricing?utm_source=notification-bar&utm_medium=shortlink&utm_campaign=campaign-2025", "is_split_enabled": false, "ip": "127.0.0.1", "host": "127.0.0.1"}, {"link_id": 9, "browser": "Chrome", "os": "<PERSON>", "device": "desktop", "referer": "https://simple-wishlist.test/wp-admin/admin.php?page=betterlinks", "uri": "notify-bar-cta-campaign-2025", "click_count": 0, "visitor_id": "bl688636fb2caea", "click_order": 0, "created_at": "2025-07-27 15:24:17", "created_at_gmt": "2025-07-27 15:24:17", "rotation_target_url": "https://simple-wishlist.test/pricing?utm_source=notification-bar&utm_medium=shortlink&utm_campaign=campaign-2025", "target_url": "https://simple-wishlist.test/pricing?utm_source=notification-bar&utm_medium=shortlink&utm_campaign=campaign-2025", "is_split_enabled": false, "ip": "127.0.0.1", "host": "127.0.0.1"}]