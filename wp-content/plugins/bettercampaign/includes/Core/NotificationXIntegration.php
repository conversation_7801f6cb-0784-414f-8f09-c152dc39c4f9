<?php
/**
 * NotificationX Integration Class
 *
 * @package BetterCampaign\Core
 */

namespace BetterCampaign\Core;

use BetterCampaign\GetInstance;

/**
 * NotificationX Integration Class
 */
class NotificationXIntegration {
    /**
     * Instance of NotificationXIntegration
     *
     * @var NotificationXIntegration
     */
    use GetInstance;

    /**
     * Initialize the NotificationX integration
     */
    public function __construct() {
        add_action('wp_ajax_nx_create_notification_bar', [$this, 'create_notification_bar']);
        add_action('wp_ajax_nx_create_gutenberg_bar', [$this, 'create_gutenberg_bar']);
        add_action('wp_ajax_nx_update_notification_bar', [$this, 'update_notification_bar']);
        add_action('wp_ajax_nx_delete_notification_bar', [$this, 'delete_notification_bar']);
        add_action('wp_ajax_create_notification_bar', [$this, 'create_notification_in_nx_posts']);
    }

    /**
     * Create a notification bar using NotificationX structure
     */
    public function create_notification_bar() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['security'] ?? '', 'bettercampaign_nonce')) {
            wp_send_json_error(__('Security check failed', 'bettercampaign'));
            return;
        }

        // Check user permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Insufficient permissions', 'bettercampaign'));
            return;
        }

        try {
            // Sanitize input data
            $post_title = sanitize_text_field($_POST['post_title'] ?? '');
            $meta_input = $_POST['meta_input'] ?? [];
            $settings = $_POST['settings'] ?? [];

            if (empty($post_title)) {
                wp_send_json_error(__('Post title is required', 'bettercampaign'));
                return;
            }

            // Create the notification bar post
            $post_data = [
                'post_title' => $post_title,
                'post_status' => 'publish',
                'post_type' => 'nx_bar',
                'post_content' => '',
                'meta_input' => $this->sanitize_meta_input($meta_input),
            ];

            $post_id = wp_insert_post($post_data);

            if (is_wp_error($post_id)) {
                wp_send_json_error($post_id->get_error_message());
                return;
            }

            // Save additional settings
            if (!empty($settings)) {
                update_post_meta($post_id, '_nx_meta_settings', $settings);
            }

            // Generate URLs
            $edit_url = admin_url("post.php?post={$post_id}&action=edit");
            $preview_url = get_permalink($post_id);

            wp_send_json_success([
                'post_id' => $post_id,
                'edit_url' => $edit_url,
                'preview_url' => $preview_url,
                'message' => __('Notification bar created successfully', 'bettercampaign'),
            ]);

        } catch (Exception $e) {
            wp_send_json_error($e->getMessage());
        }
    }

    /**
     * Create a Gutenberg-based notification bar with nx_bar_gb mapping
     */
    public function create_gutenberg_bar() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['security'] ?? '', 'bettercampaign_nonce')) {
            wp_send_json_error(__('Security check failed', 'bettercampaign'));
            return;
        }

        // Check user permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Insufficient permissions', 'bettercampaign'));
            return;
        }

        try {
            // Sanitize input data
            $post_title = sanitize_text_field($_POST['post_title'] ?? '');
            $post_content = wp_kses_post($_POST['post_content'] ?? '');
            $meta_input = $_POST['meta_input'] ?? [];

            if (empty($post_title)) {
                wp_send_json_error(__('Post title is required', 'bettercampaign'));
                return;
            }

            // Create the Gutenberg bar post (nx_bar_gb custom post type)
            $post_data = [
                'post_title' => $post_title,
                'post_status' => 'publish',
                'post_type' => 'nx_bar_gb',
                'post_content' => $post_content,
                'meta_input' => $this->sanitize_meta_input($meta_input),
            ];

            $post_id = wp_insert_post($post_data);

            if (is_wp_error($post_id)) {
                wp_send_json_error($post_id->get_error_message());
                return;
            }

            // Generate URLs
            $edit_url = admin_url("post.php?post={$post_id}&action=edit");
            $preview_url = get_permalink($post_id);

            wp_send_json_success([
                'post_id' => $post_id,
                'edit_url' => $edit_url,
                'preview_url' => $preview_url,
                'message' => __('Gutenberg notification bar created successfully', 'bettercampaign'),
            ]);

        } catch (Exception $e) {
            wp_send_json_error($e->getMessage());
        }
    }

    /**
     * Update an existing notification bar
     */
    public function update_notification_bar() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['security'] ?? '', 'bettercampaign_nonce')) {
            wp_send_json_error(__('Security check failed', 'bettercampaign'));
            return;
        }

        // Check user permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Insufficient permissions', 'bettercampaign'));
            return;
        }

        try {
            $post_id = intval($_POST['post_id'] ?? 0);

            if (!$post_id) {
                wp_send_json_error(__('Post ID is required', 'bettercampaign'));
                return;
            }

            // Verify post exists and is correct type
            $post = get_post($post_id);
            if (!$post || !in_array($post->post_type, ['nx_bar', 'nx_bar_gb'])) {
                wp_send_json_error(__('Invalid post ID or post type', 'bettercampaign'));
                return;
            }

            // Update post data
            $update_data = ['ID' => $post_id];
            
            if (isset($_POST['post_title'])) {
                $update_data['post_title'] = sanitize_text_field($_POST['post_title']);
            }
            
            if (isset($_POST['post_content'])) {
                $update_data['post_content'] = wp_kses_post($_POST['post_content']);
            }

            $result = wp_update_post($update_data);

            if (is_wp_error($result)) {
                wp_send_json_error($result->get_error_message());
                return;
            }

            // Update meta fields
            if (isset($_POST['meta_input'])) {
                $meta_input = $this->sanitize_meta_input($_POST['meta_input']);
                foreach ($meta_input as $key => $value) {
                    update_post_meta($post_id, $key, $value);
                }
            }

            wp_send_json_success([
                'post_id' => $post_id,
                'message' => __('Notification bar updated successfully', 'bettercampaign'),
            ]);

        } catch (Exception $e) {
            wp_send_json_error($e->getMessage());
        }
    }

    /**
     * Delete a notification bar
     */
    public function delete_notification_bar() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['security'] ?? '', 'bettercampaign_nonce')) {
            wp_send_json_error(__('Security check failed', 'bettercampaign'));
            return;
        }

        // Check user permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Insufficient permissions', 'bettercampaign'));
            return;
        }

        try {
            $post_id = intval($_POST['post_id'] ?? 0);

            if (!$post_id) {
                wp_send_json_error(__('Post ID is required', 'bettercampaign'));
                return;
            }

            // Verify post exists and is correct type
            $post = get_post($post_id);
            if (!$post || !in_array($post->post_type, ['nx_bar', 'nx_bar_gb'])) {
                wp_send_json_error(__('Invalid post ID or post type', 'bettercampaign'));
                return;
            }

            $result = wp_delete_post($post_id, true);

            if (!$result) {
                wp_send_json_error(__('Failed to delete notification bar', 'bettercampaign'));
                return;
            }

            wp_send_json_success([
                'post_id' => $post_id,
                'message' => __('Notification bar deleted successfully', 'bettercampaign'),
            ]);

        } catch (Exception $e) {
            wp_send_json_error($e->getMessage());
        }
    }

    /**
     * Create a notification in the nx_posts table
     */
    public function create_notification_in_nx_posts() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'] ?? '', 'bettercampaign_nonce')) {
            wp_send_json_error(__('Security check failed', 'bettercampaign'));
            return;
        }

        // Check user permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Insufficient permissions', 'bettercampaign'));
            return;
        }

        try {
            global $wpdb;

            // Sanitize input data
            $title = sanitize_text_field($_POST['title'] ?? '');
            $headline = sanitize_text_field($_POST['headline'] ?? '');
            $cta_text = sanitize_text_field($_POST['cta_text'] ?? '');
            $cta_url = esc_url_raw($_POST['cta_url'] ?? '');
            $background_image = esc_url_raw($_POST['background_image'] ?? '');
            $placement = sanitize_text_field($_POST['placement'] ?? 'top');
            $countdown_enabled = intval($_POST['countdown_enabled'] ?? 0);
            $end_date = sanitize_text_field($_POST['end_date'] ?? '');

            if (empty($title) || empty($headline) || empty($cta_text) || empty($cta_url)) {
                wp_send_json_error(__('Required fields are missing', 'bettercampaign'));
                return;
            }

            // Get table prefix
            $table_name = $wpdb->prefix . 'nx_posts';

            // Prepare notification data
            $notification_data = [
                'post_title' => $title,
                'post_content' => wp_json_encode([
                    'headline' => $headline,
                    'cta_text' => $cta_text,
                    'cta_url' => $cta_url,
                    'background_image' => $background_image,
                    'placement' => $placement,
                    'countdown_enabled' => $countdown_enabled,
                    'end_date' => $end_date,
                ]),
                'post_status' => 'publish',
                'post_type' => 'nx_bar',
                'post_author' => get_current_user_id(),
                'post_date' => current_time('mysql'),
                'post_date_gmt' => current_time('mysql', 1),
                'post_modified' => current_time('mysql'),
                'post_modified_gmt' => current_time('mysql', 1),
            ];

            // Insert into nx_posts table
            $result = $wpdb->insert($table_name, $notification_data);

            if ($result === false) {
                throw new \Exception(__('Failed to insert notification into database', 'bettercampaign'));
            }

            $post_id = $wpdb->insert_id;

            // Generate URLs
            $edit_url = admin_url("post.php?post={$post_id}&action=edit");
            $preview_url = get_permalink($post_id) ?: home_url();

            wp_send_json_success([
                'post_id' => $post_id,
                'edit_url' => $edit_url,
                'preview_url' => $preview_url,
                'message' => __('Notification created successfully in nx_posts table', 'bettercampaign'),
            ]);

        } catch (\Exception $e) {
            wp_send_json_error($e->getMessage());
        }
    }

    /**
     * Sanitize meta input data
     *
     * @param array $meta_input
     * @return array
     */
    private function sanitize_meta_input($meta_input) {
        $sanitized = [];

        foreach ($meta_input as $key => $value) {
            $sanitized_key = sanitize_key($key);
            
            if (is_array($value) || is_object($value)) {
                $sanitized[$sanitized_key] = wp_json_encode($value);
            } else {
                $sanitized[$sanitized_key] = sanitize_text_field($value);
            }
        }

        return $sanitized;
    }

    /**
     * Register custom post types for NotificationX integration
     */
    public static function register_post_types() {
        // Register nx_bar post type if it doesn't exist
        if (!post_type_exists('nx_bar')) {
            register_post_type('nx_bar', [
                'labels' => [
                    'name' => __('Notification Bars', 'bettercampaign'),
                    'singular_name' => __('Notification Bar', 'bettercampaign'),
                ],
                'public' => false,
                'show_ui' => true,
                'show_in_menu' => false,
                'supports' => ['title', 'editor', 'custom-fields'],
                'capability_type' => 'post',
            ]);
        }

        // Register nx_bar_gb post type for Gutenberg integration
        if (!post_type_exists('nx_bar_gb')) {
            register_post_type('nx_bar_gb', [
                'labels' => [
                    'name' => __('Gutenberg Notification Bars', 'bettercampaign'),
                    'singular_name' => __('Gutenberg Notification Bar', 'bettercampaign'),
                ],
                'public' => true,
                'show_ui' => true,
                'show_in_menu' => true,
                'supports' => ['title', 'editor', 'custom-fields'],
                'capability_type' => 'post',
                'show_in_rest' => true,
            ]);
        }
    }
}
