<?php
/**
 * NotificationX Integration Class
 *
 * @package BetterCampaign\Core
 */

namespace BetterCampaign\Core;

use BetterCampaign\GetInstance;

/**
 * NotificationX Integration Class
 */
class NotificationXIntegration {
    /**
     * Instance of NotificationXIntegration
     *
     * @var NotificationXIntegration
     */
    use GetInstance;

    /**
     * Initialize the NotificationX integration
     */
    public function __construct() {
        add_action('wp_ajax_nx_create_notification_bar', [$this, 'create_notification_bar']);
        add_action('wp_ajax_nx_create_gutenberg_bar', [$this, 'create_gutenberg_bar']);
        add_action('wp_ajax_nx_update_notification_bar', [$this, 'update_notification_bar']);
        add_action('wp_ajax_nx_delete_notification_bar', [$this, 'delete_notification_bar']);
        add_action('wp_ajax_create_notification_bar', [$this, 'create_notification_in_nx_posts']);
    }

    /**
     * Create a notification bar using NotificationX structure
     */
    public function create_notification_bar() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['security'] ?? '', 'bettercampaign_nonce')) {
            wp_send_json_error(__('Security check failed', 'bettercampaign'));
            return;
        }

        // Check user permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Insufficient permissions', 'bettercampaign'));
            return;
        }

        try {
            // Sanitize input data
            $post_title = sanitize_text_field($_POST['post_title'] ?? '');
            $meta_input = $_POST['meta_input'] ?? [];
            $settings = $_POST['settings'] ?? [];

            if (empty($post_title)) {
                wp_send_json_error(__('Post title is required', 'bettercampaign'));
                return;
            }

            // Create the notification bar post
            $post_data = [
                'post_title' => $post_title,
                'post_status' => 'publish',
                'post_type' => 'nx_bar',
                'post_content' => '',
                'meta_input' => $this->sanitize_meta_input($meta_input),
            ];

            $post_id = wp_insert_post($post_data);

            if (is_wp_error($post_id)) {
                wp_send_json_error($post_id->get_error_message());
                return;
            }

            // Save additional settings
            if (!empty($settings)) {
                update_post_meta($post_id, '_nx_meta_settings', $settings);
            }

            // Generate URLs
            $edit_url = admin_url("post.php?post={$post_id}&action=edit");
            $preview_url = get_permalink($post_id);

            wp_send_json_success([
                'post_id' => $post_id,
                'edit_url' => $edit_url,
                'preview_url' => $preview_url,
                'message' => __('Notification bar created successfully', 'bettercampaign'),
            ]);

        } catch (\Exception $e) {
            wp_send_json_error($e->getMessage());
        }
    }

    /**
     * Create a Gutenberg-based notification bar with nx_bar_gb mapping
     */
    public function create_gutenberg_bar() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['security'] ?? '', 'bettercampaign_nonce')) {
            wp_send_json_error(__('Security check failed', 'bettercampaign'));
            return;
        }

        // Check user permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Insufficient permissions', 'bettercampaign'));
            return;
        }

        try {
            // Sanitize input data
            $post_title = sanitize_text_field($_POST['post_title'] ?? '');
            $post_content = wp_kses_post($_POST['post_content'] ?? '');
            $meta_input = $_POST['meta_input'] ?? [];

            if (empty($post_title)) {
                wp_send_json_error(__('Post title is required', 'bettercampaign'));
                return;
            }

            // Create the Gutenberg bar post (nx_bar_gb custom post type)
            $post_data = [
                'post_title' => $post_title,
                'post_status' => 'publish',
                'post_type' => 'nx_bar_gb',
                'post_content' => $post_content,
                'meta_input' => $this->sanitize_meta_input($meta_input),
            ];

            $post_id = wp_insert_post($post_data);

            if (is_wp_error($post_id)) {
                wp_send_json_error($post_id->get_error_message());
                return;
            }

            // Generate URLs
            $edit_url = admin_url("post.php?post={$post_id}&action=edit");
            $preview_url = get_permalink($post_id);

            wp_send_json_success([
                'post_id' => $post_id,
                'edit_url' => $edit_url,
                'preview_url' => $preview_url,
                'message' => __('Gutenberg notification bar created successfully', 'bettercampaign'),
            ]);

        } catch (\Exception $e) {
            wp_send_json_error($e->getMessage());
        }
    }

    /**
     * Update an existing notification bar
     */
    public function update_notification_bar() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['security'] ?? '', 'bettercampaign_nonce')) {
            wp_send_json_error(__('Security check failed', 'bettercampaign'));
            return;
        }

        // Check user permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Insufficient permissions', 'bettercampaign'));
            return;
        }

        try {
            $post_id = intval($_POST['post_id'] ?? 0);

            if (!$post_id) {
                wp_send_json_error(__('Post ID is required', 'bettercampaign'));
                return;
            }

            // Verify post exists and is correct type
            $post = get_post($post_id);
            if (!$post || !in_array($post->post_type, ['nx_bar', 'nx_bar_gb'])) {
                wp_send_json_error(__('Invalid post ID or post type', 'bettercampaign'));
                return;
            }

            // Update post data
            $update_data = ['ID' => $post_id];
            
            if (isset($_POST['post_title'])) {
                $update_data['post_title'] = sanitize_text_field($_POST['post_title']);
            }
            
            if (isset($_POST['post_content'])) {
                $update_data['post_content'] = wp_kses_post($_POST['post_content']);
            }

            $result = wp_update_post($update_data);

            if (is_wp_error($result)) {
                wp_send_json_error($result->get_error_message());
                return;
            }

            // Update meta fields
            if (isset($_POST['meta_input'])) {
                $meta_input = $this->sanitize_meta_input($_POST['meta_input']);
                foreach ($meta_input as $key => $value) {
                    update_post_meta($post_id, $key, $value);
                }
            }

            wp_send_json_success([
                'post_id' => $post_id,
                'message' => __('Notification bar updated successfully', 'bettercampaign'),
            ]);

        } catch (\Exception $e) {
            wp_send_json_error($e->getMessage());
        }
    }

    /**
     * Delete a notification bar
     */
    public function delete_notification_bar() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['security'] ?? '', 'bettercampaign_nonce')) {
            wp_send_json_error(__('Security check failed', 'bettercampaign'));
            return;
        }

        // Check user permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Insufficient permissions', 'bettercampaign'));
            return;
        }

        try {
            $post_id = intval($_POST['post_id'] ?? 0);

            if (!$post_id) {
                wp_send_json_error(__('Post ID is required', 'bettercampaign'));
                return;
            }

            // Verify post exists and is correct type
            $post = get_post($post_id);
            if (!$post || !in_array($post->post_type, ['nx_bar', 'nx_bar_gb'])) {
                wp_send_json_error(__('Invalid post ID or post type', 'bettercampaign'));
                return;
            }

            $result = wp_delete_post($post_id, true);

            if (!$result) {
                wp_send_json_error(__('Failed to delete notification bar', 'bettercampaign'));
                return;
            }

            wp_send_json_success([
                'post_id' => $post_id,
                'message' => __('Notification bar deleted successfully', 'bettercampaign'),
            ]);

        } catch (\Exception $e) {
            wp_send_json_error($e->getMessage());
        }
    }

    /**
     * Create a notification in the nx_posts table
     */
    public function create_notification_in_nx_posts() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'] ?? '', 'bettercampaign_nonce')) {
            wp_send_json_error(__('Security check failed', 'bettercampaign'));
            return;
        }

        // Check user permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Insufficient permissions', 'bettercampaign'));
            return;
        }

        try {
            // Sanitize input data
            $title = sanitize_text_field($_POST['title'] ?? '');
            $headline = sanitize_text_field($_POST['headline'] ?? '');
            $cta_text = sanitize_text_field($_POST['cta_text'] ?? '');
            $cta_url = esc_url_raw($_POST['cta_url'] ?? '');
            $background_image = esc_url_raw($_POST['background_image'] ?? '');
            $placement = sanitize_text_field($_POST['placement'] ?? 'top');
            $countdown_enabled = intval($_POST['countdown_enabled'] ?? 0);
            $end_date = sanitize_text_field($_POST['end_date'] ?? '');

            if (empty($title) || empty($headline) || empty($cta_text) || empty($cta_url)) {
                wp_send_json_error(__('Required fields are missing', 'bettercampaign'));
                return;
            }

            // Step 1: Create Gutenberg post (nx_bar_gb)
            $gutenberg_post_id = $this->create_gutenberg_notification_post($title, $headline, $cta_text, $cta_url, $background_image);

            if (!$gutenberg_post_id) {
                throw new \Exception(__('Failed to create Gutenberg notification post', 'bettercampaign'));
            }

            // Step 2: Create NotificationX campaign using the Gutenberg post ID
            $campaign_data = [
                'title' => $title,
                'headline' => $headline,
                'cta_text' => $cta_text,
                'cta_url' => $cta_url,
                'background_image' => $background_image,
                'placement' => $placement,
                'countdown_enabled' => $countdown_enabled,
                'end_date' => $end_date,
                'gutenberg_post_id' => $gutenberg_post_id,
            ];

            $nx_post_id = $this->create_notificationx_campaign($campaign_data);

            if (!$nx_post_id) {
                // Clean up the Gutenberg post if campaign creation fails
                wp_delete_post($gutenberg_post_id, true);
                throw new \Exception(__('Failed to create NotificationX campaign', 'bettercampaign'));
            }

            // Generate URLs
            $edit_url = get_edit_post_link($gutenberg_post_id);
            $preview_url = get_permalink($gutenberg_post_id) ?: home_url();

            wp_send_json_success([
                'notification_bar' => [
                    'post_id' => $nx_post_id,
                    'edit_url' => $edit_url,
                ],
                'campaign' => [
                    'id' => $gutenberg_post_id,
                    'preview_url' => $preview_url,
                ],
                'message' => __('Notification bar created successfully', 'bettercampaign'),
            ]);

        } catch (\Exception $e) {
            wp_send_json_error($e->getMessage());
        }
    }

    /**
     * Create a Gutenberg notification post (nx_bar_gb)
     *
     * @param string $title
     * @param string $headline
     * @param string $cta_text
     * @param string $cta_url
     * @param string $background_image
     * @return int|false Post ID on success, false on failure
     */
    private function create_gutenberg_notification_post($title, $headline, $cta_text, $cta_url, $background_image = '') {
        $post_data = [
            'post_title' => $title,
            'post_content' => $this->generate_gutenberg_content($headline, $cta_text, $cta_url, $background_image),
            'post_status' => 'publish',
            'post_type' => 'nx_bar_gb',
            'post_author' => get_current_user_id(),
            'meta_input' => [
                '_nx_headline' => $headline,
                '_nx_cta_text' => $cta_text,
                '_nx_cta_url' => $cta_url,
                '_nx_background_image' => $background_image,
            ],
        ];

        return wp_insert_post($post_data);
    }

    /**
     * Generate Gutenberg block content for the notification bar
     *
     * @param string $headline
     * @param string $cta_text
     * @param string $cta_url
     * @param string $background_image
     * @return string
     */
    private function generate_gutenberg_content($headline, $cta_text, $cta_url, $background_image = '') {
        $background_style = '';
        if (!empty($background_image)) {
            $background_style = sprintf('background-image: url(%s); background-size: cover; background-position: center;', esc_url($background_image));
        }

        return sprintf(
            '<!-- wp:group {"style":{"spacing":{"padding":{"top":"1rem","bottom":"1rem","left":"2rem","right":"2rem"}},"color":{"background":"#f8f9fa"}},"layout":{"type":"flex","flexWrap":"nowrap","justifyContent":"space-between"}} -->
<div class="wp-block-group has-background" style="background-color:#f8f9fa;padding-top:1rem;padding-right:2rem;padding-bottom:1rem;padding-left:2rem;%s">
<!-- wp:heading {"level":3,"style":{"typography":{"fontWeight":"600"}}} -->
<h3 style="font-weight:600">%s</h3>
<!-- /wp:heading -->

<!-- wp:buttons -->
<div class="wp-block-buttons">
<!-- wp:button {"backgroundColor":"primary","textColor":"white"} -->
<div class="wp-block-button"><a class="wp-block-button__link has-white-color has-primary-background-color has-text-color has-background wp-element-button" href="%s">%s</a></div>
<!-- /wp:button -->
</div>
<!-- /wp:buttons -->
</div>
<!-- /wp:group -->',
            $background_style,
            esc_html($headline),
            esc_url($cta_url),
            esc_html($cta_text)
        );
    }

    /**
     * Create a NotificationX campaign entry in nx_posts table
     *
     * @param array $campaign_data
     * @return int|false Post ID on success, false on failure
     */
    private function create_notificationx_campaign($campaign_data) {
        global $wpdb;

        $title = $campaign_data['title'];
        $headline = $campaign_data['headline'];
        $cta_text = $campaign_data['cta_text'];
        $cta_url = $campaign_data['cta_url'];
        $background_image = $campaign_data['background_image'] ?? '';
        $placement = $campaign_data['placement'] ?? 'top';
        $countdown_enabled = $campaign_data['countdown_enabled'] ?? 0;
        $end_date = $campaign_data['end_date'] ?? '';
        $gutenberg_post_id = $campaign_data['gutenberg_post_id'];

        // Validate Gutenberg post ID
        if (!get_post($gutenberg_post_id) || get_post_type($gutenberg_post_id) !== 'nx_bar_gb') {
            return false;
        }

        // Build the full data payload as specified
        $data_payload = [
            '_locale'                    => 'user',
            'updated_at'                 => gmdate('Y-m-d\TH:i:s.v\Z'),
            'type'                       => 'notification_bar',
            'source'                     => 'press_bar',
            'show_notification_image'    => 'featured_image',
            'themes'                     => 'press_bar_theme-one',
            'position'                   => $placement === 'bottom' ? 'bottom' : 'top',
            'link_type'                  => 'product_page',
            'template_adv'               => false,
            'show_default_image'         => false,
            'show_on'                    => 'everywhere',
            'show_on_display'            => 'always',
            'size' => [
                'desktop' => 500,
                'tablet'  => 500,
                'mobile'  => 500,
            ],
            'animation_notification_show' => 'default',
            'animation_notification_hide' => 'default',
            'global_queue'                => false,
            'delay_before'                => 5,
            'display_for'                 => 5,
            'delay_between'              => 5,
            'display_last'               => 20,
            'display_from'               => 30,
            'loop'                        => true,
            'link_open'                   => false,
            'themes_tab'                  => 'for_desktop',
            'notification-template' => [
                'first_param'          => 'tag_name',
                'custom_first_param'   => 'Someone',
                'second_param'         => 'just purchased',
                'third_param'          => 'tag_product_title',
                'custom_third_param'   => 'Anonymous Product',
                'fourth_param'         => 'tag_time',
                'custom_fourth_param'  => 'Some time ago',
            ],
            'link_button'               => true,
            'image_shape'               => 'square',
            'image_shape_default'       => 'square',
            'random_order'              => false,
            'product_control'           => 'none',
            'product_exclude_by'        => 'none',
            'order_status'              => ['wc-completed', 'wc-processing'],
            'combine_multiorder'        => true,
            'close_button'              => true,
            'close_button_tab'          => true,
            'close_button_mobile'       => true,
            'hide_on_desktop'           => true,
            'hide_on_tab'               => true,
            'hide_on_mobile'            => true,
            'advance_edit'              => false,
            'display_from_hour'         => '0',
            'display_from_minute'       => '0',
            'button_text'               => $cta_text,
            'button_url'                => $cta_url,
            'bar_reappearance'          => 'show_welcomebar_every_page',
            'sticky_bar'                => false,
            'pressbar_body'             => false,
            'targeting_user_roles'      => ['all_users'],
            'auto_hide'                 => false,
            'appear_condition'          => 'after_few_seconds',
            'enable_countdown'          => $countdown_enabled ? '1' : '0',
            'nx_bar_border_radius_left'   => '0',
            'nx_bar_border_radius_right'  => '0',
            'nx_bar_border_radius_top'    => '0',
            'nx_bar_border_radius_bottom' => '0',
            'button_icon'               => 'none',
            'bar_bg_color'              => '#DDDDDD',
            'press_content'             => sprintf('<b>%s</b>', esc_html($headline)),
            'link_button_bg_color'      => '#000',
            'link_button_text_color'    => '#FFFFFF',
            'elementor_id'              => false,
            'gutenberg_id'              => (int) $gutenberg_post_id,
            'bar_content_type'          => 'static',
            'enable_coupon'             => false,
            'close_forever'             => false,
            'close_after_expire'        => false,
            'schedule_type'             => 'daily',
            'evergreen_timer'           => false,
            'countdown_text'            => 'Ending in',
            'countdown_expired_text'    => 'Expired',
            'countdown_start_date'      => current_time('mysql'),
            'countdown_end_date'        => !empty($end_date) ? $end_date : date('Y-m-d H:i:s', current_time('timestamp') + 7 * DAY_IN_SECONDS),
            'initial_delay'             => 5,
            'import_gutenberg_theme'    => true,
            'gutenberg_bar_theme'       => 'theme-one',
            'gutenberg_edit_link'       => get_edit_post_link($gutenberg_post_id),
            'currentTab'                => 'design_tab',
            'enabled'                   => true,
            'countdown_rand'            => wp_rand(),
            '__product_list'            => [],
            '__exclude_products'        => [],
            '__form_list'               => [],
            '__ld_course_list'          => [],
            '__give_form_list'          => [],
        ];

        // Prepare insert data for nx_posts table
        $insert_data = [
            'title'        => sanitize_text_field($title),
            'type'         => 'notification_bar',
            'source'       => 'press_bar',
            'theme'        => 'theme-one',
            'is_inline'    => 0,
            'global_queue' => 0,
            'enabled'      => 1,
            'data'         => maybe_serialize($data_payload),
            'created_at'   => current_time('mysql'),
            'updated_at'   => current_time('mysql'),
        ];

        $table_name = $wpdb->prefix . 'nx_posts';
        $result = $wpdb->insert($table_name, $insert_data);

        if ($result === false) {
            return false;
        }

        return $wpdb->insert_id;
    }

    /**
     * Sanitize meta input data
     *
     * @param array $meta_input
     * @return array
     */
    private function sanitize_meta_input($meta_input) {
        $sanitized = [];

        foreach ($meta_input as $key => $value) {
            $sanitized_key = sanitize_key($key);
            
            if (is_array($value) || is_object($value)) {
                $sanitized[$sanitized_key] = wp_json_encode($value);
            } else {
                $sanitized[$sanitized_key] = sanitize_text_field($value);
            }
        }

        return $sanitized;
    }

    /**
     * Register custom post types for NotificationX integration
     */
    public static function register_post_types() {
        // Register nx_bar post type if it doesn't exist
        if (!post_type_exists('nx_bar')) {
            register_post_type('nx_bar', [
                'labels' => [
                    'name' => __('Notification Bars', 'bettercampaign'),
                    'singular_name' => __('Notification Bar', 'bettercampaign'),
                ],
                'public' => false,
                'show_ui' => true,
                'show_in_menu' => false,
                'supports' => ['title', 'editor', 'custom-fields'],
                'capability_type' => 'post',
            ]);
        }

        // Register nx_bar_gb post type for Gutenberg integration
        if (!post_type_exists('nx_bar_gb')) {
            register_post_type('nx_bar_gb', [
                'labels' => [
                    'name' => __('Gutenberg Notification Bars', 'bettercampaign'),
                    'singular_name' => __('Gutenberg Notification Bar', 'bettercampaign'),
                ],
                'public' => true,
                'show_ui' => true,
                'show_in_menu' => true,
                'supports' => ['title', 'editor', 'custom-fields'],
                'capability_type' => 'post',
                'show_in_rest' => true,
            ]);
        }
    }
}
