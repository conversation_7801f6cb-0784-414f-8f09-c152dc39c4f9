import { __ } from '@wordpress/i18n';
import React, { useState } from 'react';
import { Button } from '@/bettercampaign/components/ui/button';
import { Badge } from '@/bettercampaign/components/ui/badge';
import { LoadingSpinner } from '@/bettercampaign/components/ui/loading-spinner';
import { useToast } from '@/bettercampaign/components/ui/use-toast';
import { Popover, PopoverContent, PopoverTrigger } from '@/bettercampaign/components/ui/popover';
import { 
  Wand2, 
  Target, 
  Sparkles,
  RefreshCw
} from 'lucide-react';

interface AICTAPopoverProps {
  onApplyContent?: (content: string, field: string) => void;
}

interface AIGeneratedContent {
  id: string;
  content: string;
  confidence: number;
}

// Predefined CTA templates
const CTA_TEMPLATES = [
  'Shop Now',
  'Claim Offer',
  'Get Deal',
  'Start Free Trial',
  'Learn More',
  'Buy Now',
  'Get Started',
  'Sign Up Today',
  'Download Now',
  'Book Now',
  'Try It Free',
  'Join Now'
];

export const AICTAPopover: React.FC<AICTAPopoverProps> = ({ onApplyContent }) => {
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedContent, setGeneratedContent] = useState<AIGeneratedContent[]>([]);
  const [isOpen, setIsOpen] = useState(false);
  const { toast } = useToast();

  const handleGenerateContent = async () => {
    setIsGenerating(true);
    
    try {
      // Simulate AI generation - replace with actual API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      const shuffled = [...CTA_TEMPLATES].sort(() => 0.5 - Math.random());
      const selected = shuffled.slice(0, 4);
      
      const mockContent: AIGeneratedContent[] = selected.map((content, index) => ({
        id: `cta-${Date.now()}-${index}`,
        content,
        confidence: Math.random() * 0.2 + 0.8, // 80-100% confidence
      }));
      
      setGeneratedContent(mockContent);
      
      toast({
        title: __('CTAs Generated!', 'bettercampaign'),
        description: __('AI has generated call-to-action suggestions for you.', 'bettercampaign'),
      });
    } catch (error) {
      toast({
        title: __('Error', 'bettercampaign'),
        description: __('Failed to generate CTAs. Please try again.', 'bettercampaign'),
        variant: 'destructive',
      });
    } finally {
      setIsGenerating(false);
    }
  };

  const handleUseContent = (content: string) => {
    onApplyContent?.(content, 'cta');
    toast({
      title: __('Applied!', 'bettercampaign'),
      description: __('Call-to-action has been applied to the form.', 'bettercampaign'),
    });
    // Close popover after applying content
    setIsOpen(false);
  };

  const handleClearContent = () => {
    setGeneratedContent([]);
  };

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          size="sm"
          className="gap-2 text-green-600 border-green-200 hover:bg-green-50 hover:border-green-300"
          title={__('Generate Call-to-Action with AI', 'bettercampaign')}
        >
          <Target className="h-4 w-4" />
          {__('AI', 'bettercampaign')}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-80 p-0" align="start" side="bottom" sideOffset={8}>
        <div className="p-4 space-y-4">
          {/* Header */}
          <div className="flex items-center gap-2">
            <div className="p-1.5 bg-green-50 rounded-md">
              <Target className="h-4 w-4 text-green-600" />
            </div>
            <div>
              <h3 className="font-medium text-sm">{__('AI CTA Generator', 'bettercampaign')}</h3>
              <p className="text-xs text-gray-600">{__('Generate compelling call-to-actions', 'bettercampaign')}</p>
            </div>
          </div>

          {/* Quick Templates */}
          <div className="space-y-3">
            <div>
              <h4 className="text-xs font-medium text-gray-700 mb-2">{__('Quick Templates', 'bettercampaign')}</h4>
              <div className="grid grid-cols-2 gap-1">
                {CTA_TEMPLATES.slice(0, 4).map((template, index) => (
                  <div key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded text-xs">
                    <span className="flex-1 truncate">{template}</span>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleUseContent(template)}
                      className="h-5 px-1 text-xs ml-1"
                    >
                      {__('Use', 'bettercampaign')}
                    </Button>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Generate Button */}
          <div className="space-y-2">
            <h4 className="text-xs font-medium text-gray-700">{__('Generate with AI', 'bettercampaign')}</h4>
            <Button
              variant="outline"
              size="sm"
              onClick={handleGenerateContent}
              disabled={isGenerating}
              className="w-full h-auto p-3 flex flex-col items-center gap-2 hover:bg-green-50 hover:border-green-200"
            >
              <Target className="h-4 w-4 text-green-600" />
              <span className="text-xs font-medium">{__('Generate CTAs', 'bettercampaign')}</span>
            </Button>
          </div>

          {/* Loading State */}
          {isGenerating && (
            <div className="flex items-center justify-center py-4">
              <LoadingSpinner size="sm" text={__('Generating...', 'bettercampaign')} />
            </div>
          )}

          {/* Generated Content */}
          {generatedContent.length > 0 && (
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <h4 className="text-xs font-medium text-gray-700">{__('Generated CTAs', 'bettercampaign')}</h4>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleClearContent}
                  className="h-6 px-2 text-xs"
                >
                  <RefreshCw className="h-3 w-3 mr-1" />
                  {__('Clear', 'bettercampaign')}
                </Button>
              </div>
              
              <div className="space-y-2 max-h-40 overflow-y-auto">
                {generatedContent.map((item) => (
                  <div key={item.id} className="border rounded p-2 space-y-2 bg-white">
                    <div className="flex items-center justify-between">
                      <Badge 
                        variant="secondary" 
                        className="text-xs bg-green-100 text-green-800"
                      >
                        {__('cta', 'bettercampaign')}
                      </Badge>
                      <span className="text-xs text-gray-500">
                        {Math.round(item.confidence * 100)}%
                      </span>
                    </div>
                    
                    <p className="text-xs leading-relaxed font-medium">{item.content}</p>
                    
                    <Button
                      variant="default"
                      size="sm"
                      onClick={() => handleUseContent(item.content)}
                      className="w-full h-6 text-xs bg-green-600 hover:bg-green-700"
                    >
                      {__('Use This CTA', 'bettercampaign')}
                    </Button>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </PopoverContent>
    </Popover>
  );
};
