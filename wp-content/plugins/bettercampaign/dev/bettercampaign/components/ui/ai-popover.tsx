import { __ } from '@wordpress/i18n';
import React, { useState } from 'react';
import { Button } from '@/bettercampaign/components/ui/button';
import { Badge } from '@/bettercampaign/components/ui/badge';
import { LoadingSpinner } from '@/bettercampaign/components/ui/loading-spinner';
import { useToast } from '@/bettercampaign/components/ui/use-toast';
import { Popover, PopoverContent, PopoverTrigger } from '@/bettercampaign/components/ui/popover';
import { 
  Wand2, 
  MessageSquare, 
  Target, 
  Sparkles,
  RefreshCw
} from 'lucide-react';

interface AIPopoverProps {
  onApplyContent?: (content: string, field: string) => void;
}

interface AIGeneratedContent {
  id: string;
  type: 'headline' | 'cta';
  content: string;
  confidence: number;
}

// Predefined content templates
const CONTENT_TEMPLATES = {
  headline: [
    '🎉 Limited Time Offer - Save 50% Today!',
    '⚡ Flash Sale: Up to 70% Off Everything!',
    '🔥 Don\'t Miss Out - Sale Ends Soon!',
    '💫 Exclusive Deal Just for You!',
    '🚀 Transform Your Business Today!'
  ],
  cta: [
    'Shop Now',
    'Claim Offer',
    'Get Deal',
    'Start Free Trial',
    'Learn More',
    'Buy Now',
    'Get Started'
  ]
};

export const AIPopover: React.FC<AIPopoverProps> = ({ onApplyContent }) => {
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedContent, setGeneratedContent] = useState<AIGeneratedContent[]>([]);
  const [isOpen, setIsOpen] = useState(false);
  const { toast } = useToast();

  const handleGenerateContent = async (type: 'headline' | 'cta') => {
    setIsGenerating(true);
    
    try {
      // Simulate AI generation - replace with actual API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      const templates = CONTENT_TEMPLATES[type];
      const shuffled = [...templates].sort(() => 0.5 - Math.random());
      const selected = shuffled.slice(0, 3);
      
      const mockContent: AIGeneratedContent[] = selected.map((content, index) => ({
        id: `${type}-${Date.now()}-${index}`,
        type,
        content,
        confidence: Math.random() * 0.2 + 0.8, // 80-100% confidence
      }));
      
      setGeneratedContent(mockContent);
      
      toast({
        title: __('Content Generated!', 'bettercampaign'),
        description: __(`AI has generated ${type} suggestions for you.`, 'bettercampaign'),
      });
    } catch (error) {
      toast({
        title: __('Error', 'bettercampaign'),
        description: __('Failed to generate content. Please try again.', 'bettercampaign'),
        variant: 'destructive',
      });
    } finally {
      setIsGenerating(false);
    }
  };

  const handleUseContent = (content: string, field: string) => {
    onApplyContent?.(content, field);
    toast({
      title: __('Applied!', 'bettercampaign'),
      description: __('Content has been applied to the form.', 'bettercampaign'),
    });
    // Close popover after applying content
    setIsOpen(false);
  };

  const handleClearContent = () => {
    setGeneratedContent([]);
  };

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          size="sm"
          className="gap-2 text-blue-600 border-blue-200 hover:bg-blue-50 hover:border-blue-300"
        >
          <Wand2 className="h-4 w-4" />
          {__('AI', 'bettercampaign')}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-80 p-0" align="start" side="bottom" sideOffset={8}>
        <div className="p-4 space-y-4">
          {/* Header */}
          <div className="flex items-center gap-2">
            <div className="p-1.5 bg-blue-50 rounded-md">
              <Sparkles className="h-4 w-4 text-blue-600" />
            </div>
            <div>
              <h3 className="font-medium text-sm">{__('AI Content Generator', 'bettercampaign')}</h3>
              <p className="text-xs text-gray-600">{__('Generate content for your notification', 'bettercampaign')}</p>
            </div>
          </div>

          {/* Predefined Templates */}
          <div className="space-y-3">
            <div>
              <h4 className="text-xs font-medium text-gray-700 mb-2">{__('Quick Templates', 'bettercampaign')}</h4>
              <div className="space-y-1">
                {CONTENT_TEMPLATES.headline.slice(0, 2).map((template, index) => (
                  <div key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded text-xs">
                    <span className="flex-1 truncate">{template}</span>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleUseContent(template, 'headline')}
                      className="h-6 px-2 text-xs"
                    >
                      {__('Use', 'bettercampaign')}
                    </Button>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Generate Buttons */}
          <div className="space-y-2">
            <h4 className="text-xs font-medium text-gray-700">{__('Generate with AI', 'bettercampaign')}</h4>
            <div className="grid grid-cols-2 gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleGenerateContent('headline')}
                disabled={isGenerating}
                className="h-auto p-2 flex flex-col items-center gap-1 text-xs"
              >
                <MessageSquare className="h-3 w-3" />
                {__('Headline', 'bettercampaign')}
              </Button>
              
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleGenerateContent('cta')}
                disabled={isGenerating}
                className="h-auto p-2 flex flex-col items-center gap-1 text-xs"
              >
                <Target className="h-3 w-3" />
                {__('Call-to-Action', 'bettercampaign')}
              </Button>
            </div>
          </div>

          {/* Loading State */}
          {isGenerating && (
            <div className="flex items-center justify-center py-4">
              <LoadingSpinner size="sm" text={__('Generating...', 'bettercampaign')} />
            </div>
          )}

          {/* Generated Content */}
          {generatedContent.length > 0 && (
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <h4 className="text-xs font-medium text-gray-700">{__('Generated Content', 'bettercampaign')}</h4>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleClearContent}
                  className="h-6 px-2 text-xs"
                >
                  <RefreshCw className="h-3 w-3 mr-1" />
                  {__('Clear', 'bettercampaign')}
                </Button>
              </div>
              
              <div className="space-y-2 max-h-40 overflow-y-auto">
                {generatedContent.map((item) => (
                  <div key={item.id} className="border rounded p-2 space-y-2 bg-white">
                    <div className="flex items-center justify-between">
                      <Badge 
                        variant="secondary" 
                        className={`text-xs ${
                          item.type === 'headline' ? 'bg-blue-100 text-blue-800' : 'bg-green-100 text-green-800'
                        }`}
                      >
                        {item.type}
                      </Badge>
                      <span className="text-xs text-gray-500">
                        {Math.round(item.confidence * 100)}%
                      </span>
                    </div>
                    
                    <p className="text-xs leading-relaxed">{item.content}</p>
                    
                    <Button
                      variant="default"
                      size="sm"
                      onClick={() => handleUseContent(item.content, item.type)}
                      className="w-full h-6 text-xs bg-blue-600 hover:bg-blue-700"
                    >
                      {__('Use This', 'bettercampaign')}
                    </Button>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </PopoverContent>
    </Popover>
  );
};
