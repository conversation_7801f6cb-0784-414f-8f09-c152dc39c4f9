import { __ } from '@wordpress/i18n';
import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/bettercampaign/components/ui/card';
import { Button } from '@/bettercampaign/components/ui/button';
import { Badge } from '@/bettercampaign/components/ui/badge';
import { Textarea } from '@/bettercampaign/components/ui/textarea';
import { LoadingSpinner } from '@/bettercampaign/components/ui/loading-spinner';
import { useToast } from '@/bettercampaign/components/ui/use-toast';
import { 
  X, 
  Sparkles, 
  Copy, 
  RefreshCw, 
  Wand2,
  MessageSquare,
  Target,
  Zap
} from 'lucide-react';

interface AIContentDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  context?: {
    type: 'notification' | 'campaign' | 'general';
    data?: any;
  };
  onApplyContent?: (content: string, field: string) => void;
}

interface AIGeneratedContent {
  id: string;
  type: 'headline' | 'cta' | 'description' | 'copy';
  content: string;
  confidence: number;
}

export const AIContentDrawer: React.FC<AIContentDrawerProps> = ({
  isOpen,
  onClose,
  context,
  onApplyContent,
}) => {
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedContent, setGeneratedContent] = useState<AIGeneratedContent[]>([]);
  const [customPrompt, setCustomPrompt] = useState('');
  const { toast } = useToast();

  const handleGenerateContent = async (type: 'headline' | 'cta' | 'description' | 'copy', prompt?: string) => {
    setIsGenerating(true);
    
    try {
      // Simulate AI generation - replace with actual API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      const mockContent: AIGeneratedContent[] = [
        {
          id: `${type}-1`,
          type,
          content: type === 'headline' 
            ? '🎉 Limited Time Offer - Save 50% Today!'
            : type === 'cta'
            ? 'Shop Now'
            : type === 'description'
            ? 'Don\'t miss out on our biggest sale of the year! Get up to 50% off on all products.'
            : 'Transform your business with our premium solutions. Join thousands of satisfied customers.',
          confidence: 0.95,
        },
        {
          id: `${type}-2`,
          type,
          content: type === 'headline'
            ? '⚡ Flash Sale: Up to 70% Off Everything!'
            : type === 'cta'
            ? 'Claim Offer'
            : type === 'description'
            ? 'Exclusive deals await! Limited time only - grab your favorites before they\'re gone.'
            : 'Boost your productivity and achieve your goals with our cutting-edge tools.',
          confidence: 0.88,
        },
        {
          id: `${type}-3`,
          type,
          content: type === 'headline'
            ? '🔥 Don\'t Miss Out - Sale Ends Soon!'
            : type === 'cta'
            ? 'Get Deal'
            : type === 'description'
            ? 'Last chance to save big! Our flash sale ends in just a few hours.'
            : 'Experience the difference with our premium service. Start your journey today.',
          confidence: 0.82,
        },
      ];
      
      setGeneratedContent(mockContent);
      
      toast({
        title: __('Content Generated!', 'bettercampaign'),
        description: __('AI has generated new content suggestions for you.', 'bettercampaign'),
      });
    } catch (error) {
      toast({
        title: __('Error', 'bettercampaign'),
        description: __('Failed to generate content. Please try again.', 'bettercampaign'),
        variant: 'destructive',
      });
    } finally {
      setIsGenerating(false);
    }
  };

  const handleCustomGenerate = async () => {
    if (!customPrompt.trim()) {
      toast({
        title: __('Error', 'bettercampaign'),
        description: __('Please enter a prompt to generate content.', 'bettercampaign'),
        variant: 'destructive',
      });
      return;
    }
    
    await handleGenerateContent('copy', customPrompt);
  };

  const handleCopyContent = (content: string) => {
    navigator.clipboard.writeText(content);
    toast({
      title: __('Copied!', 'bettercampaign'),
      description: __('Content copied to clipboard.', 'bettercampaign'),
    });
  };

  const handleApplyContent = (content: string, field: string) => {
    onApplyContent?.(content, field);
    toast({
      title: __('Applied!', 'bettercampaign'),
      description: __('Content has been applied to the form.', 'bettercampaign'),
    });
    // Close drawer after applying content for better UX
    setTimeout(() => {
      onClose();
    }, 1000);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex">
      {/* Backdrop */}
      <div
        className="fixed inset-0 bg-black/50 backdrop-blur-sm"
        onClick={onClose}
      />

      {/* Drawer */}
      <div className="ml-auto w-full max-w-2xl bg-white shadow-xl overflow-y-auto h-full flex flex-col">
        {/* Header */}
        <div className="sticky top-0 bg-white border-b p-4 z-10 flex-shrink-0">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-50 rounded-lg">
                <Sparkles className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <h2 className="text-lg font-semibold text-gray-900">{__('AI Content Generator', 'bettercampaign')}</h2>
                <p className="text-sm text-gray-600">
                  {__('Generate compelling content for your campaigns', 'bettercampaign')}
                </p>
              </div>
            </div>
            <Button variant="ghost" size="sm" onClick={onClose} className="hover:bg-gray-100">
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto" style={ { zIndex: 1000 }}>
          <div className="p-4 space-y-4">
            {/* Quick Generate Buttons */}
            <Card className="border border-gray-200">
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center gap-2 text-base">
                  <Wand2 className="h-4 w-4" />
                  {__('Quick Generate', 'bettercampaign')}
                </CardTitle>
                <CardDescription className="text-sm">
                  {__('Generate content for common elements', 'bettercampaign')}
                </CardDescription>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="grid grid-cols-2 gap-3">
                  <Button
                    variant="outline"
                    onClick={() => handleGenerateContent('headline')}
                    disabled={isGenerating}
                    className="h-auto p-3 flex flex-col items-center gap-2 hover:bg-blue-50 hover:border-blue-200"
                  >
                    <MessageSquare className="h-4 w-4 text-blue-600" />
                    <span className="text-xs font-medium">{__('Headlines', 'bettercampaign')}</span>
                  </Button>

                  <Button
                    variant="outline"
                    onClick={() => handleGenerateContent('cta')}
                    disabled={isGenerating}
                    className="h-auto p-3 flex flex-col items-center gap-2 hover:bg-green-50 hover:border-green-200"
                  >
                    <Target className="h-4 w-4 text-green-600" />
                    <span className="text-xs font-medium">{__('Call-to-Actions', 'bettercampaign')}</span>
                  </Button>

                  <Button
                    variant="outline"
                    onClick={() => handleGenerateContent('description')}
                    disabled={isGenerating}
                    className="h-auto p-3 flex flex-col items-center gap-2 hover:bg-purple-50 hover:border-purple-200"
                  >
                    <Zap className="h-4 w-4 text-purple-600" />
                    <span className="text-xs font-medium">{__('Descriptions', 'bettercampaign')}</span>
                  </Button>

                  <Button
                    variant="outline"
                    onClick={() => handleGenerateContent('copy')}
                    disabled={isGenerating}
                    className="h-auto p-3 flex flex-col items-center gap-2 hover:bg-orange-50 hover:border-orange-200"
                  >
                    <Sparkles className="h-4 w-4 text-orange-600" />
                    <span className="text-xs font-medium">{__('Marketing Copy', 'bettercampaign')}</span>
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Custom Prompt */}
            <Card className="border border-gray-200">
              <CardHeader className="pb-3">
                <CardTitle className="text-base">{__('Custom Prompt', 'bettercampaign')}</CardTitle>
                <CardDescription className="text-sm">
                  {__('Describe what you want to generate', 'bettercampaign')}
                </CardDescription>
              </CardHeader>
              <CardContent className="pt-0 space-y-3">
                <Textarea
                  placeholder={__('e.g., Create a compelling headline for a summer sale campaign targeting young professionals...', 'bettercampaign')}
                  value={customPrompt}
                  onChange={(e) => setCustomPrompt(e.target.value)}
                  className="min-h-[80px] text-sm resize-none border-gray-200 focus:border-blue-500 focus:ring-blue-500"
                />
                <Button
                  onClick={handleCustomGenerate}
                  disabled={isGenerating || !customPrompt.trim()}
                  className="w-full bg-blue-600 hover:bg-blue-700 text-white"
                  size="sm"
                >
                  {isGenerating ? (
                    <LoadingSpinner size="sm" text={__('Generating...', 'bettercampaign')} />
                  ) : (
                    <>
                      <Sparkles className="h-4 w-4 mr-2" />
                      {__('Generate Content', 'bettercampaign')}
                    </>
                  )}
                </Button>
              </CardContent>
            </Card>

            {/* Generated Content */}
            {generatedContent.length > 0 && (
              <Card className="border border-gray-200">
                <CardHeader className="pb-3">
                  <CardTitle className="flex items-center justify-between text-base">
                    {__('Generated Content', 'bettercampaign')}
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setGeneratedContent([])}
                      className="text-xs"
                    >
                      <RefreshCw className="h-3 w-3 mr-1" />
                      {__('Clear', 'bettercampaign')}
                    </Button>
                  </CardTitle>
                </CardHeader>
                <CardContent className="pt-0 space-y-3">
                  {generatedContent.map((item) => (
                    <div key={item.id} className="border border-gray-100 rounded-lg p-3 space-y-3 bg-gray-50/50">
                      <div className="flex items-center justify-between">
                        <Badge
                          variant="secondary"
                          className={`capitalize text-xs ${
                            item.type === 'headline' ? 'bg-blue-100 text-blue-800' :
                            item.type === 'cta' ? 'bg-green-100 text-green-800' :
                            item.type === 'description' ? 'bg-purple-100 text-purple-800' :
                            'bg-orange-100 text-orange-800'
                          }`}
                        >
                          {item.type}
                        </Badge>
                        <div className="flex items-center gap-1">
                          <span className="text-xs text-gray-500">
                            {Math.round(item.confidence * 100)}% confidence
                          </span>
                        </div>
                      </div>

                      <p className="text-sm leading-relaxed text-gray-800 bg-white p-2 rounded border">{item.content}</p>

                      <div className="flex gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleCopyContent(item.content)}
                          className="text-xs hover:bg-gray-100"
                        >
                          <Copy className="h-3 w-3 mr-1" />
                          {__('Copy', 'bettercampaign')}
                        </Button>

                        <Button
                          variant="default"
                          size="sm"
                          onClick={() => handleApplyContent(item.content, item.type)}
                          className="text-xs bg-blue-600 hover:bg-blue-700 text-white"
                        >
                          <Target className="h-3 w-3 mr-1" />
                          {__('Use This', 'bettercampaign')}
                        </Button>
                      </div>
                    </div>
                  ))}
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};
