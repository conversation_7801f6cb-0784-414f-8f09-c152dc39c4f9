import { __ } from '@wordpress/i18n';
import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/bettercampaign/components/ui/card';
import { Button } from '@/bettercampaign/components/ui/button';
// Removed Progress import as requested
import { Badge } from '@/bettercampaign/components/ui/badge';
import { Form } from '@/bettercampaign/components/ui/form';
import { useToast } from '@/bettercampaign/components/ui/use-toast';
import { 
  completeCampaignSchema, 
  campaignOverviewSchema,
  featureSelectionSchema,
  notificationBarSchema,
  exitIntentPopupSchema,
  shortLinkSchema,
  priceTrackingSchema,
  aiContentGenerationSchema,
  type CompleteCampaign,
  type CampaignOverview,
  type FeatureSelection,
  type NotificationBar,
  type ShortLink,
  type PriceTracking,
  type AIContentGeneration
} from '@/bettercampaign/lib/schemas';
import { CampaignOverviewStep } from './CampaignOverviewStep';
import { FeatureSelectionStep } from './FeatureSelectionStep';
import { NotificationBarStep } from './NotificationBarStep';
import { ExitIntentPopupStep } from './ExitIntentPopupStep';
import { ShortLinksStep } from './ShortLinksStep';
import { PriceTrackingStep } from './PriceTrackingStep';
import { AIContentGenerationStep } from './AIContentGenerationStep';
import { 
  CheckCircle2, 
  Circle, 
  ArrowLeft, 
  ArrowRight, 
  Save,
  Sparkles
} from 'lucide-react';
import apiFetch from '@wordpress/api-fetch';

interface CampaignCreationWizardProps {
  onSuccess?: (campaign: any) => void;
  onCancel?: () => void;
}

interface Step {
  id: string;
  title: string;
  description: string;
  component: React.ComponentType<any>;
  schema: any;
  isOptional?: boolean;
  dependsOn?: string[];
}

const baseSteps: Step[] = [
  {
    id: 'overview',
    title: __('Campaign Overview', 'bettercampaign'),
    description: __('Basic campaign information', 'bettercampaign'),
    component: CampaignOverviewStep,
    schema: campaignOverviewSchema,
  },
  {
    id: 'features',
    title: __('Select Features', 'bettercampaign'),
    description: __('Choose campaign features', 'bettercampaign'),
    component: FeatureSelectionStep,
    schema: featureSelectionSchema,
  },
];

const featureSteps: Record<string, Step> = {
  notification_bar: {
    id: 'notification_bar',
    title: __('Notification Bar', 'bettercampaign'),
    description: __('Configure notification bar', 'bettercampaign'),
    component: NotificationBarStep,
    schema: notificationBarSchema,
    isOptional: true,
    dependsOn: ['notification_bar'],
  },
  exit_intent_popup: {
    id: 'exit_intent_popup',
    title: __('Exit Intent Popup', 'bettercampaign'),
    description: __('Configure exit intent popup', 'bettercampaign'),
    component: ExitIntentPopupStep,
    schema: exitIntentPopupSchema,
    isOptional: true,
    dependsOn: ['exit_intent_popup'],
  },
  short_links: {
    id: 'short_links',
    title: __('Short Links', 'bettercampaign'),
    description: __('Configure short links', 'bettercampaign'),
    component: ShortLinksStep,
    schema: shortLinkSchema,
    isOptional: true,
    dependsOn: ['short_links'],
  },
  price_tracking: {
    id: 'price_tracking',
    title: __('Price Tracking', 'bettercampaign'),
    description: __('Configure price tracking', 'bettercampaign'),
    component: PriceTrackingStep,
    schema: priceTrackingSchema,
    isOptional: true,
    dependsOn: ['price_tracking'],
  },
  ai_content_generation: {
    id: 'ai_content_generation',
    title: __('AI Content', 'bettercampaign'),
    description: __('Configure AI content generation', 'bettercampaign'),
    component: AIContentGenerationStep,
    schema: aiContentGenerationSchema,
    isOptional: true,
    dependsOn: ['ai_content_generation'],
  },
};

export const CampaignCreationWizard: React.FC<CampaignCreationWizardProps> = ({ 
  onSuccess, 
  onCancel 
}) => {
  const [currentStepIndex, setCurrentStepIndex] = useState(0);
  const [completedSteps, setCompletedSteps] = useState<Set<string>>(new Set());
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();

  // Main form for the complete campaign
  const mainForm = useForm<CompleteCampaign>({
    resolver: zodResolver(completeCampaignSchema),
    defaultValues: {
      overview: {
        name: '',
        type: undefined,
        dateRange: undefined,
      },
      features: {
        features: [],
      },
    },
  });

  // Individual forms for each step
  const overviewForm = useForm<CampaignOverview>({
    resolver: zodResolver(campaignOverviewSchema),
    defaultValues: {
      name: '',
      type: undefined,
      dateRange: undefined,
    },
  });

  const featuresForm = useForm<FeatureSelection>({
    resolver: zodResolver(featureSelectionSchema),
    defaultValues: mainForm.getValues().features,
  });

  const notificationBarForm = useForm<NotificationBar>({
    resolver: zodResolver(notificationBarSchema),
    defaultValues: {
      headline: '',
      subheadline: '',
      countdownToggle: false,
      ctaText: '',
      ctaUrl: '',
      placement: 'top',
      backgroundImage: '',
      isGutenberg: false,
      gutenbergPatternId: '',
      gutenbergContent: '',
    },
  });

  const exitIntentForm = useForm({
    resolver: zodResolver(exitIntentPopupSchema),
    defaultValues: {
      timerHours: 24,
      heading: '',
      buttonText: '',
      buttonUrl: '',
      offerImage: '',
      backgroundImage: '',
    },
  });

  const shortLinkForm = useForm<ShortLink>({
    resolver: zodResolver(shortLinkSchema),
    defaultValues: {
      longUrl: '',
      customSlug: '',
      utmSource: '',
      utmMedium: '',
      utmCampaign: '',
    },
  });

  const priceTrackingForm = useForm<PriceTracking>({
    resolver: zodResolver(priceTrackingSchema),
    defaultValues: {
      productType: [],
      productName: '',
      originalPrice: 0,
      discountedPrice: 0,
      showStrikethrough: true,
      showPercentageBadge: true,
      currency: 'USD',
    },
  });

  const aiContentForm = useForm<AIContentGeneration>({
    resolver: zodResolver(aiContentGenerationSchema),
    defaultValues: {
      productName: '',
      offerType: undefined,
      campaignTone: undefined,
      ctaFocus: undefined,
      outputTypes: [],
    },
  });

  // Get form for current step
  const getFormForStep = (stepId: string) => {
    switch (stepId) {
      case 'overview': return overviewForm;
      case 'features': return featuresForm;
      case 'notification_bar': return notificationBarForm;
      case 'exit_intent_popup': return exitIntentForm;
      case 'short_links': return shortLinkForm;
      case 'price_tracking': return priceTrackingForm;
      case 'ai_content_generation': return aiContentForm;
      default: return overviewForm;
    }
  };

  // Generate steps based on selected features
  const selectedFeatures = featuresForm.watch('features') || [];
  const steps = [
    ...baseSteps,
    ...selectedFeatures.map(feature => featureSteps[feature]).filter(Boolean),
  ];

  const currentStep = steps[currentStepIndex];
  const currentForm = getFormForStep(currentStep.id);
  const StepComponent = currentStep.component;
  const isLastStep = currentStepIndex === steps.length - 1;
  const hasNextStep = !isLastStep;

  // Removed progress calculation as requested

  const validateCurrentStep = async () => {
    const isValid = await currentForm.trigger();
    if (isValid) {
      setCompletedSteps(prev => new Set(Array.from(prev).concat([currentStep.id])));
      
      // Update main form with current step data
      if (currentStep.id === 'overview') {
        mainForm.setValue('overview', overviewForm.getValues());
      } else if (currentStep.id === 'features') {
        mainForm.setValue('features', featuresForm.getValues());
      } else {
        // Update optional feature data
        mainForm.setValue(currentStep.id as any, currentForm.getValues());
      }
    }
    return isValid;
  };

  const handleNext = async () => {
    const isValid = await validateCurrentStep();
    if (isValid && currentStepIndex < steps.length - 1) {
      setCurrentStepIndex(currentStepIndex + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStepIndex > 0) {
      setCurrentStepIndex(currentStepIndex - 1);
    }
  };

  const handleStepClick = async (stepIndex: number) => {
    if (stepIndex <= currentStepIndex) {
      setCurrentStepIndex(stepIndex);
    }
  };

  const handleStepComplete = (stepId: string, data: any) => {
    // Mark step as completed
    setCompletedSteps(prev => {
      const newSet = new Set(prev);
      newSet.add(stepId);
      return newSet;
    });

    // Store step data if needed
    console.log(`Step ${stepId} completed with data:`, data);
  };

  const handleSubmit = async () => {
    const isValid = await validateCurrentStep();
    if (!isValid) return;

    setIsSubmitting(true);
    try {
      const campaignData = mainForm.getValues();
      
      // Transform data for API
      const apiData = {
        title: campaignData.overview.name,
        prompt: `Campaign: ${campaignData.overview.name} - ${campaignData.overview.type} campaign from ${campaignData.overview.dateRange?.from?.toLocaleDateString() || 'TBD'} to ${campaignData.overview.dateRange?.to?.toLocaleDateString() || 'TBD'}`,
        permissions: selectedFeatures,
        status: 'draft',
        campaign_data: campaignData,
      };

      const response = await apiFetch({
        path: '/bettercampaign/v1/campaigns',
        method: 'POST',
        data: apiData,
      });

      toast({
        title: __('Success!', 'bettercampaign'),
        description: __('Campaign created successfully.', 'bettercampaign'),
      });

      if (onSuccess) {
        onSuccess(response);
      }
    } catch (error) {
      console.error('Error creating campaign:', error);
      toast({
        title: __('Error', 'bettercampaign'),
        description: __('Failed to create campaign. Please try again.', 'bettercampaign'),
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="flex-shrink-0 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="p-6 pb-4">
          <div className="flex items-center justify-between mb-4">
            <div>
              <h1 className="text-xl font-semibold flex items-center gap-2">
                <Sparkles className="h-5 w-5" />
                {__('Create New Campaign', 'bettercampaign')}
              </h1>
              <p className="text-sm text-muted-foreground">
                {__('Step', 'bettercampaign')} {currentStepIndex + 1} {__('of', 'bettercampaign')} {steps.length}: {currentStep.title}
              </p>
            </div>
            {onCancel && (
              <Button variant="outline" onClick={onCancel}>
                {__('Cancel', 'bettercampaign')}
              </Button>
            )}
          </div>
          {/* Progress bar removed as requested */}
        </div>
      </div>

      {/* Step Navigation */}
      <div className="flex-shrink-0 border-b">
        <div className="p-4">
          <div className="flex flex-wrap gap-2">
            {steps.map((step, index) => {
              const isCompleted = completedSteps.has(step.id);
              const isCurrent = index === currentStepIndex;
              const isAccessible = index <= currentStepIndex;

              return (
                <button
                  key={step.id}
                  onClick={() => handleStepClick(index)}
                  disabled={!isAccessible}
                  className={`flex items-center gap-2 px-3 py-2 rounded-lg text-sm transition-colors ${
                    isCurrent
                      ? 'bg-primary text-primary-foreground'
                      : isCompleted
                      ? 'bg-green-100 text-green-800 hover:bg-green-200'
                      : isAccessible
                      ? 'bg-muted hover:bg-muted/80'
                      : 'bg-muted/50 text-muted-foreground cursor-not-allowed'
                  }`}
                >
                  {isCompleted ? (
                    <CheckCircle2 className="h-4 w-4" />
                  ) : (
                    <Circle className="h-4 w-4" />
                  )}
                  <span className="hidden sm:inline">{step.title}</span>
                  <span className="sm:hidden">{index + 1}</span>
                  {step.isOptional && (
                    <Badge variant="secondary" className="text-xs hidden sm:inline-flex">
                      {__('Optional', 'bettercampaign')}
                    </Badge>
                  )}
                </button>
              );
            })}
          </div>
        </div>
      </div>

      {/* Current Step Content */}
      <div className="flex-1 overflow-y-auto">
        <div className="p-6">
          <StepComponent
            form={currentForm}
            onComplete={(data: any) => handleStepComplete(currentStep.id, data)}
            onNext={handleNext}
            hasNextStep={hasNextStep}
            isInWizard={true}
            campaignData={currentStep.id === 'short_links' ? {
              title: overviewForm.getValues('name'),
              description: overviewForm.getValues('type'),
              features: featuresForm.getValues('features'),
            } : undefined}
          />
        </div>
      </div>

      {/* Navigation Buttons */}
      <div className="flex-shrink-0 border-t bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="p-6">
          <div className="flex justify-between">
            <Button
              variant="outline"
              onClick={handlePrevious}
              disabled={currentStepIndex === 0}
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              {__('Previous', 'bettercampaign')}
            </Button>

            <div className="flex gap-2">
              {isLastStep ? (
                <Button
                  onClick={handleSubmit}
                  disabled={isSubmitting}
                  className="min-w-[120px]"
                >
                  {isSubmitting ? (
                    <>
                      <Save className="h-4 w-4 mr-2 animate-spin" />
                      {__('Creating...', 'bettercampaign')}
                    </>
                  ) : (
                    <>
                      <Save className="h-4 w-4 mr-2" />
                      {__('Create Campaign', 'bettercampaign')}
                    </>
                  )}
                </Button>
              ) : (
                <Button onClick={handleNext}>
                  {__('Next', 'bettercampaign')}
                  <ArrowRight className="h-4 w-4 ml-2" />
                </Button>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
