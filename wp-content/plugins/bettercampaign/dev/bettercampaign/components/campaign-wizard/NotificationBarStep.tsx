import { __ } from '@wordpress/i18n';
import React, { useState } from 'react';
import { UseFormReturn } from 'react-hook-form';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/bettercampaign/components/ui/card';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/bettercampaign/components/ui/form';
import { Input } from '@/bettercampaign/components/ui/input';

import { ImageUploadWithAI } from '@/bettercampaign/components/ui/image-upload-with-ai';
import { SuccessDrawer } from '@/bettercampaign/components/ui/success-drawer';
import { AIHeadlinePopover } from '@/bettercampaign/components/ui/ai-headline-popover';
import { AICTAPopover } from '@/bettercampaign/components/ui/ai-cta-popover';
import { Button } from '@/bettercampaign/components/ui/button';
import { useToast } from '@/bettercampaign/components/ui/use-toast';
import { LoadingSpinner } from '@/bettercampaign/components/ui/loading-spinner';
import { NotificationBar } from '@/bettercampaign/lib/schemas';
import { Bell, Plus } from 'lucide-react';

interface NotificationBarStepProps {
  form: UseFormReturn<NotificationBar>;
}

export const NotificationBarStep: React.FC<NotificationBarStepProps> = ({ form }) => {
  const [showSuccessDrawer, setShowSuccessDrawer] = useState(false);
  const [isCreatingNotification, setIsCreatingNotification] = useState(false);
  const [creationResult, setCreationResult] = useState<{
    notification_bar: { post_id: number; edit_url: string };
    campaign: { id: number; preview_url: string };
  } | null>(null);
  const { toast } = useToast();

  const handleCreateNotification = async () => {
    const formData = form.getValues();

    // Validate required fields
    if (!formData.headline || !formData.ctaText || !formData.ctaUrl) {
      toast({
        title: __('Validation Error', 'bettercampaign'),
        description: __('Please fill in all required fields before creating the notification.', 'bettercampaign'),
        variant: 'destructive',
      });
      return;
    }

    setIsCreatingNotification(true);

    try {
      // API call to create notification in {prefix}_nx_posts table
      const response = await fetch('/wp-admin/admin-ajax.php', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
          action: 'create_notification_bar',
          nonce: (window as any).betterCampaignAdmin?.nonce || '',
          title: formData.headline,
          headline: formData.headline,
          cta_text: formData.ctaText,
          cta_url: formData.ctaUrl,
          background_image: formData.backgroundImage || '',
          placement: formData.placement,
          countdown_enabled: formData.countdownToggle ? '1' : '0',
          end_date: formData.endDate ? formData.endDate.toISOString() : '',
        }),
      });

      const result = await response.json();

      if (result.success) {
        // Store the created notification ID in the form (using gutenbergPatternId as storage)
        form.setValue('gutenbergPatternId', result.data.post_id.toString());

        // Store the result and show success drawer
        setCreationResult({
          notification_bar: {
            post_id: result.data.post_id,
            edit_url: result.data.edit_url,
          },
          campaign: {
            id: result.data.post_id,
            preview_url: result.data.preview_url || '#',
          },
        });
        setShowSuccessDrawer(true);

        toast({
          title: __('Success!', 'bettercampaign'),
          description: __('Notification bar created successfully!', 'bettercampaign'),
        });
      } else {
        throw new Error(result.message || __('Failed to create notification', 'bettercampaign'));
      }
    } catch (error) {
      console.error('Error creating notification:', error);
      toast({
        title: __('Error', 'bettercampaign'),
        description: error instanceof Error ? error.message : __('Failed to create notification. Please try again.', 'bettercampaign'),
        variant: 'destructive',
      });
    } finally {
      setIsCreatingNotification(false);
    }
  };

  const handleAIContentApply = (content: string, field: string) => {
    // Map AI content types to form fields
    const fieldMapping: Record<string, string> = {
      headline: 'headline',
      cta: 'ctaText',
      description: 'headline', // fallback to headline
      copy: 'headline', // fallback to headline
    };

    const targetField = fieldMapping[field] || field;
    form.setValue(targetField as any, content);

    toast({
      title: __('Applied!', 'bettercampaign'),
      description: __('AI content has been applied to the form.', 'bettercampaign'),
    });
  };



  return (
    <>
      <Form {...form}>
        <div className="max-w-4xl mx-auto">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Bell className="h-5 w-5" />
                {__('Notification Bar Configuration', 'bettercampaign')}
              </CardTitle>
              <CardDescription>
                {__('Configure your notification bar settings and content', 'bettercampaign')}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
                {/* Headline */}
                <FormField
                  control={form.control}
                  name="headline"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="flex items-center justify-between">
                        {__('Headline', 'bettercampaign')}
                        <AIHeadlinePopover onApplyContent={handleAIContentApply} />
                      </FormLabel>
                      <FormControl>
                        <Input
                          placeholder={__('🎉 Limited Time Offer - Save 50% Today!', 'bettercampaign')}
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        {__('The main message that will grab attention', 'bettercampaign')}
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* CTA Text */}
                  <FormField
                    control={form.control}
                    name="ctaText"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="flex items-center justify-between">
                          {__('Call-to-Action Text', 'bettercampaign')}
                          <AICTAPopover onApplyContent={handleAIContentApply} />
                        </FormLabel>
                        <FormControl>
                          <Input
                            placeholder={__('Shop Now', 'bettercampaign')}
                            {...field}
                          />
                        </FormControl>
                        <FormDescription>
                          {__('Text for the action button', 'bettercampaign')}
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* CTA URL */}
                  <FormField
                    control={form.control}
                    name="ctaUrl"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{__('Call-to-Action URL', 'bettercampaign')}</FormLabel>
                        <FormControl>
                          <Input 
                            type="url"
                            placeholder={__('https://example.com/sale', 'bettercampaign')} 
                            {...field} 
                          />
                        </FormControl>
                        <FormDescription>
                          {__('Where users will be directed when they click', 'bettercampaign')}
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                {/* Background Image - Only show if not using Gutenberg */}
                {!form.watch('isGutenberg') && (
                  <FormField
                    control={form.control}
                    name="backgroundImage"
                    render={({ field }) => (
                      <FormItem>
                        <ImageUploadWithAI
                          label={__('Background Image (Optional)', 'bettercampaign')}
                          description={__('Upload or generate a background image for the notification bar', 'bettercampaign')}
                          value={field.value || ''}
                          onChange={field.onChange}
                          onAIGenerate={(prompt) => {
                            // TODO: Implement AI image generation
                            console.log('Generate image with prompt:', prompt);
                          }}
                        />
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                )}

                {/* Create Notification Button - Bottom of Form */}
                <div className="pt-6 border-t">
                  <Button
                    type="button"
                    onClick={handleCreateNotification}
                    disabled={isCreatingNotification}
                    className="w-full gap-2"
                    size="lg"
                  >
                    {isCreatingNotification ? (
                      <LoadingSpinner size="sm" text={__('Creating...', 'bettercampaign')} />
                    ) : (
                      <>
                        <Plus className="h-4 w-4" />
                        {__('Create Notification', 'bettercampaign')}
                      </>
                    )}
                  </Button>
                </div>
            </CardContent>
          </Card>
        </div>
      </Form>

      {/* Success Drawer */}
      {creationResult && (
        <SuccessDrawer
          isOpen={showSuccessDrawer}
          onClose={() => setShowSuccessDrawer(false)}
          notificationBar={creationResult.notification_bar}
          campaign={creationResult.campaign}
        />
      )}
    </>
  );
};
