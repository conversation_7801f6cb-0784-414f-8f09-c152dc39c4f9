import { __ } from '@wordpress/i18n';
import React, { useState } from 'react';
import { UseFormReturn } from 'react-hook-form';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/bettercampaign/components/ui/card';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/bettercampaign/components/ui/form';
import { Input } from '@/bettercampaign/components/ui/input';
import { Textarea } from '@/bettercampaign/components/ui/textarea';
import { Switch } from '@/bettercampaign/components/ui/switch';
import { RadioGroup, RadioGroupItem } from '@/bettercampaign/components/ui/radio-group';
import { Label } from '@/bettercampaign/components/ui/label';
import { DatePicker } from '@/bettercampaign/components/ui/date-picker';
import { AIAssistance } from '@/bettercampaign/components/ui/ai-assistance';
import { ImageUploadWithAI } from '@/bettercampaign/components/ui/image-upload-with-ai';
import { SuccessDrawer } from '@/bettercampaign/components/ui/success-drawer';
import { AIContentDrawer } from '@/bettercampaign/components/ui/ai-content-drawer';
import { Button } from '@/bettercampaign/components/ui/button';
import { useToast } from '@/bettercampaign/components/ui/use-toast';
import { LoadingSpinner } from '@/bettercampaign/components/ui/loading-spinner';
import { NotificationBar } from '@/bettercampaign/lib/schemas';
import { Bell, ArrowUp, ArrowDown, Plus, Sparkles } from 'lucide-react';

interface NotificationBarStepProps {
  form: UseFormReturn<NotificationBar>;
}

export const NotificationBarStep: React.FC<NotificationBarStepProps> = ({ form }) => {
  const [aiGenerating, setAiGenerating] = useState(false);
  const [showSuccessDrawer, setShowSuccessDrawer] = useState(false);
  const [showAIDrawer, setShowAIDrawer] = useState(false);
  const [isCreatingNotification, setIsCreatingNotification] = useState(false);
  const [creationResult, setCreationResult] = useState<{
    notification_bar: { post_id: number; edit_url: string };
    campaign: { id: number; preview_url: string };
  } | null>(null);
  const { toast } = useToast();

  const handleAIGenerate = () => {
    setAiGenerating(true);
    // TODO: Implement AI generation logic
    setTimeout(() => {
      setAiGenerating(false);
    }, 2000);
  };

  const handleAIApply = (content: string, field: string) => {
    form.setValue(field as any, content);
  };

  const handleCreateNotification = async () => {
    const formData = form.getValues();

    // Validate required fields
    if (!formData.headline || !formData.ctaText || !formData.ctaUrl) {
      toast({
        title: __('Validation Error', 'bettercampaign'),
        description: __('Please fill in all required fields before creating the notification.', 'bettercampaign'),
        variant: 'destructive',
      });
      return;
    }

    setIsCreatingNotification(true);

    try {
      // API call to create notification in {prefix}_nx_posts table
      const response = await fetch('/wp-admin/admin-ajax.php', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
          action: 'create_notification_bar',
          nonce: (window as any).betterCampaignAdmin?.nonce || '',
          title: formData.headline,
          headline: formData.headline,
          cta_text: formData.ctaText,
          cta_url: formData.ctaUrl,
          background_image: formData.backgroundImage || '',
          placement: formData.placement,
          countdown_enabled: formData.countdownToggle ? '1' : '0',
          end_date: formData.endDate ? formData.endDate.toISOString() : '',
        }),
      });

      const result = await response.json();

      if (result.success) {
        // Store the created notification ID in the form (using gutenbergPatternId as storage)
        form.setValue('gutenbergPatternId', result.data.post_id.toString());

        // Store the result and show success drawer
        setCreationResult({
          notification_bar: {
            post_id: result.data.post_id,
            edit_url: result.data.edit_url,
          },
          campaign: {
            id: result.data.post_id,
            preview_url: result.data.preview_url || '#',
          },
        });
        setShowSuccessDrawer(true);

        toast({
          title: __('Success!', 'bettercampaign'),
          description: __('Notification bar created successfully!', 'bettercampaign'),
        });
      } else {
        throw new Error(result.message || __('Failed to create notification', 'bettercampaign'));
      }
    } catch (error) {
      console.error('Error creating notification:', error);
      toast({
        title: __('Error', 'bettercampaign'),
        description: error instanceof Error ? error.message : __('Failed to create notification. Please try again.', 'bettercampaign'),
        variant: 'destructive',
      });
    } finally {
      setIsCreatingNotification(false);
    }
  };

  const handleAIContentApply = (content: string, field: string) => {
    // Map AI content types to form fields
    const fieldMapping: Record<string, string> = {
      headline: 'headline',
      cta: 'ctaText',
      description: 'headline', // fallback to headline
      copy: 'headline', // fallback to headline
    };

    const targetField = fieldMapping[field] || field;
    form.setValue(targetField as any, content);

    toast({
      title: __('Applied!', 'bettercampaign'),
      description: __('AI content has been applied to the form.', 'bettercampaign'),
    });
  };

  const handleCreateGutenbergNotification = async () => {
    const formData = form.getValues();

    // Validate required fields
    if (!formData.headline || !formData.ctaText || !formData.ctaUrl) {
      alert(__('Please fill in all required fields before creating the notification bar.', 'bettercampaign'));
      return;
    }

    setAiGenerating(true);

    try {
      // Create the notification bar post programmatically
      const response = await fetch('/wp-admin/admin-ajax.php', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
          action: 'create_gutenberg_notification_bar',
          nonce: (window as any).betterCampaignAdmin?.nonce || '',
          title: formData.headline,
          cta_text: formData.ctaText,
          cta_url: formData.ctaUrl,
          image_url: formData.backgroundImage || '',
          placement: formData.placement,
        }),
      });

      const result = await response.json();

      if (result.success) {
        // Store the created post ID in the form
        form.setValue('gutenbergPatternId', result.data.notification_bar.post_id.toString());
        form.setValue('gutenbergContent', result.data.notification_bar.content);

        // Store the result and show success drawer
        setCreationResult(result.data);
        setShowSuccessDrawer(true);
      } else {
        alert(__('Error creating notification bar: ', 'bettercampaign') + result.data);
      }
    } catch (error) {
      console.error('Error creating notification bar:', error);
      alert(__('Error creating notification bar. Please try again.', 'bettercampaign'));
    } finally {
      setAiGenerating(false);
    }
  };

  return (
    <Form {...form}>
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2 top-6">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <Bell className="h-5 w-5" />
                    {__('Notification Bar Configuration', 'bettercampaign')}
                  </CardTitle>
                  <CardDescription>
                    {__('Configure your notification bar settings and content', 'bettercampaign')}
                  </CardDescription>
                </div>
                <Button
                  type="button"
                  onClick={handleCreateNotification}
                  disabled={isCreatingNotification}
                  className="gap-2"
                >
                  {isCreatingNotification ? (
                    <LoadingSpinner size="sm" text={__('Creating...', 'bettercampaign')} />
                  ) : (
                    <>
                      <Plus className="h-4 w-4" />
                      {__('Create Notification', 'bettercampaign')}
                    </>
                  )}
                </Button>
              </div>
            </CardHeader>
            <CardContent className="space-y-6">
                {/* Headline */}
                <FormField
                  control={form.control}
                  name="headline"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{__('Headline', 'bettercampaign')}</FormLabel>
                      <FormControl>
                        <Input 
                          placeholder={__('🎉 Limited Time Offer - Save 50% Today!', 'bettercampaign')} 
                          {...field} 
                        />
                      </FormControl>
                      <FormDescription>
                        {__('The main message that will grab attention', 'bettercampaign')}
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* CTA Text */}
                  <FormField
                    control={form.control}
                    name="ctaText"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{__('Call-to-Action Text', 'bettercampaign')}</FormLabel>
                        <FormControl>
                          <Input 
                            placeholder={__('Shop Now', 'bettercampaign')} 
                            {...field} 
                          />
                        </FormControl>
                        <FormDescription>
                          {__('Text for the action button', 'bettercampaign')}
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* CTA URL */}
                  <FormField
                    control={form.control}
                    name="ctaUrl"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{__('Call-to-Action URL', 'bettercampaign')}</FormLabel>
                        <FormControl>
                          <Input 
                            type="url"
                            placeholder={__('https://example.com/sale', 'bettercampaign')} 
                            {...field} 
                          />
                        </FormControl>
                        <FormDescription>
                          {__('Where users will be directed when they click', 'bettercampaign')}
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                {/* Background Image - Only show if not using Gutenberg */}
                {!form.watch('isGutenberg') && (
                  <FormField
                    control={form.control}
                    name="backgroundImage"
                    render={({ field }) => (
                      <FormItem>
                        <ImageUploadWithAI
                          label={__('Background Image (Optional)', 'bettercampaign')}
                          description={__('Upload or generate a background image for the notification bar', 'bettercampaign')}
                          value={field.value || ''}
                          onChange={field.onChange}
                          onAIGenerate={(prompt) => {
                            // TODO: Implement AI image generation
                            console.log('Generate image with prompt:', prompt);
                          }}
                        />
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                )}
            </CardContent>
          </Card>
        </div>

        {/* AI Assistance Sidebar - Optimized Full Width */}
        <div className="space-y-4 sticky top-6">
          {/* Generate with AI Button */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Sparkles className="h-5 w-5" />
                {__('AI Content Assistant', 'bettercampaign')}
              </CardTitle>
              <CardDescription>
                {__('Generate compelling content for your notification bar', 'bettercampaign')}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button
                type="button"
                onClick={() => setShowAIDrawer(true)}
                className="w-full gap-2"
                variant="outline"
              >
                <Sparkles className="h-4 w-4" />
                {__('Generate with AI', 'bettercampaign')}
              </Button>
            </CardContent>
          </Card>

          {/* Full-width AI Assistance Components */}
          <div className="space-y-4">
            <AIAssistance
              title={__('Headline Generator', 'bettercampaign')}
              description={__('Generate compelling headlines for your notification bar', 'bettercampaign')}
              onGenerate={handleAIGenerate}
              onApply={(content) => handleAIApply(content, 'headline')}
              isGenerating={aiGenerating}
              suggestions={[
                __('🎉 Limited Time Offer - Save 50% Today!', 'bettercampaign'),
                __('⚡ Flash Sale: Up to 70% Off Everything!', 'bettercampaign'),
                __('🔥 Don\'t Miss Out - Sale Ends Soon!', 'bettercampaign'),
              ]}
              className="w-full"
            />

            <AIAssistance
              title={__('CTA Generator', 'bettercampaign')}
              description={__('Create effective call-to-action buttons', 'bettercampaign')}
              onGenerate={handleAIGenerate}
              onApply={(content) => handleAIApply(content, 'ctaText')}
              isGenerating={aiGenerating}
              suggestions={[
                __('Shop Now', 'bettercampaign'),
                __('Claim Offer', 'bettercampaign'),
                __('Get Deal', 'bettercampaign'),
                __('Save Today', 'bettercampaign'),
              ]}
              className="w-full"
            />
          </div>
        </div>
      </div>

      {/* Success Drawer */}
      {creationResult && (
        <SuccessDrawer
          isOpen={showSuccessDrawer}
          onClose={() => setShowSuccessDrawer(false)}
          notificationBar={creationResult.notification_bar}
          campaign={creationResult.campaign}
        />
      )}

      {/* AI Content Drawer */}
      <AIContentDrawer
        isOpen={showAIDrawer}
        onClose={() => setShowAIDrawer(false)}
        context={{
          type: 'notification',
          data: form.getValues(),
        }}
        onApplyContent={handleAIContentApply}
      />
    </Form>
  );
};
