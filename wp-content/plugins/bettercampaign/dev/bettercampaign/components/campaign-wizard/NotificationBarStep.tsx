import { __ } from '@wordpress/i18n';
import React, { useState } from 'react';
import { UseFormReturn } from 'react-hook-form';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/bettercampaign/components/ui/card';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/bettercampaign/components/ui/form';
import { Input } from '@/bettercampaign/components/ui/input';
import { Switch } from '@/bettercampaign/components/ui/switch';
import { RadioGroup, RadioGroupItem } from '@/bettercampaign/components/ui/radio-group';
import { Label } from '@/bettercampaign/components/ui/label';
import { DatePicker } from '@/bettercampaign/components/ui/date-picker';
import { ImageUploadWithAI } from '@/bettercampaign/components/ui/image-upload-with-ai';
import { SuccessDrawer } from '@/bettercampaign/components/ui/success-drawer';
import { AIContentDrawer } from '@/bettercampaign/components/ui/ai-content-drawer';
import { Button } from '@/bettercampaign/components/ui/button';
import { useToast } from '@/bettercampaign/components/ui/use-toast';
import { LoadingSpinner } from '@/bettercampaign/components/ui/loading-spinner';
import { NotificationBar } from '@/bettercampaign/lib/schemas';
import { Bell, ArrowUp, ArrowDown, Plus, Sparkles } from 'lucide-react';

interface NotificationBarStepProps {
  form: UseFormReturn<NotificationBar>;
}

export const NotificationBarStep: React.FC<NotificationBarStepProps> = ({ form }) => {
  const [showSuccessDrawer, setShowSuccessDrawer] = useState(false);
  const [showAIDrawer, setShowAIDrawer] = useState(false);
  const [isCreatingNotification, setIsCreatingNotification] = useState(false);
  const [creationResult, setCreationResult] = useState<{
    notification_bar: { post_id: number; edit_url: string };
    campaign: { id: number; preview_url: string };
  } | null>(null);
  const { toast } = useToast();

  const handleCreateNotification = async () => {
    const formData = form.getValues();

    // Validate required fields
    if (!formData.headline || !formData.ctaText || !formData.ctaUrl) {
      toast({
        title: __('Validation Error', 'bettercampaign'),
        description: __('Please fill in all required fields before creating the notification.', 'bettercampaign'),
        variant: 'destructive',
      });
      return;
    }

    setIsCreatingNotification(true);

    try {
      // API call to create notification in {prefix}_nx_posts table
      const response = await fetch('/wp-admin/admin-ajax.php', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
          action: 'create_notification_bar',
          nonce: (window as any).betterCampaignAdmin?.nonce || '',
          title: formData.headline,
          headline: formData.headline,
          cta_text: formData.ctaText,
          cta_url: formData.ctaUrl,
          background_image: formData.backgroundImage || '',
          placement: formData.placement,
          countdown_enabled: formData.countdownToggle ? '1' : '0',
          end_date: formData.endDate ? formData.endDate.toISOString() : '',
        }),
      });

      const result = await response.json();

      if (result.success) {
        // Store the created notification ID in the form (using gutenbergPatternId as storage)
        form.setValue('gutenbergPatternId', result.data.post_id.toString());

        // Store the result and show success drawer
        setCreationResult({
          notification_bar: {
            post_id: result.data.post_id,
            edit_url: result.data.edit_url,
          },
          campaign: {
            id: result.data.post_id,
            preview_url: result.data.preview_url || '#',
          },
        });
        setShowSuccessDrawer(true);

        toast({
          title: __('Success!', 'bettercampaign'),
          description: __('Notification bar created successfully!', 'bettercampaign'),
        });
      } else {
        throw new Error(result.message || __('Failed to create notification', 'bettercampaign'));
      }
    } catch (error) {
      console.error('Error creating notification:', error);
      toast({
        title: __('Error', 'bettercampaign'),
        description: error instanceof Error ? error.message : __('Failed to create notification. Please try again.', 'bettercampaign'),
        variant: 'destructive',
      });
    } finally {
      setIsCreatingNotification(false);
    }
  };

  const handleAIContentApply = (content: string, field: string) => {
    // Map AI content types to form fields
    const fieldMapping: Record<string, string> = {
      headline: 'headline',
      cta: 'ctaText',
      description: 'headline', // fallback to headline
      copy: 'headline', // fallback to headline
    };

    const targetField = fieldMapping[field] || field;
    form.setValue(targetField as any, content);

    toast({
      title: __('Applied!', 'bettercampaign'),
      description: __('AI content has been applied to the form.', 'bettercampaign'),
    });
  };



  return (
    <Form {...form}>
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2 top-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Bell className="h-5 w-5" />
                {__('Notification Bar Configuration', 'bettercampaign')}
              </CardTitle>
              <CardDescription>
                {__('Configure your notification bar settings and content', 'bettercampaign')}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
                {/* Headline */}
                <FormField
                  control={form.control}
                  name="headline"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{__('Headline', 'bettercampaign')}</FormLabel>
                      <FormControl>
                        <Input 
                          placeholder={__('🎉 Limited Time Offer - Save 50% Today!', 'bettercampaign')} 
                          {...field} 
                        />
                      </FormControl>
                      <FormDescription>
                        {__('The main message that will grab attention', 'bettercampaign')}
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* CTA Text */}
                  <FormField
                    control={form.control}
                    name="ctaText"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{__('Call-to-Action Text', 'bettercampaign')}</FormLabel>
                        <FormControl>
                          <Input 
                            placeholder={__('Shop Now', 'bettercampaign')} 
                            {...field} 
                          />
                        </FormControl>
                        <FormDescription>
                          {__('Text for the action button', 'bettercampaign')}
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* CTA URL */}
                  <FormField
                    control={form.control}
                    name="ctaUrl"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{__('Call-to-Action URL', 'bettercampaign')}</FormLabel>
                        <FormControl>
                          <Input 
                            type="url"
                            placeholder={__('https://example.com/sale', 'bettercampaign')} 
                            {...field} 
                          />
                        </FormControl>
                        <FormDescription>
                          {__('Where users will be directed when they click', 'bettercampaign')}
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                {/* Background Image - Only show if not using Gutenberg */}
                {!form.watch('isGutenberg') && (
                  <FormField
                    control={form.control}
                    name="backgroundImage"
                    render={({ field }) => (
                      <FormItem>
                        <ImageUploadWithAI
                          label={__('Background Image (Optional)', 'bettercampaign')}
                          description={__('Upload or generate a background image for the notification bar', 'bettercampaign')}
                          value={field.value || ''}
                          onChange={field.onChange}
                          onAIGenerate={(prompt) => {
                            // TODO: Implement AI image generation
                            console.log('Generate image with prompt:', prompt);
                          }}
                        />
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                )}

                {/* Create Notification Button - Bottom of Form */}
                <div className="pt-6 border-t">
                  <Button
                    type="button"
                    onClick={handleCreateNotification}
                    disabled={isCreatingNotification}
                    className="w-full gap-2"
                    size="lg"
                  >
                    {isCreatingNotification ? (
                      <LoadingSpinner size="sm" text={__('Creating...', 'bettercampaign')} />
                    ) : (
                      <>
                        <Plus className="h-4 w-4" />
                        {__('Create Notification', 'bettercampaign')}
                      </>
                    )}
                  </Button>
                </div>
            </CardContent>
          </Card>
        </div>

        {/* AI Assistance Sidebar - Fixed Design */}
        <div className="lg:col-span-1">
          <div className="sticky top-6 space-y-4">
            {/* AI Content Assistant Card */}
            <Card className="h-fit">
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center gap-2 text-base">
                  <Sparkles className="h-4 w-4" />
                  {__('AI Assistant', 'bettercampaign')}
                </CardTitle>
                <CardDescription className="text-sm">
                  {__('Generate compelling content for your notification bar', 'bettercampaign')}
                </CardDescription>
              </CardHeader>
              <CardContent className="pt-0">
                <Button
                  type="button"
                  onClick={() => setShowAIDrawer(true)}
                  className="w-full gap-2"
                  variant="outline"
                  size="sm"
                >
                  <Sparkles className="h-4 w-4" />
                  {__('Generate with AI', 'bettercampaign')}
                </Button>
              </CardContent>
            </Card>

            {/* Additional Sidebar Content - Placement Settings */}
            <Card className="h-fit">
              <CardHeader className="pb-3">
                <CardTitle className="text-base">{__('Placement', 'bettercampaign')}</CardTitle>
                <CardDescription className="text-sm">
                  {__('Choose where to display the notification bar', 'bettercampaign')}
                </CardDescription>
              </CardHeader>
              <CardContent className="pt-0">
                <FormField
                  control={form.control}
                  name="placement"
                  render={({ field }) => (
                    <FormItem className="space-y-3">
                      <FormControl>
                        <RadioGroup
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                          className="space-y-2"
                        >
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="top" id="top" />
                            <Label htmlFor="top" className="text-sm flex items-center gap-2">
                              <ArrowUp className="h-3 w-3" />
                              {__('Top of page', 'bettercampaign')}
                            </Label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="bottom" id="bottom" />
                            <Label htmlFor="bottom" className="text-sm flex items-center gap-2">
                              <ArrowDown className="h-3 w-3" />
                              {__('Bottom of page', 'bettercampaign')}
                            </Label>
                          </div>
                        </RadioGroup>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </CardContent>
            </Card>

            {/* Countdown Settings */}
            <Card className="h-fit">
              <CardHeader className="pb-3">
                <CardTitle className="text-base">{__('Countdown Timer', 'bettercampaign')}</CardTitle>
                <CardDescription className="text-sm">
                  {__('Add urgency with a countdown timer', 'bettercampaign')}
                </CardDescription>
              </CardHeader>
              <CardContent className="pt-0 space-y-4">
                <FormField
                  control={form.control}
                  name="countdownToggle"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                      <div className="space-y-0.5">
                        <FormLabel className="text-sm font-medium">
                          {__('Enable Countdown', 'bettercampaign')}
                        </FormLabel>
                        <FormDescription className="text-xs">
                          {__('Show a countdown timer', 'bettercampaign')}
                        </FormDescription>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />

                {form.watch('countdownToggle') && (
                  <FormField
                    control={form.control}
                    name="endDate"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-sm">{__('End Date', 'bettercampaign')}</FormLabel>
                        <FormControl>
                          <DatePicker
                            date={field.value}
                            onDateChange={field.onChange}
                            placeholder={__('Select end date', 'bettercampaign')}
                          />
                        </FormControl>
                        <FormDescription className="text-xs">
                          {__('When the countdown should end', 'bettercampaign')}
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      {/* Success Drawer */}
      {creationResult && (
        <SuccessDrawer
          isOpen={showSuccessDrawer}
          onClose={() => setShowSuccessDrawer(false)}
          notificationBar={creationResult.notification_bar}
          campaign={creationResult.campaign}
        />
      )}

      {/* AI Content Drawer */}
      <AIContentDrawer
        isOpen={showAIDrawer}
        onClose={() => setShowAIDrawer(false)}
        context={{
          type: 'notification',
          data: form.getValues(),
        }}
        onApplyContent={handleAIContentApply}
      />
    </Form>
  );
};
