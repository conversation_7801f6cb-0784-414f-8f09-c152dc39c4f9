import { __ } from '@wordpress/i18n';
import React, { useState, useEffect } from 'react';
import { UseFormReturn } from 'react-hook-form';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/bettercampaign/components/ui/card';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/bettercampaign/components/ui/form';
import { Input } from '@/bettercampaign/components/ui/input';
import { Button } from '@/bettercampaign/components/ui/button';
import { useToast } from '@/bettercampaign/components/ui/use-toast';
import { LoadingSpinner } from '@/bettercampaign/components/ui/loading-spinner';
import { ImageUploadWithAI } from '@/bettercampaign/components/ui/image-upload-with-ai';
import { AIExitPopupHeadingPopover } from '@/bettercampaign/components/ui/ai-exit-popup-heading-popover';
import { AIExitPopupButtonPopover } from '@/bettercampaign/components/ui/ai-exit-popup-button-popover';
import { ExitIntentPopup } from '@/bettercampaign/lib/schemas';
import { Zap, Plus, Clock } from 'lucide-react';

interface ExitIntentPopupStepProps {
  form: UseFormReturn<ExitIntentPopup>;
  onComplete?: (data: any) => void;
  onNext?: () => void;
  hasNextStep?: boolean;
  isInWizard?: boolean;
}

export const ExitIntentPopupStep: React.FC<ExitIntentPopupStepProps> = ({
  form,
  onComplete,
  onNext,
  hasNextStep = false,
  isInWizard = false
}) => {
  const [isCreatingPopup, setIsCreatingPopup] = useState(false);
  const [exitPopupData, setExitPopupData] = useState<any>(null);
  const { toast } = useToast();

  // Load JSON data on component mount
  useEffect(() => {
    const loadExitPopupData = async () => {
      try {
        // Import the JSON data
        const response = await fetch('/wp-content/plugins/bettercampaign/dev/bettercampaign/data/exit-intend-popup.json');
        if (response.ok) {
          const data = await response.json();
          setExitPopupData(data);

          // Pre-fill form with default values from JSON if available
          if (data.content) {
            // Extract default values from the JSON content
            // This is a simplified extraction - you might want to parse the content more thoroughly
            const defaultHeading = 'Wait! Don\'t Leave Empty-Handed!';
            const defaultButtonText = 'Claim My Discount';

            form.setValue('heading', defaultHeading);
            form.setValue('buttonText', defaultButtonText);
            form.setValue('timerHours', 24); // Default 24 hours
          }
        }
      } catch (error) {
        console.error('Failed to load exit popup data:', error);
      }
    };

    loadExitPopupData();
  }, [form]);

  const handleCreatePopup = async () => {
    setIsCreatingPopup(true);

    try {
      const formData = form.getValues();

      // Validate required fields
      if (!formData.heading || !formData.buttonText || !formData.buttonUrl) {
        toast({
          title: __('Validation Error', 'bettercampaign'),
          description: __('Please fill in all required fields.', 'bettercampaign'),
          variant: 'destructive',
        });
        return;
      }

      // API call to create exit intent popup
      const response = await fetch('/wp-admin/admin-ajax.php', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
          action: 'create_exit_intent_popup',
          nonce: (window as any).betterCampaignAdmin?.nonce || '',
          heading: formData.heading,
          button_text: formData.buttonText,
          button_url: formData.buttonUrl,
          timer_hours: formData.timerHours?.toString() || '24',
          offer_image: formData.offerImage || '',
          background_image: formData.backgroundImage || '',
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();

      if (result.success && result.data) {
        const creationData = {
          popup: {
            post_id: result.data.popup?.post_id || 0,
            edit_url: result.data.popup?.edit_url || '',
          },
          campaign: {
            id: result.data.campaign?.id || 0,
            preview_url: result.data.campaign?.preview_url || '#',
          },
        };

        // Handle next step logic
        if (isInWizard && hasNextStep) {
          toast({
            title: __('Success!', 'bettercampaign'),
            description: __('Exit intent popup created! Proceeding to next step...', 'bettercampaign'),
          });

          onComplete?.(creationData);

          setTimeout(() => {
            onNext?.();
          }, 1000);

        } else if (isInWizard && !hasNextStep) {
          toast({
            title: __('Campaign Created!', 'bettercampaign'),
            description: __('Your exit intent popup campaign has been created successfully!', 'bettercampaign'),
          });

          onComplete?.(creationData);

        } else {
          toast({
            title: __('Success!', 'bettercampaign'),
            description: __('Exit intent popup created successfully!', 'bettercampaign'),
          });
        }
      } else {
        const errorMessage = result.data?.message || result.message || __('Failed to create exit intent popup', 'bettercampaign');
        throw new Error(errorMessage);
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : __('Unknown error occurred', 'bettercampaign');
      toast({
        title: __('Error', 'bettercampaign'),
        description: errorMessage,
        variant: 'destructive',
      });
    } finally {
      setIsCreatingPopup(false);
    }
  };

  const handleAIContentApply = (content: string, field: string) => {
    // Map AI content types to form fields
    const fieldMapping: Record<string, string> = {
      heading: 'heading',
      buttonText: 'buttonText',
    };

    const targetField = fieldMapping[field] || field;
    form.setValue(targetField as any, content);

    toast({
      title: __('Applied!', 'bettercampaign'),
      description: __('AI content has been applied to the form.', 'bettercampaign'),
    });
  };

  return (
    <>
      <Form {...form}>
        <div className="max-w-4xl mx-auto">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Zap className="h-5 w-5" />
                {__('Exit Intent Popup Configuration', 'bettercampaign')}
              </CardTitle>
              <CardDescription>
                {__('Configure your exit intent popup to capture leaving visitors', 'bettercampaign')}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Timer Hours */}
              <FormField
                control={form.control}
                name="timerHours"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center gap-2">
                      <Clock className="h-4 w-4" />
                      {__('Timer (Hours)', 'bettercampaign')}
                    </FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        min="1"
                        max="168"
                        placeholder={__('24', 'bettercampaign')}
                        {...field}
                        onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                      />
                    </FormControl>
                    <FormDescription>
                      {__('Set countdown timer in hours (1-168 hours)', 'bettercampaign')}
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Heading */}
              <FormField
                control={form.control}
                name="heading"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center justify-between">
                      {__('Heading', 'bettercampaign')}
                      <AIExitPopupHeadingPopover onApplyContent={handleAIContentApply} />
                    </FormLabel>
                    <FormControl>
                      <Input
                        placeholder={__('Wait! Don\'t Leave Empty-Handed!', 'bettercampaign')}
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      {__('The main heading that will grab attention', 'bettercampaign')}
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Button Text */}
                <FormField
                  control={form.control}
                  name="buttonText"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="flex items-center justify-between">
                        {__('Button Text', 'bettercampaign')}
                        <AIExitPopupButtonPopover onApplyContent={handleAIContentApply} />
                      </FormLabel>
                      <FormControl>
                        <Input
                          placeholder={__('Claim My Discount', 'bettercampaign')}
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        {__('Text for the action button', 'bettercampaign')}
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Button URL */}
                <FormField
                  control={form.control}
                  name="buttonUrl"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{__('Button URL', 'bettercampaign')}</FormLabel>
                      <FormControl>
                        <Input
                          type="url"
                          placeholder={__('https://example.com/offer', 'bettercampaign')}
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        {__('Where the button should redirect users', 'bettercampaign')}
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Offer Image */}
              <FormField
                control={form.control}
                name="offerImage"
                render={({ field }) => (
                  <FormItem>
                    <ImageUploadWithAI
                      label={__('Offer Image', 'bettercampaign')}
                      description={__('Upload or generate an image for your offer', 'bettercampaign')}
                      value={field.value || ''}
                      onChange={field.onChange}
                      onAIGenerate={(prompt) => {
                        // TODO: Implement AI image generation
                        console.log('Generate offer image with prompt:', prompt);
                      }}
                    />
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Background Image */}
              <FormField
                control={form.control}
                name="backgroundImage"
                render={({ field }) => (
                  <FormItem>
                    <ImageUploadWithAI
                      label={__('Background Image (Optional)', 'bettercampaign')}
                      description={__('Upload or generate a background image for the popup', 'bettercampaign')}
                      value={field.value || ''}
                      onChange={field.onChange}
                      onAIGenerate={(prompt) => {
                        // TODO: Implement AI image generation
                        console.log('Generate background image with prompt:', prompt);
                      }}
                    />
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Create Popup Button */}
              <div className="pt-6 border-t">
                <Button
                  type="button"
                  onClick={handleCreatePopup}
                  disabled={isCreatingPopup}
                  className="w-full gap-2"
                  size="lg"
                >
                  {isCreatingPopup ? (
                    <LoadingSpinner
                      size="sm"
                      text={isInWizard
                        ? __('Creating and proceeding...', 'bettercampaign')
                        : __('Creating...', 'bettercampaign')
                      }
                    />
                  ) : (
                    <>
                      <Plus className="h-4 w-4" />
                      {isInWizard && hasNextStep
                        ? __('Save and Continue', 'bettercampaign')
                        : isInWizard && !hasNextStep
                        ? __('Complete Campaign', 'bettercampaign')
                        : __('Create Exit Intent Popup', 'bettercampaign')
                      }
                    </>
                  )}
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </Form>
    </>
  );
};
