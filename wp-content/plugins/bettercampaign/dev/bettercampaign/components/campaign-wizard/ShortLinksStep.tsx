import { __ } from '@wordpress/i18n';
import React, { useState } from 'react';
import { UseFormReturn } from 'react-hook-form';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/bettercampaign/components/ui/card';
import { Button } from '@/bettercampaign/components/ui/button';
import { useToast } from '@/bettercampaign/components/ui/use-toast';
import { LoadingSpinner } from '@/bettercampaign/components/ui/loading-spinner';
import { ShortLink } from '@/bettercampaign/lib/schemas';
import { Link, BarChart3, Copy, Zap, ExternalLink, CheckCircle2, AlertCircle } from 'lucide-react';

interface ShortLinksStepProps {
  form: UseFormReturn<ShortLink>;
  campaignData?: {
    title?: string;
    description?: string;
    features?: string[];
  };
}

interface GeneratedLink {
  id?: number;
  title: string;
  slug: string;
  shortUrl: string;
  targetUrl: string;
  status: 'pending' | 'creating' | 'success' | 'error';
  error?: string;
}

export const ShortLinksStep: React.FC<ShortLinksStepProps> = ({ form, campaignData }) => {
  const [isGeneratingLinks, setIsGeneratingLinks] = useState(false);
  const [generatedLinks, setGeneratedLinks] = useState<GeneratedLink[]>([]);
  const [categoryId, setCategoryId] = useState<number | null>(null);
  const { toast } = useToast();

  // Generate contextual link templates based on campaign data
  const generateLinkTemplates = (): Omit<GeneratedLink, 'status'>[] => {
    const campaignTitle = campaignData?.title || 'Campaign';
    const baseUrl = window.location.origin;
    
    return [
      {
        title: `${campaignTitle} - Landing Page`,
        slug: 'campaign-landing',
        shortUrl: 'go/campaign-landing',
        targetUrl: `${baseUrl}/campaign/special-offer?utm_source=campaign&utm_medium=shortlink&utm_campaign=main-landing`,
      },
      {
        title: 'Exit Intent Special Offer',
        slug: 'exit-offer',
        shortUrl: 'go/exit-offer',
        targetUrl: `${baseUrl}/pricing/unlimited-lifetime?utm_source=exit-popup&utm_medium=shortlink&utm_campaign=exit-intent-offer`,
      },
      {
        title: 'Notification Bar CTA',
        slug: 'notify-cta',
        shortUrl: 'go/notify-cta',
        targetUrl: `${baseUrl}/get-started?utm_source=notification-bar&utm_medium=shortlink&utm_campaign=top-banner-cta`,
      },
      {
        title: 'Email Campaign Promotion',
        slug: 'email-promo',
        shortUrl: 'go/email-promo',
        targetUrl: `${baseUrl}/products/featured?utm_source=newsletter&utm_medium=email&utm_campaign=weekly-newsletter`,
      },
      {
        title: 'Social Media Campaign',
        slug: 'social-campaign',
        shortUrl: 'go/social-campaign',
        targetUrl: `${baseUrl}/special-deals?utm_source=social&utm_medium=facebook&utm_campaign=social-media-blast`,
      },
      {
        title: 'Limited Time Flash Sale',
        slug: 'flash-sale',
        shortUrl: 'go/flash-sale',
        targetUrl: `${baseUrl}/flash-sale?utm_source=campaign&utm_medium=shortlink&utm_campaign=limited-time-offer`,
      },
      {
        title: 'Free Trial Registration',
        slug: 'free-trial',
        shortUrl: 'go/free-trial',
        targetUrl: `${baseUrl}/signup/free-trial?utm_source=campaign&utm_medium=shortlink&utm_campaign=free-trial-signup`,
      },
    ];
  };

  // Create or get BetterLinks category
  const createCategory = async (): Promise<number> => {
    try {
      const categoryName = `${campaignData?.title || 'Campaign'} Links`;
      const categorySlug = categoryName.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/^-|-$/g, '');

      const response = await fetch('/wp-json/betterlinks/v1/categories', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-WP-Nonce': (window as any).wpApiSettings?.nonce || '',
        },
        body: JSON.stringify({
          name: categoryName,
          slug: categorySlug,
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      if (result.success && result.data?.id) {
        return result.data.id;
      } else {
        return 1; // Default category ID
      }
    } catch (error) {
      console.error('Error creating category:', error);
      return 1; // Default category ID
    }
  };

  // Create a single BetterLinks short link
  const createShortLink = async (linkData: Omit<GeneratedLink, 'status'>, catId: number): Promise<GeneratedLink> => {
    try {
      const timestamp = new Date().toISOString().slice(0, 19).replace('T', ' ');
      
      const response = await fetch('/wp-json/betterlinks/v1/links', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-WP-Nonce': (window as any).wpApiSettings?.nonce || '',
        },
        body: JSON.stringify({
          params: {
            link_title: linkData.title,
            link_slug: linkData.slug,
            target_url: linkData.targetUrl,
            short_url: linkData.shortUrl,
            redirect_type: '307',
            track_me: true,
            nofollow: true,
            prefix: 'go',
            cat_id: catId,
            link_date: timestamp,
            link_modified: timestamp,
          },
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      if (result.success && result.data) {
        return {
          ...linkData,
          id: result.data.ID || result.data.id,
          status: 'success',
        };
      } else {
        throw new Error(result.message || 'Failed to create link');
      }
    } catch (error) {
      return {
        ...linkData,
        status: 'error',
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  };

  // Generate all short links
  const handleGenerateLinks = async () => {
    setIsGeneratingLinks(true);
    
    try {
      // Step 1: Create or get category
      const catId = await createCategory();
      setCategoryId(catId);

      // Step 2: Generate link templates
      const linkTemplates = generateLinkTemplates();
      
      // Step 3: Initialize links with pending status
      const initialLinks: GeneratedLink[] = linkTemplates.map(template => ({
        ...template,
        status: 'pending',
      }));
      setGeneratedLinks(initialLinks);

      // Step 4: Create links one by one
      for (let i = 0; i < linkTemplates.length; i++) {
        const template = linkTemplates[i];
        
        // Update status to creating
        setGeneratedLinks(prev => prev.map((link, index) => 
          index === i ? { ...link, status: 'creating' } : link
        ));

        // Create the link
        const createdLink = await createShortLink(template, catId);
        
        // Update with result
        setGeneratedLinks(prev => prev.map((link, index) => 
          index === i ? createdLink : link
        ));

        // Small delay between requests
        await new Promise(resolve => setTimeout(resolve, 500));
      }

      toast({
        title: __('Success!', 'bettercampaign'),
        description: __('Short links have been generated successfully!', 'bettercampaign'),
      });

    } catch (error) {
      toast({
        title: __('Error', 'bettercampaign'),
        description: __('Failed to generate short links. Please try again.', 'bettercampaign'),
        variant: 'destructive',
      });
    } finally {
      setIsGeneratingLinks(false);
    }
  };

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      toast({
        title: __('Copied!', 'bettercampaign'),
        description: __('Short URL copied to clipboard', 'bettercampaign'),
      });
    } catch (error) {
      toast({
        title: __('Error', 'bettercampaign'),
        description: __('Failed to copy to clipboard', 'bettercampaign'),
        variant: 'destructive',
      });
    }
  };

  return (
    <div className="max-w-4xl mx-auto">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Zap className="h-5 w-5" />
              <div>
                <CardTitle>{__('Generate Campaign Short Links', 'bettercampaign')}</CardTitle>
                <CardDescription>
                  {__('Automatically create trackable short links for your campaign', 'bettercampaign')}
                </CardDescription>
              </div>
            </div>
            <Button
              type="button"
              onClick={handleGenerateLinks}
              disabled={isGeneratingLinks}
              className="gap-2"
            >
              {isGeneratingLinks ? (
                <LoadingSpinner size="sm" text={__('Generating...', 'bettercampaign')} />
              ) : (
                <>
                  <Zap className="h-4 w-4" />
                  {__('Generate Links', 'bettercampaign')}
                </>
              )}
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {generatedLinks.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <Link className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>{__('Click "Generate Links" to create trackable short URLs for your campaign', 'bettercampaign')}</p>
            </div>
          ) : (
            <div className="space-y-4">
              {generatedLinks.map((link, index) => (
                <div
                  key={index}
                  className="flex items-center justify-between p-4 border rounded-lg"
                >
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      <h4 className="font-medium text-sm">{link.title}</h4>
                      {link.status === 'success' && (
                        <CheckCircle2 className="h-4 w-4 text-green-500" />
                      )}
                      {link.status === 'error' && (
                        <AlertCircle className="h-4 w-4 text-red-500" />
                      )}
                      {link.status === 'creating' && (
                        <div className="h-4 w-4 animate-spin rounded-full border-2 border-blue-500 border-t-transparent" />
                      )}
                    </div>
                    <div className="space-y-1">
                      <div className="flex items-center gap-2 text-sm">
                        <span className="text-muted-foreground">{__('Short URL:', 'bettercampaign')}</span>
                        <code className="bg-muted px-2 py-1 rounded text-xs">
                          {window.location.origin}/{link.shortUrl}
                        </code>
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => copyToClipboard(`${window.location.origin}/${link.shortUrl}`)}
                          className="h-6 w-6 p-0"
                        >
                          <Copy className="h-3 w-3" />
                        </Button>
                      </div>
                      <div className="text-xs text-muted-foreground truncate">
                        <span>{__('Target:', 'bettercampaign')} </span>
                        <span>{link.targetUrl}</span>
                      </div>
                      {link.error && (
                        <div className="text-xs text-red-500">
                          {__('Error:', 'bettercampaign')} {link.error}
                        </div>
                      )}
                    </div>
                  </div>
                  {link.status === 'success' && (
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => window.open(`${window.location.origin}/${link.shortUrl}`, '_blank')}
                      className="gap-1 ml-4"
                    >
                      <ExternalLink className="h-3 w-3" />
                      {__('Test', 'bettercampaign')}
                    </Button>
                  )}
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
