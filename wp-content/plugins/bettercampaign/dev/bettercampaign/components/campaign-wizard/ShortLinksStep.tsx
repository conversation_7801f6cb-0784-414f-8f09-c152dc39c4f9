import { __ } from '@wordpress/i18n';
import React, { useState, useEffect } from 'react';
import { UseFormReturn } from 'react-hook-form';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/bettercampaign/components/ui/card';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/bettercampaign/components/ui/form';
import { Input } from '@/bettercampaign/components/ui/input';
import { Button } from '@/bettercampaign/components/ui/button';
import { useToast } from '@/bettercampaign/components/ui/use-toast';
import { LoadingSpinner } from '@/bettercampaign/components/ui/loading-spinner';
import { AIAssistance } from '@/bettercampaign/components/ui/ai-assistance';
import { ShortLink } from '@/bettercampaign/lib/schemas';
import { Link, BarChart3, Copy, Zap, ExternalLink, CheckCircle2, AlertCircle } from 'lucide-react';

interface ShortLinksStepProps {
  form: UseFormReturn<ShortLink>;
  campaignData?: {
    title?: string;
    description?: string;
    features?: string[];
  };
}

interface GeneratedLink {
  id?: number;
  title: string;
  slug: string;
  shortUrl: string;
  targetUrl: string;
  status: 'pending' | 'creating' | 'success' | 'error';
  error?: string;
}

export const ShortLinksStep: React.FC<ShortLinksStepProps> = ({ form, campaignData }) => {
  const longUrl = form.watch('longUrl');
  const customSlug = form.watch('customSlug');
  const utmSource = form.watch('utmSource');
  const utmMedium = form.watch('utmMedium');
  const utmCampaign = form.watch('utmCampaign');
  const [aiGenerating, setAiGenerating] = useState(false);
  const [isGeneratingLinks, setIsGeneratingLinks] = useState(false);
  const [generatedLinks, setGeneratedLinks] = useState<GeneratedLink[]>([]);
  const [categoryId, setCategoryId] = useState<number | null>(null);
  const { toast } = useToast();

  // Generate preview URL
  const generatePreviewUrl = () => {
    if (!longUrl) return '';

    try {
      const url = new URL(longUrl);
      const params = new URLSearchParams(url.search);

      if (utmSource) params.set('utm_source', utmSource);
      if (utmMedium) params.set('utm_medium', utmMedium);
      if (utmCampaign) params.set('utm_campaign', utmCampaign);

      url.search = params.toString();
      return url.toString();
    } catch {
      return longUrl;
    }
  };

  const generateShortUrl = () => {
    if (!customSlug) return '';
    return `https://yourdomain.com/${customSlug}`;
  };

  // Generate contextual link templates based on campaign data
  const generateLinkTemplates = (): Omit<GeneratedLink, 'status'>[] => {
    const campaignTitle = campaignData?.title || 'Campaign';
    const baseUrl = window.location.origin;
    const timestamp = new Date().toISOString().slice(0, 19).replace('T', ' ');

    return [
      {
        title: `${campaignTitle} - Landing Page`,
        slug: 'campaign-landing',
        shortUrl: 'go/campaign-landing',
        targetUrl: `${baseUrl}/campaign/special-offer?utm_source=campaign&utm_medium=shortlink&utm_campaign=main-landing`,
      },
      {
        title: 'Exit Intent Special Offer',
        slug: 'exit-offer',
        shortUrl: 'go/exit-offer',
        targetUrl: `${baseUrl}/pricing/unlimited-lifetime?utm_source=exit-popup&utm_medium=shortlink&utm_campaign=exit-intent-offer`,
      },
      {
        title: 'Notification Bar CTA',
        slug: 'notify-cta',
        shortUrl: 'go/notify-cta',
        targetUrl: `${baseUrl}/get-started?utm_source=notification-bar&utm_medium=shortlink&utm_campaign=top-banner-cta`,
      },
      {
        title: 'Email Campaign Promotion',
        slug: 'email-promo',
        shortUrl: 'go/email-promo',
        targetUrl: `${baseUrl}/products/featured?utm_source=newsletter&utm_medium=email&utm_campaign=weekly-newsletter`,
      },
      {
        title: 'Social Media Campaign',
        slug: 'social-campaign',
        shortUrl: 'go/social-campaign',
        targetUrl: `${baseUrl}/special-deals?utm_source=social&utm_medium=facebook&utm_campaign=social-media-blast`,
      },
      {
        title: 'Limited Time Flash Sale',
        slug: 'flash-sale',
        shortUrl: 'go/flash-sale',
        targetUrl: `${baseUrl}/flash-sale?utm_source=campaign&utm_medium=shortlink&utm_campaign=limited-time-offer`,
      },
      {
        title: 'Free Trial Registration',
        slug: 'free-trial',
        shortUrl: 'go/free-trial',
        targetUrl: `${baseUrl}/signup/free-trial?utm_source=campaign&utm_medium=shortlink&utm_campaign=free-trial-signup`,
      },
    ];
  };

  // Create or get BetterLinks category
  const createCategory = async (): Promise<number> => {
    try {
      const categoryName = `${campaignData?.title || 'Campaign'} Links`;
      const categorySlug = categoryName.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/^-|-$/g, '');

      const response = await fetch('/wp-json/betterlinks/v1/categories', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-WP-Nonce': (window as any).wpApiSettings?.nonce || '',
        },
        body: JSON.stringify({
          name: categoryName,
          slug: categorySlug,
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      if (result.success && result.data?.id) {
        return result.data.id;
      } else {
        // If category already exists, try to get it
        return 1; // Default category ID
      }
    } catch (error) {
      console.error('Error creating category:', error);
      return 1; // Default category ID
    }
  };

  // Create a single BetterLinks short link
  const createShortLink = async (linkData: Omit<GeneratedLink, 'status'>, catId: number): Promise<GeneratedLink> => {
    try {
      const timestamp = new Date().toISOString().slice(0, 19).replace('T', ' ');

      const response = await fetch('/wp-json/betterlinks/v1/links', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-WP-Nonce': (window as any).wpApiSettings?.nonce || '',
        },
        body: JSON.stringify({
          params: {
            link_title: linkData.title,
            link_slug: linkData.slug,
            target_url: linkData.targetUrl,
            short_url: linkData.shortUrl,
            redirect_type: '307',
            track_me: true,
            nofollow: true,
            prefix: 'go',
            cat_id: catId,
            link_date: timestamp,
            link_modified: timestamp,
          },
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      if (result.success && result.data) {
        return {
          ...linkData,
          id: result.data.ID || result.data.id,
          status: 'success',
        };
      } else {
        throw new Error(result.message || 'Failed to create link');
      }
    } catch (error) {
      return {
        ...linkData,
        status: 'error',
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  };

  // Generate all short links
  const handleGenerateLinks = async () => {
    setIsGeneratingLinks(true);

    try {
      // Step 1: Create or get category
      const catId = await createCategory();
      setCategoryId(catId);

      // Step 2: Generate link templates
      const linkTemplates = generateLinkTemplates();

      // Step 3: Initialize links with pending status
      const initialLinks: GeneratedLink[] = linkTemplates.map(template => ({
        ...template,
        status: 'pending',
      }));
      setGeneratedLinks(initialLinks);

      // Step 4: Create links one by one
      for (let i = 0; i < linkTemplates.length; i++) {
        const template = linkTemplates[i];

        // Update status to creating
        setGeneratedLinks(prev => prev.map((link, index) =>
          index === i ? { ...link, status: 'creating' } : link
        ));

        // Create the link
        const createdLink = await createShortLink(template, catId);

        // Update with result
        setGeneratedLinks(prev => prev.map((link, index) =>
          index === i ? createdLink : link
        ));

        // Small delay between requests
        await new Promise(resolve => setTimeout(resolve, 500));
      }

      toast({
        title: __('Success!', 'bettercampaign'),
        description: __('Short links have been generated successfully!', 'bettercampaign'),
      });

    } catch (error) {
      toast({
        title: __('Error', 'bettercampaign'),
        description: __('Failed to generate short links. Please try again.', 'bettercampaign'),
        variant: 'destructive',
      });
    } finally {
      setIsGeneratingLinks(false);
    }
  };

  const handleAIGenerate = () => {
    setAiGenerating(true);
    // TODO: Implement AI generation logic
    setTimeout(() => {
      setAiGenerating(false);
    }, 2000);
  };

  const handleAIApply = (content: string, field: string) => {
    form.setValue(field as any, content);
  };

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      toast({
        title: __('Copied!', 'bettercampaign'),
        description: __('Short URL copied to clipboard', 'bettercampaign'),
      });
    } catch (error) {
      toast({
        title: __('Error', 'bettercampaign'),
        description: __('Failed to copy to clipboard', 'bettercampaign'),
        variant: 'destructive',
      });
    }
  };

  return (
    <Form {...form}>
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2">
          <Card>
      <CardHeader>
        <div className="flex items-center gap-2">
          <Link className="h-5 w-5" />
          <div>
            <CardTitle>{__('Short Links Configuration', 'bettercampaign')}</CardTitle>
            <CardDescription>
              {__('Create trackable short links for your campaign with UTM parameters', 'bettercampaign')}
            </CardDescription>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Long URL */}
        <FormField
          control={form.control}
          name="longUrl"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{__('Long URL', 'bettercampaign')}</FormLabel>
              <FormControl>
                <Input 
                  type="url"
                  placeholder={__('https://example.com/product-page', 'bettercampaign')} 
                  {...field} 
                />
              </FormControl>
              <FormDescription>
                {__('The full URL you want to shorten and track', 'bettercampaign')}
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Custom Slug */}
        <FormField
          control={form.control}
          name="customSlug"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{__('Custom Slug', 'bettercampaign')}</FormLabel>
              <FormControl>
                <div className="flex">
                  <span className="inline-flex items-center px-3 rounded-l-md border border-r-0 border-input bg-muted text-muted-foreground text-sm">
                    yourdomain.com/
                  </span>
                  <Input 
                    placeholder={__('my-campaign', 'bettercampaign')}
                    className="rounded-l-none"
                    {...field} 
                  />
                </div>
              </FormControl>
              <FormDescription>
                {__('Custom path for your short URL (letters, numbers, hyphens, and underscores only)', 'bettercampaign')}
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* UTM Parameters */}
        <div className="space-y-4">
          <div className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            <h3 className="text-lg font-semibold">{__('UTM Parameters', 'bettercampaign')}</h3>
          </div>
          <p className="text-sm text-muted-foreground">
            {__('Add UTM parameters to track the performance of your campaign links in analytics', 'bettercampaign')}
          </p>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* UTM Source */}
            <FormField
              control={form.control}
              name="utmSource"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{__('UTM Source', 'bettercampaign')}</FormLabel>
                  <FormControl>
                    <Input 
                      placeholder={__('newsletter', 'bettercampaign')} 
                      {...field} 
                    />
                  </FormControl>
                  <FormDescription>
                    {__('Traffic source (e.g., newsletter, social)', 'bettercampaign')}
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* UTM Medium */}
            <FormField
              control={form.control}
              name="utmMedium"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{__('UTM Medium', 'bettercampaign')}</FormLabel>
                  <FormControl>
                    <Input 
                      placeholder={__('email', 'bettercampaign')} 
                      {...field} 
                    />
                  </FormControl>
                  <FormDescription>
                    {__('Marketing medium (e.g., email, cpc)', 'bettercampaign')}
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* UTM Campaign */}
            <FormField
              control={form.control}
              name="utmCampaign"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{__('UTM Campaign', 'bettercampaign')}</FormLabel>
                  <FormControl>
                    <Input 
                      placeholder={__('summer-sale', 'bettercampaign')} 
                      {...field} 
                    />
                  </FormControl>
                  <FormDescription>
                    {__('Campaign name or identifier', 'bettercampaign')}
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </div>

        {/* Preview */}
        <div className="mt-6 space-y-4">
          <h4 className="font-medium text-sm">{__('Preview', 'bettercampaign')}</h4>
          
          {/* Short URL Preview */}
          {customSlug && (
            <div className="p-4 bg-muted/50 rounded-lg">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium">{__('Short URL:', 'bettercampaign')}</p>
                  <p className="text-sm text-primary font-mono">{generateShortUrl()}</p>
                </div>
                <Button variant="outline" size="sm">
                  <Copy className="h-4 w-4 mr-2" />
                  {__('Copy', 'bettercampaign')}
                </Button>
              </div>
            </div>
          )}

          {/* Full URL with UTM Preview */}
          {longUrl && (
            <div className="p-4 bg-muted/50 rounded-lg">
              <div className="space-y-2">
                <p className="text-sm font-medium">{__('Full URL with UTM parameters:', 'bettercampaign')}</p>
                <div className="p-2 bg-white border rounded text-xs font-mono break-all">
                  {generatePreviewUrl() || longUrl}
                </div>
                {(utmSource || utmMedium || utmCampaign) && (
                  <div className="text-xs text-muted-foreground">
                    <p>{__('UTM parameters will be automatically added to track campaign performance', 'bettercampaign')}</p>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Analytics Info */}
          <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="flex items-start gap-2">
              <BarChart3 className="h-4 w-4 text-blue-600 mt-0.5" />
              <div className="text-sm">
                <p className="font-medium text-blue-900">{__('Analytics Tracking', 'bettercampaign')}</p>
                <p className="text-blue-700 mt-1">
                  {__('This short link will track clicks, referrers, and conversion data. UTM parameters will help you identify traffic sources in Google Analytics.', 'bettercampaign')}
                </p>
              </div>
            </div>
          </div>

          {/* BetterLinks Generation Section */}
          <Card className="mt-6">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Zap className="h-5 w-5" />
                  <div>
                    <CardTitle>{__('Generate Campaign Short Links', 'bettercampaign')}</CardTitle>
                    <CardDescription>
                      {__('Automatically create trackable short links for your campaign', 'bettercampaign')}
                    </CardDescription>
                  </div>
                </div>
                <Button
                  type="button"
                  onClick={handleGenerateLinks}
                  disabled={isGeneratingLinks}
                  className="gap-2"
                >
                  {isGeneratingLinks ? (
                    <LoadingSpinner size="sm" text={__('Generating...', 'bettercampaign')} />
                  ) : (
                    <>
                      <Zap className="h-4 w-4" />
                      {__('Generate Links', 'bettercampaign')}
                    </>
                  )}
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {generatedLinks.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <Link className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>{__('Click "Generate Links" to create trackable short URLs for your campaign', 'bettercampaign')}</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {generatedLinks.map((link, index) => (
                    <div
                      key={index}
                      className="flex items-center justify-between p-4 border rounded-lg"
                    >
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 mb-1">
                          <h4 className="font-medium text-sm">{link.title}</h4>
                          {link.status === 'success' && (
                            <CheckCircle2 className="h-4 w-4 text-green-500" />
                          )}
                          {link.status === 'error' && (
                            <AlertCircle className="h-4 w-4 text-red-500" />
                          )}
                          {link.status === 'creating' && (
                            <div className="h-4 w-4 animate-spin rounded-full border-2 border-blue-500 border-t-transparent" />
                          )}
                        </div>
                        <div className="space-y-1">
                          <div className="flex items-center gap-2 text-sm">
                            <span className="text-muted-foreground">{__('Short URL:', 'bettercampaign')}</span>
                            <code className="bg-muted px-2 py-1 rounded text-xs">
                              {window.location.origin}/{link.shortUrl}
                            </code>
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              onClick={() => copyToClipboard(`${window.location.origin}/${link.shortUrl}`)}
                              className="h-6 w-6 p-0"
                            >
                              <Copy className="h-3 w-3" />
                            </Button>
                          </div>
                          <div className="text-xs text-muted-foreground truncate">
                            <span>{__('Target:', 'bettercampaign')} </span>
                            <span>{link.targetUrl}</span>
                          </div>
                          {link.error && (
                            <div className="text-xs text-red-500">
                              {__('Error:', 'bettercampaign')} {link.error}
                            </div>
                          )}
                        </div>
                      </div>
                      {link.status === 'success' && (
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => window.open(`${window.location.origin}/${link.shortUrl}`, '_blank')}
                          className="gap-1 ml-4"
                        >
                          <ExternalLink className="h-3 w-3" />
                          {__('Test', 'bettercampaign')}
                        </Button>
                      )}
                    </div>
                  ))}

                  {generatedLinks.length > 0 && (
                    <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                      <div className="flex items-start gap-2">
                        <BarChart3 className="h-5 w-5 text-blue-600 mt-0.5" />
                        <div>
                          <h4 className="font-medium text-blue-900 mb-1">
                            {__('Track Your Links', 'bettercampaign')}
                          </h4>
                          <p className="text-sm text-blue-700">
                            {__('All generated links include UTM tracking parameters and are monitored through BetterLinks analytics. Visit the BetterLinks dashboard to view click statistics and performance metrics.', 'bettercampaign')}
                          </p>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </div>
            </CardContent>
          </Card>
        </div>

        {/* AI Assistance Sidebar */}
        <div className="space-y-4 sticky top-6">
          <AIAssistance
            title={__('Slug Generator', 'bettercampaign')}
            description={__('Generate SEO-friendly custom slugs for your short links', 'bettercampaign')}
            onGenerate={handleAIGenerate}
            onApply={(content) => handleAIApply(content, 'customSlug')}
            isGenerating={aiGenerating}
            suggestions={[
              __('black-friday-sale', 'bettercampaign'),
              __('holiday-deals', 'bettercampaign'),
              __('special-offer', 'bettercampaign'),
              __('limited-time', 'bettercampaign'),
            ]}
          />

          <AIAssistance
            title={__('UTM Generator', 'bettercampaign')}
            description={__('Generate UTM parameters for better tracking', 'bettercampaign')}
            onGenerate={handleAIGenerate}
            onApply={(content) => {
              const [source, medium, campaign] = content.split('|');
              if (source) form.setValue('utmSource', source.trim());
              if (medium) form.setValue('utmMedium', medium.trim());
              if (campaign) form.setValue('utmCampaign', campaign.trim());
            }}
            isGenerating={aiGenerating}
            suggestions={[
              __('newsletter | email | holiday-campaign', 'bettercampaign'),
              __('social | facebook | black-friday', 'bettercampaign'),
              __('website | banner | special-offer', 'bettercampaign'),
            ]}
          />
        </div>
      </div>
    </Form>
  );
};
