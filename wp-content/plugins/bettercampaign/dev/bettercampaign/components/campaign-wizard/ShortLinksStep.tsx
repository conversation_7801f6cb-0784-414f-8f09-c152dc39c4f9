import { __ } from '@wordpress/i18n';
import React, { useState, useEffect } from 'react';
import { UseFormReturn } from 'react-hook-form';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/bettercampaign/components/ui/card';
import { Button } from '@/bettercampaign/components/ui/button';
import { useToast } from '@/bettercampaign/components/ui/use-toast';
import { LoadingSpinner } from '@/bettercampaign/components/ui/loading-spinner';
import { ShortLink } from '@/bettercampaign/lib/schemas';
import { Link, Copy, Zap, ExternalLink, CheckCircle2, AlertCircle, Hash, Calendar } from 'lucide-react';

interface ShortLinksStepProps {
  form: UseFormReturn<ShortLink>;
  campaignData?: {
    title?: string;
    description?: string;
    features?: string[];
    type?: string;
  };
  onLinksGenerated?: (links: GeneratedLinkResult[]) => void;
}

interface GeneratedLink {
  id?: number;
  title: string;
  slug: string;
  shortUrl: string;
  targetUrl: string;
  status: 'pending' | 'creating' | 'success' | 'error';
  error?: string;
}

interface GeneratedLinkResult {
  title: string;
  slug: string;
  short_url: string;
  target_url: string;
  id?: number;
}

export const ShortLinksStep: React.FC<ShortLinksStepProps> = ({
  form,
  campaignData,
  onLinksGenerated
}) => {
  const [isGeneratingLinks, setIsGeneratingLinks] = useState(false);
  const [generatedLinks, setGeneratedLinks] = useState<GeneratedLink[]>([]);
  const [categoryId, setCategoryId] = useState<number | null>(null);
  const { toast } = useToast();

  // Generate unique hash for slug uniqueness
  const generateHash = (): string => {
    return Math.random().toString(36).substring(2, 8);
  };

  // Generate timestamp for current date
  const getCurrentTimestamp = (): string => {
    return new Date().toISOString().slice(0, 19).replace('T', ' ');
  };

  // Generate campaign context slug suffix
  const generateContextSuffix = (): string => {
    const campaignTitle = campaignData?.title || campaignData?.type || 'campaign';
    const year = new Date().getFullYear();
    return campaignTitle
      .toLowerCase()
      .replace(/[^a-z0-9\s]/g, '')
      .replace(/\s+/g, '-')
      .substring(0, 20) + `-${year}`;
  };

  // Check slug uniqueness
  const checkSlugUniqueness = async (slug: string): Promise<boolean> => {
    try {
      const response = await fetch('/wp-admin/admin-ajax.php', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'X-WP-Nonce': (window as any).wpApiSettings?.nonce || '',
        },
        body: new URLSearchParams({
          action: 'betterlinks/admin/cat_slug_unique_checker',
          slug: slug,
        }),
      });

      if (response.ok) {
        const result = await response.json();
        return result.success && result.data?.unique !== false;
      }
      return true; // Assume unique if check fails
    } catch (error) {
      console.warn('Slug uniqueness check failed:', error);
      return true; // Assume unique if check fails
    }
  };

  // Generate unique slug
  const generateUniqueSlug = async (baseSlug: string): Promise<string> => {
    let slug = baseSlug;
    let isUnique = await checkSlugUniqueness(slug);
    let counter = 1;

    while (!isUnique && counter < 10) {
      slug = `${baseSlug}-${counter}`;
      isUnique = await checkSlugUniqueness(slug);
      counter++;
    }

    // If still not unique after 10 attempts, add random hash
    if (!isUnique) {
      slug = `${baseSlug}-${generateHash()}`;
    }

    return slug;
  };

  // Generate contextual link templates based on campaign data
  const generateLinkTemplates = async (): Promise<Omit<GeneratedLink, 'status'>[]> => {
    const baseUrl = window.location.origin;
    const contextSuffix = generateContextSuffix();

    const templates = [
      {
        title: 'Notification Bar CTA',
        baseSlug: `notify-bar-cta-${contextSuffix}`,
        targetUrl: `${baseUrl}/pricing?utm_source=notification-bar&utm_medium=shortlink&utm_campaign=${contextSuffix}`,
      },
      {
        title: 'Exit Intent Popup CTA',
        baseSlug: `exit-popup-cta-${contextSuffix}`,
        targetUrl: `${baseUrl}/special-offer?utm_source=exit-popup&utm_medium=shortlink&utm_campaign=${contextSuffix}`,
      },
      {
        title: 'Pricing Plan CTA: Single Site (1 Year)',
        baseSlug: `pricing-single-year-${contextSuffix}`,
        targetUrl: `${baseUrl}/checkout/single-site-yearly?utm_source=pricing&utm_medium=shortlink&utm_campaign=${contextSuffix}`,
      },
      {
        title: 'Pricing Plan CTA: Unlimited Site (1 Year)',
        baseSlug: `pricing-unlimited-year-${contextSuffix}`,
        targetUrl: `${baseUrl}/checkout/unlimited-yearly?utm_source=pricing&utm_medium=shortlink&utm_campaign=${contextSuffix}`,
      },
      {
        title: 'Pricing Plan CTA: Unlimited Site (Lifetime)',
        baseSlug: `pricing-unlimited-lifetime-${contextSuffix}`,
        targetUrl: `${baseUrl}/checkout/unlimited-lifetime?utm_source=pricing&utm_medium=shortlink&utm_campaign=${contextSuffix}`,
      },
      {
        title: 'Pricing Plan CTA: Agency Bundle (1 Year)',
        baseSlug: `pricing-agency-year-${contextSuffix}`,
        targetUrl: `${baseUrl}/checkout/agency-yearly?utm_source=pricing&utm_medium=shortlink&utm_campaign=${contextSuffix}`,
      },
      {
        title: 'Pricing Plan CTA: Agency Bundle (Lifetime)',
        baseSlug: `pricing-agency-lifetime-${contextSuffix}`,
        targetUrl: `${baseUrl}/checkout/agency-lifetime?utm_source=pricing&utm_medium=shortlink&utm_campaign=${contextSuffix}`,
      },
    ];

    // Generate unique slugs for all templates
    const linkTemplates = [];
    for (const template of templates) {
      const uniqueSlug = await generateUniqueSlug(template.baseSlug);
      linkTemplates.push({
        title: template.title,
        slug: uniqueSlug,
        shortUrl: `go/${uniqueSlug}`,
        targetUrl: template.targetUrl,
      });
    }

    return linkTemplates;
  };

  // Create BetterLinks category using /terms endpoint
  const createCategory = async (): Promise<number> => {
    try {
      const campaignTitle = campaignData?.title || campaignData?.type || 'Campaign';
      const categoryName = `${campaignTitle} Links`;
      const categorySlug = await generateUniqueSlug(
        categoryName.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/^-|-$/g, '')
      );

      const response = await fetch('/wp-json/betterlinks/v1/terms', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-WP-Nonce': (window as any).wpApiSettings?.nonce || '',
        },
        body: JSON.stringify({
          params: {
            ID: 0,
            term_name: categoryName,
            term_slug: categorySlug,
            term_type: 'category',
          },
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      if (result.success && result.data?.ID) {
        return result.data.ID;
      } else if (result.success && result.data?.id) {
        return result.data.id;
      } else {
        throw new Error(result.message || 'Failed to create category');
      }
    } catch (error) {
      console.error('Error creating category:', error);
      // Return a default category ID or throw error
      throw new Error(`Category creation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  // Create a single BetterLinks short link using /links endpoint
  const createShortLink = async (linkData: Omit<GeneratedLink, 'status'>, catId: number): Promise<GeneratedLink> => {
    try {
      const timestamp = getCurrentTimestamp();

      const response = await fetch('/wp-json/betterlinks/v1/links', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-WP-Nonce': (window as any).wpApiSettings?.nonce || '',
        },
        body: JSON.stringify({
          params: {
            link_title: linkData.title,
            link_slug: linkData.slug,
            target_url: linkData.targetUrl,
            short_url: linkData.shortUrl,
            cat_id: catId,
            redirect_type: '307',
            nofollow: true,
            track_me: true,
            prefix: 'go',
            link_date: timestamp,
            link_modified: timestamp,
          },
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      if (result.success && result.data) {
        return {
          ...linkData,
          id: result.data.ID || result.data.id,
          status: 'success',
        };
      } else {
        throw new Error(result.message || 'Failed to create link');
      }
    } catch (error) {
      return {
        ...linkData,
        status: 'error',
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  };

  // Generate all short links
  const handleGenerateLinks = async () => {
    setIsGeneratingLinks(true);

    try {
      // Step 1: Create category
      const catId = await createCategory();
      setCategoryId(catId);

      // Step 2: Generate link templates (await the async function)
      const linkTemplates = await generateLinkTemplates();

      // Step 3: Initialize links with pending status
      const initialLinks: GeneratedLink[] = linkTemplates.map(template => ({
        ...template,
        status: 'pending' as const,
      }));
      setGeneratedLinks(initialLinks);

      // Step 4: Create links one by one
      const finalLinks: GeneratedLinkResult[] = [];

      for (let i = 0; i < linkTemplates.length; i++) {
        const template = linkTemplates[i];

        // Update status to creating
        setGeneratedLinks(prev => prev.map((link, index) =>
          index === i ? { ...link, status: 'creating' as const } : link
        ));

        // Create the link
        const createdLink = await createShortLink(template, catId);

        // Update with result
        setGeneratedLinks(prev => prev.map((link, index) =>
          index === i ? createdLink : link
        ));

        // Add to final results if successful
        if (createdLink.status === 'success') {
          finalLinks.push({
            title: createdLink.title,
            slug: createdLink.slug,
            short_url: createdLink.shortUrl,
            target_url: createdLink.targetUrl,
            id: createdLink.id,
          });
        }

        // Small delay between requests
        await new Promise(resolve => setTimeout(resolve, 500));
      }

      // Notify parent component with generated links
      if (onLinksGenerated && finalLinks.length > 0) {
        onLinksGenerated(finalLinks);
      }

      toast({
        title: __('Success!', 'bettercampaign'),
        description: __(`${finalLinks.length} short links generated successfully!`, 'bettercampaign'),
      });

    } catch (error) {
      console.error('Link generation error:', error);
      toast({
        title: __('Error', 'bettercampaign'),
        description: error instanceof Error ? error.message : __('Failed to generate short links. Please try again.', 'bettercampaign'),
        variant: 'destructive',
      });
    } finally {
      setIsGeneratingLinks(false);
    }
  };

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      toast({
        title: __('Copied!', 'bettercampaign'),
        description: __('Short URL copied to clipboard', 'bettercampaign'),
      });
    } catch (error) {
      toast({
        title: __('Error', 'bettercampaign'),
        description: __('Failed to copy to clipboard', 'bettercampaign'),
        variant: 'destructive',
      });
    }
  };

  return (
    <div className="max-w-6xl mx-auto">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Zap className="h-5 w-5 text-blue-600" />
              <div>
                <CardTitle className="text-xl">
                  {__('Generate Campaign Short Links', 'bettercampaign')}
                </CardTitle>
                <CardDescription className="text-base">
                  {__('Automatically create 7 trackable short links for your campaign components', 'bettercampaign')}
                </CardDescription>
              </div>
            </div>
            <Button
              type="button"
              onClick={handleGenerateLinks}
              disabled={isGeneratingLinks}
              className="gap-2 px-6 py-2"
              size="lg"
            >
              {isGeneratingLinks ? (
                <LoadingSpinner size="sm" text={__('Generating...', 'bettercampaign')} />
              ) : (
                <>
                  <Zap className="h-4 w-4" />
                  {__('Generate Links', 'bettercampaign')}
                </>
              )}
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {generatedLinks.length === 0 ? (
            <div className="text-center py-12 text-muted-foreground">
              <Link className="h-16 w-16 mx-auto mb-6 opacity-50" />
              <h3 className="text-lg font-medium mb-2">
                {__('Ready to Generate Campaign Links', 'bettercampaign')}
              </h3>
              <p className="text-sm max-w-md mx-auto">
                {__('Click "Generate Links" to create 7 unique trackable short URLs that will be used across your campaign components including notification bars, exit popups, and pricing plans.', 'bettercampaign')}
              </p>
              <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-3 max-w-2xl mx-auto text-xs text-left">
                <div className="p-3 bg-blue-50 rounded-lg">
                  <div className="font-medium text-blue-900 mb-1">
                    {__('Notification & Popup CTAs', 'bettercampaign')}
                  </div>
                  <div className="text-blue-700">
                    {__('Links for notification bars and exit intent popups', 'bettercampaign')}
                  </div>
                </div>
                <div className="p-3 bg-green-50 rounded-lg">
                  <div className="font-medium text-green-900 mb-1">
                    {__('Pricing Plan CTAs', 'bettercampaign')}
                  </div>
                  <div className="text-green-700">
                    {__('Links for all pricing tiers and packages', 'bettercampaign')}
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold">
                  {__('Generated Campaign Links', 'bettercampaign')}
                </h3>
                <div className="text-sm text-muted-foreground">
                  {generatedLinks.filter(link => link.status === 'success').length} / {generatedLinks.length} {__('successful', 'bettercampaign')}
                </div>
              </div>

              {generatedLinks.map((link, index) => (
                <div
                  key={index}
                  className={`p-4 border rounded-lg transition-all ${
                    link.status === 'success' ? 'border-green-200 bg-green-50/50' :
                    link.status === 'error' ? 'border-red-200 bg-red-50/50' :
                    link.status === 'creating' ? 'border-blue-200 bg-blue-50/50' :
                    'border-gray-200'
                  }`}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-2">
                        <h4 className="font-medium text-sm">{link.title}</h4>
                        {link.status === 'success' && (
                          <CheckCircle2 className="h-4 w-4 text-green-600" />
                        )}
                        {link.status === 'error' && (
                          <AlertCircle className="h-4 w-4 text-red-600" />
                        )}
                        {link.status === 'creating' && (
                          <div className="h-4 w-4 animate-spin rounded-full border-2 border-blue-600 border-t-transparent" />
                        )}
                        {link.status === 'pending' && (
                          <div className="h-4 w-4 rounded-full bg-gray-300" />
                        )}
                      </div>

                      <div className="space-y-2">
                        <div className="flex items-center gap-2 text-sm">
                          <span className="text-muted-foreground font-medium">{__('Short URL:', 'bettercampaign')}</span>
                          <code className="bg-white px-3 py-1 rounded border text-xs font-mono">
                            {window.location.origin}/{link.shortUrl}
                          </code>
                          {link.status === 'success' && (
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              onClick={() => copyToClipboard(`${window.location.origin}/${link.shortUrl}`)}
                              className="h-7 w-7 p-0"
                            >
                              <Copy className="h-3 w-3" />
                            </Button>
                          )}
                        </div>

                        <div className="text-xs text-muted-foreground">
                          <span className="font-medium">{__('Target:', 'bettercampaign')} </span>
                          <span className="break-all">{link.targetUrl}</span>
                        </div>

                        <div className="text-xs text-muted-foreground">
                          <span className="font-medium">{__('Slug:', 'bettercampaign')} </span>
                          <code className="bg-gray-100 px-1 rounded">{link.slug}</code>
                        </div>

                        {link.error && (
                          <div className="text-xs text-red-600 bg-red-100 p-2 rounded">
                            <span className="font-medium">{__('Error:', 'bettercampaign')} </span>
                            {link.error}
                          </div>
                        )}
                      </div>
                    </div>

                    {link.status === 'success' && (
                      <div className="flex gap-2 ml-4">
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => window.open(`${window.location.origin}/${link.shortUrl}`, '_blank')}
                          className="gap-1"
                        >
                          <ExternalLink className="h-3 w-3" />
                          {__('Test', 'bettercampaign')}
                        </Button>
                      </div>
                    )}
                  </div>
                </div>
              ))}

              {generatedLinks.some(link => link.status === 'success') && (
                <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                  <div className="flex items-start gap-3">
                    <CheckCircle2 className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />
                    <div>
                      <h4 className="font-medium text-blue-900 mb-1">
                        {__('Links Ready for Campaign Use', 'bettercampaign')}
                      </h4>
                      <p className="text-sm text-blue-700 mb-2">
                        {__('Your generated links are now available for use in:', 'bettercampaign')}
                      </p>
                      <ul className="text-sm text-blue-700 space-y-1">
                        <li>• {__('NotificationX notification bars', 'bettercampaign')}</li>
                        <li>• {__('Exit intent popup buttons', 'bettercampaign')}</li>
                        <li>• {__('Pricing plan CTA buttons', 'bettercampaign')}</li>
                        <li>• {__('Email campaigns and social media', 'bettercampaign')}</li>
                      </ul>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
