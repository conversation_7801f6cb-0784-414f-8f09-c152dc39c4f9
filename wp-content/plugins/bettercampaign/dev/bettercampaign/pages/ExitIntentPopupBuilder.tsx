import { __ } from '@wordpress/i18n';
import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/bettercampaign/components/ui/card';
import { Button } from '@/bettercampaign/components/ui/button';
import { useToast } from '@/bettercampaign/components/ui/use-toast';
import { ExitIntentPopupStep } from '@/bettercampaign/components/campaign-wizard/ExitIntentPopupStep';
import { exitIntentPopupSchema } from '@/bettercampaign/lib/schemas';
import { ArrowLeft, Zap, FileText } from 'lucide-react';

interface ExitIntentPopupBuilderProps {
  onBack?: () => void;
}

export const ExitIntentPopupBuilder: React.FC<ExitIntentPopupBuilderProps> = ({ onBack }) => {
  const [exitPopupData, setExitPopupData] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const { toast } = useToast();

  // Initialize form with schema validation
  const form = useForm({
    resolver: zodResolver(exitIntentPopupSchema),
    defaultValues: {
      timerHours: 24,
      heading: '',
      buttonText: '',
      buttonUrl: '',
      offerImage: '',
      backgroundImage: '',
    },
  });

  // Load JSON data on component mount
  useEffect(() => {
    const loadExitPopupData = async () => {
      try {
        setIsLoading(true);
        
        // Load template data via API
        const response = await fetch('/wp-admin/admin-ajax.php', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
          body: new URLSearchParams({
            action: 'get_exit_intent_popup_template',
            nonce: (window as any).betterCampaignAdmin?.nonce || '',
          }),
        });

        if (response.ok) {
          const result = await response.json();
          if (result.success && result.data.template) {
            const data = result.data.template;
            setExitPopupData(data);

            // Pre-fill form with default values from JSON template
            const defaultHeading = 'Wait! Don\'t Leave Empty-Handed!';
            const defaultButtonText = 'Click Me!';

            // Set form values
            form.setValue('heading', defaultHeading);
            form.setValue('buttonText', defaultButtonText);
            form.setValue('timerHours', 100); // Default from template
            form.setValue('buttonUrl', 'https://google.com');

            toast({
              title: __('Template Loaded', 'bettercampaign'),
              description: __('Exit intent popup template has been loaded successfully.', 'bettercampaign'),
            });
          }
        } else {
          console.warn('Could not load exit popup template data');
        }
      } catch (error) {
        console.error('Failed to load exit popup data:', error);
        toast({
          title: __('Warning', 'bettercampaign'),
          description: __('Could not load template data. Using default values.', 'bettercampaign'),
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    };

    loadExitPopupData();
  }, [form, toast]);

  const handleComplete = (data: any) => {
    toast({
      title: __('Success!', 'bettercampaign'),
      description: __('Exit intent popup page has been created successfully!', 'bettercampaign'),
    });

    // Show success message with links
    if (data.page) {
      setTimeout(() => {
        toast({
          title: __('Page Created!', 'bettercampaign'),
          description: __('You can now edit your exit intent popup in Gutenberg.', 'bettercampaign'),
        });
      }, 2000);
    }

    console.log('Exit intent popup page created:', data);
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">{__('Loading exit intent popup builder...', 'bettercampaign')}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center gap-4">
              {onBack && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={onBack}
                  className="gap-2"
                >
                  <ArrowLeft className="h-4 w-4" />
                  {__('Back', 'bettercampaign')}
                </Button>
              )}
              
              <div className="flex items-center gap-3">
                <div className="p-2 bg-purple-100 rounded-lg">
                  <Zap className="h-6 w-6 text-purple-600" />
                </div>
                <div>
                  <h1 className="text-xl font-semibold text-gray-900">
                    {__('Exit Intent Popup Builder', 'bettercampaign')}
                  </h1>
                  <p className="text-sm text-gray-600">
                    {__('Create compelling popups to capture leaving visitors', 'bettercampaign')}
                  </p>
                </div>
              </div>
            </div>

            {/* Template Info */}
            {exitPopupData && (
              <div className="flex items-center gap-2 text-sm text-gray-600">
                <FileText className="h-4 w-4" />
                <span>{__('Template loaded from:', 'bettercampaign')} exit-intend-popup.json</span>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Template Information Card */}
        {exitPopupData && (
          <Card className="mb-8 border-purple-200 bg-purple-50/50">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-purple-800">
                <FileText className="h-5 w-5" />
                {__('Template Information', 'bettercampaign')}
              </CardTitle>
              <CardDescription className="text-purple-700">
                {__('This builder is pre-configured with template data from exit-intend-popup.json', 'bettercampaign')}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div>
                  <span className="font-medium text-purple-800">{__('Template Title:', 'bettercampaign')}</span>
                  <p className="text-purple-700">{exitPopupData.title || 'ExitIntendPopup'}</p>
                </div>
                <div>
                  <span className="font-medium text-purple-800">{__('File Type:', 'bettercampaign')}</span>
                  <p className="text-purple-700">{exitPopupData.__file || 'wp_block'}</p>
                </div>
                <div>
                  <span className="font-medium text-purple-800">{__('Sync Status:', 'bettercampaign')}</span>
                  <p className="text-purple-700">{exitPopupData.syncStatus || __('Ready', 'bettercampaign')}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Exit Intent Popup Configuration */}
        <ExitIntentPopupStep
          form={form}
          onComplete={handleComplete}
          isInWizard={false}
          hasNextStep={false}
        />
      </div>
    </div>
  );
};
