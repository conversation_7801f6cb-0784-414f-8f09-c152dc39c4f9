import { z } from 'zod';
import { DateRange } from 'react-day-picker';

// Campaign Overview Schema
export const campaignOverviewSchema = z.object({
  name: z.string().min(1, 'Campaign name is required'),
  type: z.enum([
    'bfcm',
    'new_year_sale',
    'christmas_sale',
    'easter_sale',
    'valentines_day',
    'halloween_deal',
    'summer_sale',
    'spring_flash_sale',
    'back_to_school',
    'end_of_year_clearance'
  ], {
    message: 'Please select a campaign type',
  }),
  dateRange: z.custom<DateRange>().optional(),
  // campaignTheme removed as requested
}).refine((data) => {
  if (data.dateRange?.from && data.dateRange?.to) {
    return data.dateRange.to > data.dateRange.from;
  }
  return true;
}, {
  message: 'End date must be after start date',
  path: ['dateRange'],
});

// Feature Selection Schema
export const featureSelectionSchema = z.object({
  features: z.array(z.enum([
    'notification_bar',
    'exit_intent_popup',
    'short_links',
    'price_tracking',
    'ai_content_generation'
  ])).min(1, 'Please select at least one feature'),
});

// Notification Bar Schema
export const notificationBarSchema = z.object({
  headline: z.string().min(1, 'Headline is required'),
  subheadline: z.string().optional(),
  countdownToggle: z.boolean(),
  endDate: z.date().optional(),
  ctaText: z.string().min(1, 'CTA text is required'),
  ctaUrl: z.string().url('Please enter a valid URL'),
  placement: z.enum(['top', 'bottom'], {
    message: 'Please select a placement',
  }),
  backgroundImage: z.string().optional(),
  isGutenberg: z.boolean(),
  gutenbergPatternId: z.string().optional(),
  gutenbergContent: z.string().optional(),
}).refine((data) => {
  if (data.countdownToggle && !data.endDate) {
    return false;
  }
  return true;
}, {
  message: 'End date is required when countdown is enabled',
  path: ['endDate'],
});

// Exit Intent Popup Schema
export const exitIntentPopupSchema = z.object({
  timerHours: z.number().min(1, 'Timer must be at least 1 hour').max(168, 'Timer cannot exceed 168 hours (7 days)').default(24),
  heading: z.string().min(1, 'Heading is required'),
  buttonText: z.string().min(1, 'Button text is required'),
  buttonUrl: z.string().url('Please enter a valid URL'),
  offerImage: z.string().optional(),
  backgroundImage: z.string().optional(),
});

// Short Link Schema
export const shortLinkSchema = z.object({
  longUrl: z.string().url('Please enter a valid URL'),
  customSlug: z.string().min(1, 'Custom slug is required').regex(/^[a-zA-Z0-9-_]+$/, 'Slug can only contain letters, numbers, hyphens, and underscores'),
  utmSource: z.string().optional(),
  utmMedium: z.string().optional(),
  utmCampaign: z.string().optional(),
});

// Price Tier Schema
export const priceTierSchema = z.object({
  name: z.string().min(1, 'Tier name is required'),
  originalPrice: z.number().min(0, 'Original price must be positive'),
  discountedPrice: z.number().min(0, 'Discounted price must be positive'),
}).refine((data) => data.discountedPrice < data.originalPrice, {
  message: 'Discounted price must be less than original price',
  path: ['discountedPrice'],
});

// Price Tracking Schema
export const priceTrackingSchema = z.object({
  productType: z.array(z.enum(['single', 'agency_bundle'])).min(1, 'Please select at least one product type'),
  productName: z.string().min(1, 'Product name is required'),
  originalPrice: z.number().min(0, 'Original price must be positive'),
  discountedPrice: z.number().min(0, 'Discounted price must be positive'),
  showStrikethrough: z.boolean(),
  showPercentageBadge: z.boolean(),
  currency: z.string().min(1, 'Currency is required'),
  priceTiers: z.array(priceTierSchema).optional(),
}).refine((data) => data.discountedPrice < data.originalPrice, {
  message: 'Discounted price must be less than original price',
  path: ['discountedPrice'],
});

// AI Content Generation Schema
export const aiContentGenerationSchema = z.object({
  productName: z.string().min(1, 'Product name is required'),
  offerType: z.enum(['sale', 'launch', 'deal'], {
    message: 'Please select an offer type',
  }),
  campaignTone: z.enum(['friendly', 'urgent', 'professional'], {
    message: 'Please select a campaign tone',
  }),
  ctaFocus: z.enum(['learn_more', 'buy_now', 'save_money'], {
    message: 'Please select a CTA focus',
  }),
  outputTypes: z.array(z.enum([
    'notification_bar',
    'popup',
    'email_header',
    'social_caption'
  ])).min(1, 'Please select at least one output type'),
});

// Complete Campaign Schema
export const completeCampaignSchema = z.object({
  overview: campaignOverviewSchema,
  features: featureSelectionSchema,
  notificationBar: notificationBarSchema.optional(),
  exitIntentPopup: exitIntentPopupSchema.optional(),
  shortLink: shortLinkSchema.optional(),
  priceTracking: priceTrackingSchema.optional(),
  aiContentGeneration: aiContentGenerationSchema.optional(),
});

// Type exports
export type CampaignOverview = z.infer<typeof campaignOverviewSchema>;
export type FeatureSelection = z.infer<typeof featureSelectionSchema>;
export type NotificationBar = z.infer<typeof notificationBarSchema>;
export type ExitIntentPopup = z.infer<typeof exitIntentPopupSchema>;
export type ShortLink = z.infer<typeof shortLinkSchema>;
export type PriceTier = z.infer<typeof priceTierSchema>;
export type PriceTracking = z.infer<typeof priceTrackingSchema>;
export type AIContentGeneration = z.infer<typeof aiContentGenerationSchema>;
export type CompleteCampaign = z.infer<typeof completeCampaignSchema>;
