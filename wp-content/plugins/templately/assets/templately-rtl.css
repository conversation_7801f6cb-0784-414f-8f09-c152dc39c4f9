/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/.pnpm/css-loader@6.11.0_webpack@5.98.0_webpack-cli@5.1.4_/node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[2].use[1]!./node_modules/.pnpm/postcss-loader@6.2.1_postcss@8.5.3_webpack@5.98.0_webpack-cli@5.1.4_/node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[2].use[2]!./node_modules/.pnpm/react-multi-carousel@2.8.5/node_modules/react-multi-carousel/lib/styles.css ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
@font-face{font-family:"revicons";fallback:fallback;src:url(../fonts/revicons.e8746a62.woff) format('woff'),url(../fonts/revicons.57fd05d4.ttf) format('ttf'),url(../fonts/revicons.a77de540.eot) format('ttf')}.react-multi-carousel-list{display:flex;align-items:center;overflow:hidden;position:relative}.react-multi-carousel-track{list-style:none;padding:0;margin:0;display:flex;flex-direction:row;position:relative;transform-style:preserve-3d;backface-visibility:hidden;will-change:transform,transition}.react-multiple-carousel__arrow{position:absolute;outline:0;transition:all .5s;border-radius:35px;z-index:1000;border:0;background:rgba(0,0,0,0.5);min-width:43px;min-height:43px;opacity:1;cursor:pointer}.react-multiple-carousel__arrow:hover{background:rgba(0,0,0,0.8)}.react-multiple-carousel__arrow::before{font-size:20px;color:#fff;display:block;font-family:revicons;text-align:center;z-index:2;position:relative}.react-multiple-carousel__arrow:disabled{cursor:default;background:rgba(0,0,0,0.5)}.react-multiple-carousel__arrow--left{right:calc(4% + 1px)}.react-multiple-carousel__arrow--left::before{content:"\e824"}.react-multiple-carousel__arrow--right{left:calc(4% + 1px)}.react-multiple-carousel__arrow--right::before{content:"\e825"}.react-multi-carousel-dot-list{position:absolute;bottom:0;display:flex;right:0;left:0;justify-content:center;margin:auto;padding:0;margin:0;list-style:none;text-align:center}.react-multi-carousel-dot button{display:inline-block;width:12px;height:12px;border-radius:50%;opacity:1;padding:5px 5px 5px 5px;box-shadow:none;transition:background .5s;border-width:2px;border-style:solid;border-color:grey;padding:0;margin:0;margin-left:6px;outline:0;cursor:pointer}.react-multi-carousel-dot button:hover:active{background:#080808}.react-multi-carousel-dot--active button{background:#080808}.react-multi-carousel-item{transform-style:preserve-3d;backface-visibility:hidden}@media all and (-ms-high-contrast:none),(-ms-high-contrast:active){.react-multi-carousel-item{flex-shrink:0 !important}.react-multi-carousel-track{overflow:visible !important}}[dir='rtl'].react-multi-carousel-list{direction:ltr}.rtl.react-multiple-carousel__arrow--right{left:auto;right:calc(4% + 1px)}.rtl.react-multiple-carousel__arrow--right::before{content:"\e824"}.rtl.react-multiple-carousel__arrow--left{right:auto;left:calc(4% + 1px)}.rtl.react-multiple-carousel__arrow--left::before{content:"\e825"}
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/.pnpm/css-loader@6.11.0_webpack@5.98.0_webpack-cli@5.1.4_/node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[4].use[1]!./node_modules/.pnpm/postcss-loader@6.2.1_postcss@8.5.3_webpack@5.98.0_webpack-cli@5.1.4_/node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[4].use[2]!./node_modules/.pnpm/sass-loader@12.6.0_sass@1.86.0_webpack@5.98.0_webpack-cli@5.1.4_/node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[4].use[3]!./react-src/styles/wrapper.scss ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
.templately-wrapper {
  height: 100%;
  background-color: #eaeff3;
  font-family: "DM Sans", sans-serif;
  font-size: 13px;
  font-weight: 400;
  color: #3c434a;
  @import url("https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap");
  @import url("https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap");
}
.templately-wrapper input,
.templately-wrapper button {
  font-family: "DM Sans", sans-serif;
}
.templately-wrapper .bg-warning {
  background-color: #f39c12 !important;
}
.templately-wrapper .bg-danger {
  background-color: #c0392b !important;
}
.templately-wrapper .text-danger {
  color: #c0392b !important;
}
.templately-wrapper .text-right {
  text-align: left;
}
.templately-wrapper .text-center {
  text-align: center;
}
.templately-wrapper .text-left {
  text-align: right;
}
.templately-wrapper .no-padding {
  padding: 0 !important;
}
.templately-wrapper .no-margin {
  margin: 0 !important;
}
.templately-wrapper .d-flex {
  display: flex !important;
}
.templately-wrapper .d-inline-flex {
  display: inline-flex !important;
}
.templately-wrapper .align-center {
  align-items: center;
}
.templately-wrapper .flex-end {
  justify-content: flex-end;
}
.templately-wrapper .justify-between {
  justify-content: space-between;
}
.templately-wrapper .justify-around {
  justify-content: space-around;
}
.templately-wrapper .justify-center {
  justify-content: center;
}
.templately-wrapper .templately-row {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -15px;
}
.templately-wrapper .templately-row:not(:last-of-type) {
  margin-bottom: 15px;
}
.templately-wrapper .templately-row [class^=templately-col] {
  padding: 0 15px;
  box-sizing: border-box;
}
.templately-wrapper .templately-row .templately-col-1 {
  flex-basis: 8.3333333333%;
  width: 8.3333333333%;
}
.templately-wrapper .templately-row .templately-col-2 {
  flex-basis: 16.6666666667%;
  width: 16.6666666667%;
}
.templately-wrapper .templately-row .templately-col-3 {
  flex-basis: 25%;
  width: 25%;
}
.templately-wrapper .templately-row .templately-col-4 {
  flex-basis: 33.3333333333%;
  width: 33.3333333333%;
}
.templately-wrapper .templately-row .templately-col-5 {
  flex-basis: 41.6666666667%;
  width: 41.6666666667%;
}
.templately-wrapper .templately-row .templately-col-6 {
  flex-basis: 50%;
  width: 50%;
}
.templately-wrapper .templately-row .templately-col-7 {
  flex-basis: 58.3333333333%;
  width: 58.3333333333%;
}
.templately-wrapper .templately-row .templately-col-8 {
  flex-basis: 66.6666666667%;
  width: 66.6666666667%;
}
.templately-wrapper .templately-row .templately-col-9 {
  flex-basis: 75%;
  width: 75%;
}
.templately-wrapper .templately-row .templately-col-10 {
  flex-basis: 83.3333333333%;
  width: 83.3333333333%;
}
.templately-wrapper .templately-row .templately-col-11 {
  flex-basis: 91.6666666667%;
  width: 91.6666666667%;
}
.templately-wrapper .templately-row .templately-col-12 {
  flex-basis: 100%;
  width: 100%;
}
.templately-wrapper [data-templately-tooltip]::before, .templately-wrapper [data-templately-tooltip]::after {
  content: "";
  position: absolute;
  right: 50%;
  bottom: 0px;
  visibility: hidden;
  opacity: 0;
  transition: all 0.15s ease-in 0s;
}
.templately-wrapper [data-templately-tooltip]::before {
  content: attr(data-templately-tooltip);
  color: #fff;
  background: #5453fd;
  padding: 5px 10px;
  font-size: 9px;
  text-align: center;
  width: 100%;
  box-sizing: border-box;
  border-radius: 6px;
  transform: translate(50%, 0px);
}
.templately-wrapper [data-templately-tooltip]::after {
  bottom: calc(100% - 5px);
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 5px;
  border-color: transparent transparent #5453fd transparent;
  transform: translate(50%, -5px);
}
.templately-wrapper [data-templately-tooltip].tt-top::before {
  bottom: 100%;
  width: -moz-max-content;
  width: max-content;
  transform: translate(50%, -5px);
}
.templately-wrapper [data-templately-tooltip].tt-top::after {
  visibility: visible;
  bottom: calc(100% - 5px);
  transform: translate(50%, 0px);
  border-color: #5453fd transparent transparent transparent;
}
.templately-wrapper [data-templately-tooltip].tt-top-left::before {
  bottom: 100%;
  right: 0px;
  width: -moz-max-content;
  width: max-content;
  transform: translate(0px, -5px);
}
.templately-wrapper [data-templately-tooltip].tt-top-left::after {
  visibility: visible;
  bottom: calc(100% - 5px);
  right: 7px;
  transform: translate(0px, 0px);
  border-color: #5453fd transparent transparent transparent;
}
.templately-wrapper [data-templately-tooltip]:hover::before {
  visibility: visible;
  opacity: 1;
}
.templately-wrapper [data-templately-tooltip].tt-top:hover::after {
  visibility: visible;
  opacity: 1;
}
.templately-wrapper [data-templately-tooltip].tt-top-left:hover::after {
  visibility: visible;
  opacity: 1;
}
.templately-wrapper [data-templately-tooltip].tt-warning::before {
  background: #f39c12;
}
.templately-wrapper [data-templately-tooltip].tt-warning::after {
  border-color: #f39c12 transparent transparent transparent;
}
.templately-wrapper [data-templately-tooltip].tt-danger::before {
  background: #c0392b;
}
.templately-wrapper [data-templately-tooltip].tt-danger::after {
  border-color: #c0392b transparent transparent transparent;
}
.templately-wrapper [data-templately-tooltip].tt-black::before {
  background: #101828;
}
.templately-wrapper [data-templately-tooltip].tt-black::after {
  border-color: #101828 transparent transparent transparent;
}
.templately-wrapper .templately-carousel-wrapper .templately-carousel-dot-list {
  margin-top: 10px;
  position: relative;
}
.templately-wrapper .templately-carousel-wrapper .templately-carousel-dot-list li.react-multi-carousel-dot button {
  border-color: #6072ff;
}
.templately-wrapper .templately-carousel-wrapper .templately-carousel-dot-list li.react-multi-carousel-dot.react-multi-carousel-dot--active button {
  background-color: #6072ff;
}
.templately-wrapper .templately-preloader {
  flex-basis: 100%;
  padding: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  text-align: center;
  overflow: hidden;
  max-height: calc(100vh - 102px);
}
.templately-wrapper .templately-preloader img {
  width: 100px;
}
.templately-wrapper .templately-preloader .tp-description {
  margin-top: 15px;
}
.templately-wrapper .templately-preloader.templately-preloader-placeholder {
  padding: 0px;
}
.templately-wrapper .templately-preloader .templately-ph-wrapper {
  width: 100%;
  position: relative;
}
@keyframes templately-placeholder-animation {
  0% {
    transform: translate3d(30%, 0, 0);
  }
  100% {
    transform: translate3d(-30%, 0, 0);
  }
}
.templately-wrapper .templately-preloader .templately-ph-wrapper:before {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 50%;
  z-index: 1;
  width: 500%;
  margin-right: -250%;
  pointer-events: none;
  content: "";
  background: linear-gradient(to left, rgba(255, 255, 255, 0) 46%, rgba(255, 255, 255, 0.35) 50%, rgba(255, 255, 255, 0) 54%) 50% 50%;
  animation: templately-placeholder-animation 0.8s linear infinite;
}
.templately-wrapper .templately-preloader .templately-ph-wrapper .tph-row {
  margin-right: -10px;
  margin-left: -10px;
}
.templately-wrapper .templately-preloader .templately-ph-wrapper .tph-item {
  width: 100%;
  height: 10px;
}
.templately-wrapper .templately-preloader .templately-ph-wrapper .tph-col-1 {
  width: 8.3333333333%;
  height: 10px;
}
.templately-wrapper .templately-preloader .templately-ph-wrapper .tph-col-2 {
  width: 16.6666666667%;
  height: 10px;
}
.templately-wrapper .templately-preloader .templately-ph-wrapper .tph-col-3 {
  width: 25%;
  height: 10px;
}
.templately-wrapper .templately-preloader .templately-ph-wrapper .tph-col-4 {
  width: 33.3333333333%;
  height: 10px;
}
.templately-wrapper .templately-preloader .templately-ph-wrapper .tph-col-5 {
  width: 41.6666666667%;
  height: 10px;
}
.templately-wrapper .templately-preloader .templately-ph-wrapper .tph-col-6 {
  width: 50%;
  height: 10px;
}
.templately-wrapper .templately-preloader .templately-ph-wrapper .tph-col-7 {
  width: 58.3333333333%;
  height: 10px;
}
.templately-wrapper .templately-preloader .templately-ph-wrapper .tph-col-8 {
  width: 66.6666666667%;
  height: 10px;
}
.templately-wrapper .templately-preloader .templately-ph-wrapper .tph-col-9 {
  width: 75%;
  height: 10px;
}
.templately-wrapper .templately-preloader .templately-ph-wrapper .tph-col-10 {
  width: 83.3333333333%;
  height: 10px;
}
.templately-wrapper .templately-preloader .templately-ph-wrapper .tph-col-11 {
  width: 91.6666666667%;
  height: 10px;
}
.templately-wrapper .templately-preloader .templately-ph-wrapper .tph-col-12 {
  width: 100%;
  height: 10px;
}
.templately-wrapper .templately-preloader .templately-ph-wrapper [class^=tph-col],
.templately-wrapper .templately-preloader .templately-ph-wrapper .tph-item {
  background-color: #ced4da;
  padding: 0 10px;
  box-sizing: border-box;
  margin-bottom: 5px;
}
.templately-wrapper .templately-preloader .templately-ph-wrapper [class^=tph-col].tiny,
.templately-wrapper .templately-preloader .templately-ph-wrapper .tph-item.tiny {
  height: 6px;
}
.templately-wrapper .templately-preloader .templately-ph-wrapper [class^=tph-col].medium,
.templately-wrapper .templately-preloader .templately-ph-wrapper .tph-item.medium {
  height: 20px;
}
.templately-wrapper .templately-preloader .templately-ph-wrapper [class^=tph-col].large,
.templately-wrapper .templately-preloader .templately-ph-wrapper .tph-item.large {
  height: 30px;
}
.templately-wrapper .templately-preloader .templately-ph-wrapper [class^=tph-col].rounded,
.templately-wrapper .templately-preloader .templately-ph-wrapper .tph-item.rounded {
  border-radius: 10px;
}
.templately-wrapper .tp-waviy {
  position: relative;
  -webkit-box-reflect: below -20px linear-gradient(transparent, rgba(0, 0, 0, 0.2));
  font-size: 25px;
  margin-top: 15px;
}
.templately-wrapper .tp-waviy span {
  position: relative;
  display: inline-block;
  color: rgb(86, 51, 209);
  text-transform: uppercase;
  animation: waviy 1s infinite;
  animation-delay: calc(0.1s * var(--i));
  letter-spacing: 2px;
}
@keyframes waviy {
  0%, 40%, 100% {
    transform: translateY(0);
  }
  20% {
    transform: translateY(-10px);
  }
}
.templately-wrapper .templately-badge {
  position: absolute;
  left: 0px;
  top: 30px;
  background-color: #5ac0ff;
  color: #fff;
  text-transform: uppercase;
  padding: 10px 20px;
  border-radius: 0 40px 40px 0;
  font-weight: 500;
  font-size: 10px;
  z-index: 3;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
  letter-spacing: 0.5px;
}
.templately-wrapper .templately-badge.templately-pro {
  background: #ff7a8e;
}
.templately-wrapper .templately-badge.templately-pro > svg {
  margin-left: 5px;
}
.templately-wrapper .templately-badge.ribbon {
  width: 100px;
  height: 100px;
  overflow: hidden;
  position: absolute;
  background: none;
  padding: 0;
  border-radius: unset;
  /* top left*/
  /* top right*/
}
.templately-wrapper .templately-badge.ribbon::before, .templately-wrapper .templately-badge.ribbon::after {
  position: absolute;
  z-index: -1;
  content: "";
  display: block;
  border: 5px solid #5453fd;
}
.templately-wrapper .templately-badge.ribbon > span {
  position: absolute;
  display: flex;
  width: 150px;
  font-size: 8px;
  height: 20px;
  color: #fff;
  text-transform: uppercase;
  text-align: center;
  align-items: center;
  justify-content: center;
}
.templately-wrapper .templately-badge.ribbon.templately-free::before, .templately-wrapper .templately-badge.ribbon.templately-free::after {
  border-color: rgb(33.8793103448, 32.5862068966, 252.4137931034);
}
.templately-wrapper .templately-badge.ribbon.templately-free > span {
  background-color: #5453fd;
}
.templately-wrapper .templately-badge.ribbon.templately-pro::before, .templately-wrapper .templately-badge.ribbon.templately-pro::after {
  border-color: rgb(255, 71, 98.6691729323);
}
.templately-wrapper .templately-badge.ribbon.templately-pro > span {
  background-color: #ff7a8e;
}
.templately-wrapper .templately-badge.ribbon.templately-pro > span > svg {
  margin-left: 5px;
}
.templately-wrapper .templately-badge.ribbon.ribbon-top-left {
  top: -10px;
  right: -10px;
}
.templately-wrapper .templately-badge.ribbon.ribbon-top-left::before, .templately-wrapper .templately-badge.ribbon.ribbon-top-left::after {
  border-top-color: transparent;
  border-right-color: transparent;
}
.templately-wrapper .templately-badge.ribbon.ribbon-top-left::before {
  top: 0;
  left: 0;
}
.templately-wrapper .templately-badge.ribbon.ribbon-top-left::after {
  bottom: 0;
  right: 0;
}
.templately-wrapper .templately-badge.ribbon.ribbon-top-left span {
  left: -25px;
  top: 30px;
  transform: rotate(45deg);
}
.templately-wrapper .templately-badge.ribbon.ribbon-top-right {
  top: -10px;
  left: -10px;
}
.templately-wrapper .templately-badge.ribbon.ribbon-top-right::before, .templately-wrapper .templately-badge.ribbon.ribbon-top-right::after {
  border-top-color: transparent;
  border-left-color: transparent;
}
.templately-wrapper .templately-badge.ribbon.ribbon-top-right::before {
  top: 0;
  right: 32px;
}
.templately-wrapper .templately-badge.ribbon.ribbon-top-right::after {
  bottom: 32px;
  left: 0;
}
.templately-wrapper .templately-badge.ribbon.ribbon-top-right span {
  right: 30px;
  top: -30px;
  transform: rotate(-45deg);
  transform-origin: 100% 0;
}
.templately-wrapper .templately-badge.templately-details-banner-badge {
  left: -10px;
  top: -10px;
  z-index: 1;
  overflow: hidden;
  width: 105px;
  height: 105px;
  text-align: left;
  background: none;
}
.templately-wrapper .templately-badge.templately-details-banner-badge > span {
  font-size: 10px;
  font-weight: 700;
  color: #fff;
  text-transform: uppercase;
  text-align: center;
  line-height: 2.5;
  transform: rotate(-45deg);
  width: 130px;
  display: flex;
  position: absolute;
  top: 26px;
  left: -27px;
  align-items: center;
  justify-content: center;
}
.templately-wrapper .templately-badge.templately-details-banner-badge > span > svg {
  margin-left: 5px;
}
.templately-wrapper .templately-badge.templately-details-banner-badge > span:before, .templately-wrapper .templately-badge.templately-details-banner-badge > span:after {
  content: "";
  position: absolute;
  top: calc(100% - 1px);
  z-index: -1;
  border-style: solid;
  border-width: 7px;
}
.templately-wrapper .templately-badge.templately-details-banner-badge > span:before {
  right: 0;
}
.templately-wrapper .templately-badge.templately-details-banner-badge > span:after {
  left: 0;
}
.templately-wrapper .templately-badge.templately-details-banner-badge.templately-pro span {
  background-color: #ff7a8e;
}
.templately-wrapper .templately-badge.templately-details-banner-badge.templately-pro span:before {
  border-color: rgb(255, 71, 98.6691729323) rgb(255, 71, 98.6691729323) transparent transparent;
}
.templately-wrapper .templately-badge.templately-details-banner-badge.templately-pro span:after {
  border-color: rgb(255, 71, 98.6691729323) transparent transparent rgb(255, 71, 98.6691729323);
}
.templately-wrapper .templately-badge.templately-details-banner-badge.templately-free span {
  background-color: #5453fd;
}
.templately-wrapper .templately-badge.templately-details-banner-badge.templately-free span:before {
  border-color: rgb(33.8793103448, 32.5862068966, 252.4137931034) rgb(33.8793103448, 32.5862068966, 252.4137931034) transparent transparent;
}
.templately-wrapper .templately-badge.templately-details-banner-badge.templately-free span:after {
  border-color: rgb(33.8793103448, 32.5862068966, 252.4137931034) transparent transparent rgb(33.8793103448, 32.5862068966, 252.4137931034);
}
.templately-wrapper .templately-list {
  margin: 0;
  padding: 0;
  list-style: none;
}
.templately-wrapper .templately-list li {
  display: flex;
  align-items: center;
  position: relative;
  cursor: pointer;
  margin-bottom: 6px;
}
.templately-wrapper .templately-list li::before {
  position: absolute;
  right: 0px;
  top: 50%;
  margin-top: -7px;
  content: "";
  width: 12px;
  height: 12px;
  border-radius: 2px;
  border: 1px solid #b6bdc7;
  box-sizing: initial;
}
.templately-wrapper .templately-list li.templately-list-item-active::after {
  box-sizing: initial;
  content: "";
  width: 10px;
  height: 10px;
  border-radius: 2px;
  background-color: #5453fd;
  position: absolute;
  right: 2px;
  top: 50%;
  margin-top: -5px;
}
.templately-wrapper .templately-list li > input {
  display: none;
  margin-right: 0;
  margin-top: 0;
  margin-bottom: 0;
  margin-left: 10px;
}
.templately-wrapper .templately-list li > label {
  position: relative;
  z-index: 1;
  padding-right: 22px;
}
.templately-wrapper .templately-button {
  cursor: pointer;
  border: 0;
  background-color: transparent;
  box-shadow: none;
  outline: none;
  text-decoration: none;
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-family: "DM Sans", sans-serif;
  box-sizing: border-box;
  border-radius: 5px;
}
.templately-wrapper .templately-button.btn-insert-pack {
  margin-top: 25px;
  padding: 10px 25px;
  margin-right: auto;
  background-color: #5453F4;
  color: #FFF;
  border-radius: 30px;
  text-transform: uppercase;
  width: 100%;
  max-width: 300px;
}
.templately-wrapper .templately-button.btn-insert-pack > svg {
  margin-left: 5px;
}
.templately-wrapper .templately-button.btn-insert-pack:hover {
  background-color: #5453fd;
  color: #fff;
}
.templately-wrapper .templately-button.templately-close {
  margin-right: 5px;
  padding: 10px;
}
.templately-wrapper .templately-button.templately-close svg {
  width: 20px;
  height: 20px;
}
.templately-theme-dark.templately-wrapper .templately-button.btn-screenshots {
  color: #5453fd;
}

.templately-wrapper .templately-button.rounded {
  border-radius: 50px;
}
.templately-wrapper .templately-button.tb-rd-btn {
  border-radius: 5px;
  margin-left: 10px;
  font-size: 13px;
  padding: 10px 15px;
}
.templately-wrapper .templately-button.tb-verified {
  background-color: #5453fd;
}
.templately-wrapper .templately-button.tb-login {
  background-color: #5453fd;
  color: #fff;
  padding: 10px 20px;
}
.templately-wrapper .templately-button.tb-import {
  background-color: #5453fd;
  color: #fff;
  padding: 10px 30px;
  border-radius: 5px;
  letter-spacing: 1px;
  text-transform: uppercase;
  z-index: 9;
}
.templately-wrapper .templately-button.tb-import > svg {
  margin-left: 10px;
}
.templately-wrapper .templately-button.tb-import.tb-import-secondary {
  margin-top: 10px;
}
.templately-wrapper .templately-button.tb-upgrade-pro-btn {
  font-size: 14px;
  padding: 6px 20px;
  margin-left: 10px;
}
.templately-wrapper .templately-button:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}
.templately-wrapper .templately-button.tf-filter, .templately-wrapper .templately-button.tf-reset {
  border-radius: 4px;
  color: #fff;
  cursor: pointer;
  background-color: #5633d1;
  padding: 12px 20px;
  letter-spacing: 2px;
  transition: all 0.3s ease 0s;
  border: 0;
  margin-left: 10px;
}
.templately-wrapper .templately-button.tf-filter:hover, .templately-wrapper .templately-button.tf-reset:hover {
  background-color: #6072ff;
}
.templately-wrapper .templately-button.tf-reset {
  background-color: #9679ff;
}
.templately-wrapper .templately-button.tb-save-n-push {
  background-color: #6072ff;
  color: #fff;
  padding: 20px 30px;
  border-radius: 5px;
  margin-right: 30px;
}
.templately-wrapper .templately-button.templately-cloud-sync, .templately-wrapper .templately-button.templately-profile-btn {
  width: 47px;
  height: 47px;
  border-radius: 50%;
  border: 1px solid #f2f2f2;
  justify-content: center;
  padding: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.templately-wrapper .templately-button.templately-cloud-sync > img, .templately-wrapper .templately-button.templately-profile-btn > img {
  width: 100%;
  height: 100%;
  border-radius: 50%;
}
.templately-wrapper .templately-button.templately-cloud-sync svg, .templately-wrapper .templately-button.templately-profile-btn svg {
  width: 20px;
  height: 20px;
}
.templately-wrapper .templately-pro-required-btns {
  margin-top: 15px;
}
.templately-wrapper .tc-padding-lrb {
  padding-right: 30px;
  padding-left: 30px;
  padding-bottom: 30px;
}
.templately-wrapper .templately-nav-link:focus {
  box-shadow: none;
  outline: none;
}
.templately-wrapper .templately-nav-wrapper.templately-nav-sidebar li {
  padding: 0 20px;
}
.templately-wrapper .templately-nav-wrapper.templately-nav-sidebar li.nav-item-active {
  background-color: #f7f8ff;
  border-left: 1px solid #6072ff;
}
.templately-wrapper .templately-nav-wrapper.templately-nav-sidebar li.nav-item-active > a {
  color: #6072ff;
}
.templately-wrapper .templately-nav-wrapper.templately-nav-sidebar li.nav-item-active > a > svg {
  fill: #6072ff;
}
.templately-wrapper .templately-nav-wrapper.templately-nav-sidebar li > a {
  text-decoration: none;
  font-size: 16px;
  color: #979fa8;
  display: flex;
  align-items: center;
  padding: 10px;
  margin-bottom: 10px;
  cursor: pointer;
}
.templately-wrapper .templately-nav-wrapper.templately-nav-sidebar li > a > svg {
  fill: #979fa8;
  margin-left: 10px;
}
.templately-wrapper .templately-nav-wrapper.templately-nav-sidebar li > a.active {
  color: #031d3c;
}
.templately-wrapper .templately-nav-wrapper.templately-nav-sidebar li > a.active > svg {
  fill: #6072ff;
}
.templately-wrapper .templately-nav-wrapper.templately-nav-sidebar li > a:focus, .templately-wrapper .templately-nav-wrapper.templately-nav-sidebar li > a:visited {
  outline: none;
  box-shadow: none;
}
.templately-wrapper .templately-nav-wrapper.templately-nav-sidebar li > a i {
  background-repeat: no-repeat;
  background-size: contain;
  width: 20px;
  height: 20px;
  background-position: center center;
  margin-left: 15px;
  display: inline-flex;
  align-items: center;
}
.templately-wrapper .templately-nav-wrapper.templately-nav-sidebar li > a > span.tn-dropdown-icon {
  flex: 1;
  text-align: left;
}
.templately-wrapper .templately-nav-wrapper.templately-nav-sidebar li > .templately-nav-dropdown {
  padding-right: 20px;
}
.templately-wrapper .templately-nav-wrapper.templately-nav-sidebar li > .templately-nav-dropdown.dropdown-active {
  height: auto;
  opacity: 1;
  visibility: visible;
  transition: all 300ms ease 0;
}
.templately-wrapper .templately-nav-wrapper.templately-nav-sidebar li > .templately-nav-dropdown .dropdown-nav-active {
  border-left: 1px solid #6072ff;
}
.templately-wrapper .templately-nav-wrapper.templately-nav-sidebar li > .templately-nav-dropdown .dropdown-nav-active > a {
  color: #6072ff;
}
.templately-wrapper .templately-nav-wrapper.templately-nav-sidebar li > .templately-nav-dropdown .dropdown-nav-active > a > svg {
  fill: #6072ff;
}
.templately-wrapper .templately-sweetalert-container .templately-sweetalert-header h3 {
  margin-top: 0px;
  margin-bottom: 20px;
}
.templately-wrapper .templately-pagination {
  padding: 30px 0 30px;
  margin-top: 20px;
}
.templately-wrapper .templately-pagination > ul {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0px;
}
.templately-wrapper .templately-pagination > ul > li {
  background: #fff;
  margin: 0px 5px;
  min-width: 30px;
  height: 30px;
  cursor: pointer;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid rgba(109, 120, 130, 0.2);
}
.templately-wrapper .templately-pagination > ul > li button {
  background: transparent;
  display: inline-flex;
  border: none;
  margin: 0;
  padding: 0;
  outline: 0;
  cursor: pointer;
}
.templately-wrapper .templately-pagination > ul > li button:focus, .templately-wrapper .templately-pagination > ul > li button:visited {
  outline: none;
  box-shadow: none;
  border: none;
}
.templately-wrapper .templately-pagination > ul > li button:disabled {
  cursor: not-allowed;
  pointer-events: none;
  opacity: 0.7;
}
.templately-wrapper .templately-pagination > ul > li.epage-active {
  font-weight: 700;
  border: 1px solid rgba(109, 120, 130, 0.5);
}
.templately-wrapper .templately-pagination > ul > li.pagination-arrow {
  font-size: 16px;
}
.templately-wrapper #templatelyAdmin .templately-pagination {
  padding: 30px 0 30px;
  margin-top: 0px;
}
.templately-wrapper .templately-table {
  width: 100%;
  flex-basis: 100%;
  text-align: right;
}
.templately-wrapper .templately-table * {
  box-sizing: border-box;
}
.templately-wrapper .templately-table .templately-table-column {
  font-size: 14px;
  font-weight: 500;
  position: relative;
}
.templately-wrapper .templately-table .templately-table-column > .templately-button {
  margin-top: 0px;
}
.templately-wrapper .templately-table .templately-table-column.templately-col-actions-btn {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.templately-wrapper .templately-table .templately-table-column > div.ttcol-header {
  display: flex;
  align-items: center;
}
.templately-wrapper .templately-table .templately-table-column > div.ttcol-header > img {
  margin-left: 10px;
  width: 40px;
  height: 40px;
  -o-object-fit: cover;
     object-fit: cover;
}
.templately-wrapper .templately-table .templately-table-column > .templately-sw-cxt-menu {
  position: absolute;
  left: 0;
  z-index: 11;
  box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.14);
  top: 100%;
}
.templately-wrapper .templately-table > .templately-table-body {
  background: none;
  margin: 0;
  padding: 0;
}
.templately-wrapper .templately-table > .templately-table-body > div:nth-child(6) .templately-table-column > .templately-sw-cxt-menu, .templately-wrapper .templately-table > .templately-table-body > div:nth-child(7) .templately-table-column > .templately-sw-cxt-menu, .templately-wrapper .templately-table > .templately-table-body > div:nth-child(8) .templately-table-column > .templately-sw-cxt-menu, .templately-wrapper .templately-table > .templately-table-body > div:nth-child(9) .templately-table-column > .templately-sw-cxt-menu, .templately-wrapper .templately-table > .templately-table-body > div:nth-child(10) .templately-table-column > .templately-sw-cxt-menu {
  bottom: 100%;
  top: auto;
}
.templately-wrapper .templately-table .templately-table-row.templately-table-head {
  background: none;
}
.templately-wrapper .templately-table .templately-table-row.templately-table-head .templately-table-row {
  background: none;
}
.templately-wrapper .templately-table .templately-table-row.templately-table-head .templately-table-column {
  color: #979fa8;
}
.templately-wrapper .templately-table .templately-table-row .templately-table-column {
  color: #031d3c;
}
.templately-wrapper .templately-table .templately-table-row .templately-table-column:last-of-type .templately-table-column:last-of-type {
  text-align: left;
}
.templately-wrapper .templately-table > .templately-table-row {
  margin-bottom: 10px;
}
.templately-wrapper .templately-table .templately-table-body > .templately-table-row {
  margin-bottom: 10px;
  background-color: #fff;
}
.templately-wrapper .templately-table .templately-table-body > .templately-table-row.templately-table-head, .templately-wrapper .templately-table .templately-table-body > .templately-table-row .templately-table-row {
  background-color: transparent;
}
.templately-wrapper .templately-table .templately-table-row {
  display: flex;
  align-items: center;
}
.templately-wrapper .templately-table .templately-table-row > .templately-table-column {
  flex: 1;
  padding: 15px;
}
.templately-wrapper .templately-table .templately-table-row > .templately-table-column > p {
  margin: 0;
}
.templately-wrapper .templately-table .templately-table-row > .templately-table-column .templately-table-column {
  padding: 0;
}
.templately-wrapper .templately-table .templately-context-menu {
  position: relative;
  left: auto;
  top: auto;
}
.templately-wrapper .templately-table.tt-view-grid .templately-table-body.tt-view-grid {
  display: grid;
  grid-gap: 30px;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
}
.templately-wrapper .templately-table.tt-view-grid .templately-table-body > .templately-table-row {
  position: relative;
  padding: 20px;
  border-radius: 5px;
}
.templately-wrapper .templately-table.tt-view-grid .templately-table-row {
  display: block;
}
.templately-wrapper .templately-table.tt-view-grid .templately-table-row .templatey-cloud-header {
  display: block;
}
.templately-wrapper .templately-table.tt-view-grid .templately-table-column {
  position: unset;
  padding: 0px;
}
.templately-wrapper .templately-table.tt-view-grid .templately-table-column .templately-context-menu {
  position: absolute;
  left: 20px;
  top: 20px;
}
.templately-wrapper .templately-table.tt-view-grid .templately-table-column .templately-table-column > p {
  font-size: 13px;
  color: #6d7c90;
}
.templately-wrapper .templately-search {
  border: 1px solid #f2f2f2;
  border-radius: 25px;
  overflow: hidden;
  display: flex;
  align-items: center;
  background-color: #ffffff;
  max-height: 36px;
  min-height: 36px;
  margin-left: 15px;
  line-height: 1;
}
.templately-wrapper .templately-search .close {
  background: none;
  border: none;
  padding: 0;
  line-height: 0;
  margin: 1px;
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: #f2f2f2;
  cursor: pointer;
}
.templately-wrapper .templately-search > input {
  border: 0px;
  padding: 5px 20px;
  box-sizing: border-box;
  color: #64666a;
  line-height: 1;
  width: auto;
  background: #fff;
}
.templately-wrapper .templately-search > input:focus {
  outline: none;
  box-shadow: none;
}
@media only screen and (max-width: 1024px) and (min-width: 787px) {
  .templately-wrapper .templately-search {
    margin-right: auto;
  }
}
@media only screen and (max-width: 786px) {
  .templately-wrapper .templately-search {
    flex-basis: 38%;
    width: 90%;
    margin-top: 15px;
    margin-bottom: 15px;
  }
}
.templately-wrapper .templately-search .templately-search-button {
  background: #f2f2f2;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  margin: 0;
  color: rgb(87.03428, 52.24848, 209.28152);
}
.templately-wrapper .templately-search.search-active .templately-search-button {
  color: #e74c3c;
}
.templately-wrapper .templately-sticky .templately-search {
  box-shadow: 0 0 50px -30px rgba(0, 0, 0, 0.6);
}
.templately-wrapper .templately-header {
  display: flex;
  align-items: stretch;
  box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1411764706);
  overflow: visible;
  z-index: 10000;
  position: relative;
  background-color: #fff;
  flex-wrap: wrap;
  min-height: 70px;
}
.templately-screen-templately .templately-header {
  position: fixed;
  width: calc(100% - 160px);
  right: 160px;
  top: 32px;
}
.wp-admin.folded .templately-screen-templately .templately-header {
  right: 36px !important;
  width: calc(100% - 36px) !important;
}

@media (max-width: 782px) {
  #wpwrap.wp-responsive-open .templately-screen-templately .templately-header {
    right: 190px;
    width: calc(100% - 190px);
  }
}

@media (max-width: 960px) {
  #wpwrap:not(.wp-responsive-open) .templately-screen-templately .templately-header {
    width: calc(100% - 36px);
    right: 36px;
  }
}
@media (max-width: 782px) {
  #wpwrap:not(.wp-responsive-open) .templately-screen-templately .templately-header {
    width: 100%;
    right: 0px;
    top: 46px;
  }
}

.templately-wrapper .templately-header .templately-logo {
  display: flex;
  align-items: center;
  padding: 15px;
  margin-left: 30px;
  position: relative;
  min-width: 203px;
}
.templately-wrapper .templately-header .templately-logo > svg {
  margin-left: 10px;
}
.templately-wrapper .templately-header .templately-logo > svg + svg {
  max-width: 143px;
  margin-left: 0px;
}
.templately-wrapper .templately-header .templately-logo > span {
  font-size: 20px;
}
.templately-wrapper .templately-header .templately-logo span.templately-back-2-library {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  cursor: pointer;
  height: 100%;
}
.templately-wrapper .templately-header .templately-logo span.templately-back-2-library > svg {
  margin-left: 10px;
}
@media (max-width: 782px) {
  #wpwrap:not(.wp-responsive-open) .templately-screen-templately .templately-logo {
    min-width: 153px;
    margin-left: 10px;
    padding: 10px;
  }
}

.templately-wrapper .templately-header .templately-menu ul.templately-nav li.templately-nav-item:hover > a, .templately-wrapper .templately-header .templately-menu ul.templately-nav li.templately-nav-item.templately-nav-active > a {
  color: #576880;
  background-color: rgba(96, 114, 255, 0.05);
}
.templately-wrapper .templately-header .templately-menu ul.templately-nav li.templately-nav-item:hover > a:after, .templately-wrapper .templately-header .templately-menu ul.templately-nav li.templately-nav-item.templately-nav-active > a:after {
  content: "";
  background: #6072ff;
  position: absolute;
  bottom: -2px;
  width: 100%;
  height: 2px;
  right: 0;
  border-radius: 0 0 5px 5px;
}
.templately-wrapper .templately-header .templately-menu {
  display: flex;
  align-items: stretch;
}
.templately-wrapper .templately-header .templately-menu ul.templately-nav {
  display: flex;
  margin: 0;
  padding: 0;
}
.templately-wrapper .templately-header .templately-menu ul.templately-nav li.templately-nav-item {
  font-size: 16px;
  font-weight: 400;
  margin-bottom: 0;
}
.templately-wrapper .templately-header .templately-menu ul.templately-nav li.templately-nav-item svg {
  fill: #979fa8;
}
.templately-wrapper .templately-header .templately-menu ul.templately-nav li.templately-nav-item.templately-nav-active a.efy-clouds-nav::after {
  background: #6072ff;
}
.templately-wrapper .templately-header .templately-menu ul.templately-nav li.templately-nav-item.templately-nav-active svg {
  fill: #5453fd;
}
.templately-wrapper .templately-header .templately-menu ul.templately-nav li.templately-nav-item:hover a.efy-clouds-nav::after {
  background: #6072ff;
}
.templately-wrapper .templately-header .templately-menu ul.templately-nav li.templately-nav-item > a {
  color: #a4b0c1;
  height: 100%;
  align-items: center;
  display: flex;
  padding: 0 16px;
  position: relative;
  text-decoration: none;
  box-shadow: none;
}
.templately-wrapper .templately-header .templately-menu ul.templately-nav li.templately-nav-item > a:focus, .templately-wrapper .templately-header .templately-menu ul.templately-nav li.templately-nav-item > a:active {
  outline: none;
}
.templately-wrapper .templately-header .templately-menu ul.templately-nav li.templately-nav-item > a > i {
  display: inline-block;
  margin-left: 10px;
}
.templately-wrapper .templately-header .templately-menu ul.templately-nav li.templately-nav-item > a > i > svg {
  width: 20px;
}
@media (max-width: 782px) {
  .templately-screen-templately.templately-wrapper .templately-header .templately-menu ul.templately-nav li.templately-nav-item > a {
    padding: 0 10px;
  }
}

.templately-wrapper .templately-header .templately-actions {
  margin-right: auto;
  align-content: center;
  display: flex;
  padding: 0 20px;
  align-items: center;
}
.templately-wrapper .templately-header .templately-actions .templately-chat-button {
  background: #fff;
  margin: 0 0 0 15px;
  transition: all 0.3s ease 0s;
  border: 1px solid #f2f2f2;
  padding: 6px 16px;
  background-color: #5453fd;
  color: #fff;
  border-radius: 60px;
  font-size: 14px;
  font-weight: 500;
  line-height: 18px;
  letter-spacing: 0em;
  text-align: right;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}
.templately-wrapper .templately-header .templately-actions .templately-chat-button svg {
  font-size: 24px;
}
@media only screen and (max-width: 1439px) {
  .templately-wrapper .templately-header .templately-actions .templately-chat-button span {
    display: none;
  }
}
.templately-wrapper .templately-header .templately-actions .templately-switcher__control {
  min-width: 180px;
  margin-left: 20px;
}
@media (max-width: 782px) {
  .templately-wrapper .templately-header .templately-actions .templately-switcher__control {
    min-width: auto;
  }
  .templately-wrapper .templately-header .templately-actions .templately-switcher__control .templately-switcher__single-value .templately-editor-name {
    display: none;
  }
  .templately-switcher__menu {
    min-width: 180px;
    left: 20px;
  }
}
.templately-wrapper .templately-header .templately-actions .templately-switcher__menu {
  left: 20px;
  z-index: 10000;
}
.templately-wrapper .templately-header .templately-actions .templately-switcher__control .templately-switcher__single-value,
.templately-wrapper .templately-header .templately-actions .templately-switcher__control .templately-switcher__option,
.templately-wrapper .templately-header .templately-actions .templately-switcher__menu .templately-switcher__single-value,
.templately-wrapper .templately-header .templately-actions .templately-switcher__menu .templately-switcher__option {
  display: flex;
  align-items: center;
}
.templately-wrapper .templately-header .templately-actions .templately-switcher__control .templately-switcher__single-value span.tswitcher-icon,
.templately-wrapper .templately-header .templately-actions .templately-switcher__control .templately-switcher__option span.tswitcher-icon,
.templately-wrapper .templately-header .templately-actions .templately-switcher__menu .templately-switcher__single-value span.tswitcher-icon,
.templately-wrapper .templately-header .templately-actions .templately-switcher__menu .templately-switcher__option span.tswitcher-icon {
  width: 25px;
  height: 25px;
  border-radius: 50%;
  display: inline-block;
  margin-left: 5px;
  background-position: center center;
  background-repeat: no-repeat;
  background-size: contain;
}
.templately-wrapper .templately-header .templately-actions .templately-switcher__control .templately-switcher__single-value span.tswitcher-icon.tswitch-elementor,
.templately-wrapper .templately-header .templately-actions .templately-switcher__control .templately-switcher__option span.tswitcher-icon.tswitch-elementor,
.templately-wrapper .templately-header .templately-actions .templately-switcher__menu .templately-switcher__single-value span.tswitcher-icon.tswitch-elementor,
.templately-wrapper .templately-header .templately-actions .templately-switcher__menu .templately-switcher__option span.tswitcher-icon.tswitch-elementor {
  background-image: url(../images/elementor.png);
}
.templately-wrapper .templately-header .templately-actions .templately-switcher__control .templately-switcher__single-value span.tswitcher-icon.tswitch-gutenberg,
.templately-wrapper .templately-header .templately-actions .templately-switcher__control .templately-switcher__option span.tswitcher-icon.tswitch-gutenberg,
.templately-wrapper .templately-header .templately-actions .templately-switcher__menu .templately-switcher__single-value span.tswitcher-icon.tswitch-gutenberg,
.templately-wrapper .templately-header .templately-actions .templately-switcher__menu .templately-switcher__option span.tswitcher-icon.tswitch-gutenberg {
  background-image: url(../images/gutenberg.png);
}
.templately-wrapper .templately-header .templately-actions .templately-profile-drops-wrapper {
  position: relative;
}
.templately-wrapper .templately-header .templately-actions .templately-profile-drops-wrapper .templately-profile-drops-inner {
  position: absolute;
  left: 0;
  top: 100%;
  padding-top: 15px;
}
.templately-wrapper .templately-header .templately-actions .templately-profile-drops-wrapper .templately-profile-drops-inner > ul {
  overflow: hidden;
  min-width: 250px;
  border-radius: 5px;
  padding: 0px;
  margin: 0;
  background-color: #fff;
  box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.14);
}
.templately-wrapper .templately-header .templately-actions .templately-profile-drops-wrapper .templately-profile-drops-inner > ul > li {
  margin: 0;
  padding: 0;
}
.templately-wrapper .templately-header .templately-actions .templately-profile-drops-wrapper .templately-profile-drops-inner > ul > li:not(:last-of-type) {
  border-bottom: 1px solid #f2f2f2;
}
.templately-wrapper .templately-header .templately-actions .templately-profile-drops-wrapper .templately-profile-drops-inner > ul > li > button {
  padding: 15px;
}
.templately-wrapper .templately-header .templately-actions .templately-profile-drops-wrapper .templately-profile-drops-inner > ul > li > button.tb-logout {
  width: 100%;
  justify-content: flex-start;
}
.templately-wrapper .templately-header .templately-actions .templately-profile-drops-wrapper .templately-profile-drops-inner > ul > li a {
  padding: 15px;
  color: #333;
  text-decoration: none;
}
.templately-wrapper .templately-header .templately-actions .templately-profile-drops-wrapper .templately-profile-drops-inner > ul > li a > svg {
  margin-left: 10px;
}
.templately-wrapper .templately-header .templately-actions .templately-profile-drops-wrapper .templately-profile-drops-inner > ul > li a:focus, .templately-wrapper .templately-header .templately-actions .templately-profile-drops-wrapper .templately-profile-drops-inner > ul > li a:visited, .templately-wrapper .templately-header .templately-actions .templately-profile-drops-wrapper .templately-profile-drops-inner > ul > li a:active {
  outline: none;
  box-shadow: none;
}
.templately-wrapper .templately-header .templately-actions .templately-profile-drops-wrapper .templately-profile-drops-inner > ul > li:hover {
  background-color: rgb(86, 51, 209);
  color: #fff;
}
.templately-wrapper .templately-header .templately-actions .templately-profile-drops-wrapper .templately-profile-drops-inner > ul > li:hover > a, .templately-wrapper .templately-header .templately-actions .templately-profile-drops-wrapper .templately-profile-drops-inner > ul > li:hover > button {
  color: #fff;
}
.templately-wrapper .templately-header .templately-actions .templately-profile-drops-wrapper .templately-profile-drops {
  padding: 15px !important;
}
.templately-wrapper .templately-header .templately-actions .templately-profile-drops-wrapper .templately-profile-drops > div > img, .templately-wrapper .templately-header .templately-actions .templately-profile-drops-wrapper .templately-profile-drops > div > svg {
  margin-left: 10px;
  border-radius: 50%;
  overflow: hidden;
}
.templately-wrapper .templately-header .templately-actions .templately-profile-drops-wrapper .templately-profile-drops .templately-profile-drops-details > h4, .templately-wrapper .templately-header .templately-actions .templately-profile-drops-wrapper .templately-profile-drops .templately-profile-drops-details > p {
  margin: 0;
  padding: 0;
}
.templately-wrapper .templately-header .templately-actions .templately-profile-drops-wrapper .templately-profile-drops:hover > div > svg > g {
  fill: #fff !important;
}
.templately-wrapper .templately-container {
  height: calc(100% - 70px);
  position: relative;
}
.templately-screen-templately .templately-container {
  top: 70px;
}

.templately-wrapper .templately-container .templately-container-row {
  display: flex;
  flex-wrap: nowrap;
  flex-direction: row;
  height: 100%;
}
.templately-wrapper .templately-container .templately-error-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 1;
  max-height: calc(100vh - 102px);
}
.templately-wrapper .templately-container .templately-server-notice {
  flex-basis: 100%;
  background: rgba(207, 207, 255, 0.4196078431);
  padding: 0 15px;
  margin-bottom: 13px;
}
.templately-wrapper .templately-container .templately-contents.templately-frontpage {
  display: flex;
  flex-direction: column;
  background-image: url(../images/login-bg.png);
  background-size: cover;
  background-repeat: no-repeat;
  overflow-y: auto;
  padding: 100px 0px;
}
.templately-wrapper .templately-container .templately-contents.templately-frontpage .tf-content-box {
  padding: 0;
  background-color: #f6f6fe;
  width: 95%;
  max-width: 1012px;
  display: flex;
  align-items: stretch;
  justify-content: center;
  border-radius: 24px;
  margin: 0 auto;
  box-sizing: border-box;
}
@media screen and (min-width: 1660px) {
  .templately-wrapper .templately-container .templately-contents.templately-frontpage .tf-content-box {
    margin-top: auto;
    margin-bottom: auto;
  }
}
.templately-wrapper .templately-container .templately-contents.templately-frontpage .tf-content-box .tf-promo-title {
  flex-basis: 43.5%;
  max-width: 43.5%;
  text-align: center;
  color: #fff;
  border-top-right-radius: 24px;
  border-bottom-right-radius: 24px;
  background: linear-gradient(-180deg, #6060ff 0%, #5633d1 100%);
  padding: 50px 35px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.templately-wrapper .templately-container .templately-contents.templately-frontpage .tf-content-box .tf-promo-title img,
.templately-wrapper .templately-container .templately-contents.templately-frontpage .tf-content-box .tf-promo-title svg,
.templately-wrapper .templately-container .templately-contents.templately-frontpage .tf-content-box .tf-promo-title object {
  width: 100%;
  margin-bottom: 50px;
  height: auto;
}
.templately-wrapper .templately-container .templately-contents.templately-frontpage .tf-content-box .tf-promo-title h1 {
  color: #fff;
  font-size: 36px;
  font-weight: 400;
  line-height: 1.2;
  margin: 0 0 16px 0;
}
.templately-wrapper .templately-container .templately-contents.templately-frontpage .tf-content-box .tf-promo-title h1 b {
  font-weight: 700;
}
.templately-wrapper .templately-container .templately-contents.templately-frontpage .tf-content-box .tf-promo-title p {
  color: #fff;
  margin: 0;
  font-size: 13px;
  line-height: 1.85;
  margin-bottom: 32px;
}
.templately-wrapper .templately-container .templately-contents.templately-frontpage .tf-content-box .tf-promo-title button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  border: 1px solid #fff;
  border-radius: 50px;
  padding: 5px 5px 5px 22px;
  gap: 12px;
  background-color: transparent;
  color: #fff;
  font-size: 16px;
  font-weight: 700;
  cursor: pointer;
}
.templately-wrapper .templately-container .templately-contents.templately-frontpage .tf-content-box .tf-promo-title button svg {
  height: 39px;
  width: 39px;
  fill: #fff;
  margin: 0;
  padding: 0;
  flex-shrink: 0;
}
.templately-wrapper .templately-container .templately-contents.templately-frontpage .tf-content-box .tf-user-form {
  flex-basis: 56.5%;
  max-width: 56.5%;
  padding: 16px;
  border-top-left-radius: 24px;
  border-bottom-left-radius: 24px;
  box-sizing: border-box;
}
.templately-wrapper .templately-container .templately-contents.templately-frontpage .tf-content-box .tf-user-form .tf-user-form-tab {
  width: 100%;
  height: 100%;
  background: #fff;
  border-top-left-radius: 24px;
  border-bottom-left-radius: 24px;
  border-radius: 24px;
  padding: 20px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}
.templately-wrapper .templately-container .templately-contents.templately-frontpage .tf-content-box .tf-user-form .tf-user-form-tab > ul.tf-uft-menu {
  display: flex;
  align-items: center;
  margin-top: 36px;
  margin-bottom: 40px;
  margin-right: auto;
  margin-left: auto;
  padding: 0;
  justify-content: center;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}
.templately-wrapper .templately-container .templately-contents.templately-frontpage .tf-content-box .tf-user-form .tf-user-form-tab > ul.tf-uft-menu li {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  min-height: 48px;
  min-width: 170px;
  box-sizing: border-box;
  padding: 2px 32px;
  text-align: center;
  cursor: pointer;
  margin: 0;
  border: 1px solid #5453f4;
  color: #5453f4;
  font-size: 18px;
  font-weight: 500;
}
.templately-wrapper .templately-container .templately-contents.templately-frontpage .tf-content-box .tf-user-form .tf-user-form-tab > ul.tf-uft-menu li:first-child {
  border-top-right-radius: 8px;
  border-bottom-right-radius: 8px;
}
.templately-wrapper .templately-container .templately-contents.templately-frontpage .tf-content-box .tf-user-form .tf-user-form-tab > ul.tf-uft-menu li:last-child {
  border-top-left-radius: 8px;
  border-bottom-left-radius: 8px;
}
.templately-wrapper .templately-container .templately-contents.templately-frontpage .tf-content-box .tf-user-form .tf-user-form-tab > ul.tf-uft-menu li.active {
  background-color: #5453f4;
  color: #fff;
}
.templately-wrapper .templately-container .templately-contents.templately-frontpage .tf-content-box .tf-user-form .tf-user-form-tab .tf-uft-content {
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  height: 100%;
}
.templately-wrapper .templately-container .templately-contents.templately-frontpage .tf-content-box .tf-user-form .tfu-content {
  padding-right: 40px;
  padding-left: 40px;
  box-sizing: border-box;
  height: 100%;
}
.templately-wrapper .templately-container .templately-contents.templately-frontpage .tf-content-box .tf-user-form .tfu-content .tfu-row {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  -moz-column-gap: 18px;
       column-gap: 18px;
}
.templately-wrapper .templately-container .templately-contents.templately-frontpage .tf-content-box .tf-user-form .tfu-content .tfu-row.tfu-cols-2 {
  flex-wrap: wrap;
  margin-bottom: 18px;
}
.templately-wrapper .templately-container .templately-contents.templately-frontpage .tf-content-box .tf-user-form .tfu-content .tfu-row.tfu-cols-2 > * {
  flex-basis: calc(50% - 9px);
  max-width: calc(50% - 9px);
  margin-bottom: 0;
}
.templately-wrapper .templately-container .templately-contents.templately-frontpage .tf-content-box .tf-user-form .tfu-content .tfu-row.tfu-cols-2 .tfu-error-wrapper {
  flex-basis: 100% !important;
  max-width: 100% !important;
}
.templately-wrapper .templately-container .templately-contents.templately-frontpage .tf-content-box .tf-user-form .tfu-content .tf-input-wrapper {
  display: flex;
  flex-direction: column;
  margin-bottom: 18px;
}
.templately-wrapper .templately-container .templately-contents.templately-frontpage .tf-content-box .tf-user-form .tfu-content .tf-input-wrapper .tf-input {
  margin-bottom: 0;
}
.templately-wrapper .templately-container .templately-contents.templately-frontpage .tf-content-box .tf-user-form .tfu-content .tf-input-wrapper .tf-forget-link {
  margin-top: 8px;
  margin-right: auto;
  color: #4d4d4d;
  font-size: 12px;
  font-weight: 400;
  line-height: 1;
  text-decoration: none;
}
.templately-wrapper .templately-container .templately-contents.templately-frontpage .tf-content-box .tf-user-form .tfu-content .tf-input-wrapper .tf-forget-link:hover {
  color: #5453f4;
  text-decoration: underline;
}
.templately-wrapper .templately-container .templately-contents.templately-frontpage .tf-content-box .tf-user-form .tfu-content .tf-input {
  display: flex;
  flex-direction: column;
  margin-bottom: 18px;
}
.templately-wrapper .templately-container .templately-contents.templately-frontpage .tf-content-box .tf-user-form .tfu-content .tf-input .tf-input-label:first-child {
  color: #20172f;
  font-size: 16px;
  font-weight: 400;
  margin-bottom: 12px;
}
.templately-wrapper .templately-container .templately-contents.templately-frontpage .tf-content-box .tf-user-form .tfu-content .tf-input .has-icon,
.templately-wrapper .templately-container .templately-contents.templately-frontpage .tf-content-box .tf-user-form .tfu-content .tf-input .has-change-button {
  position: relative;
}
.templately-wrapper .templately-container .templately-contents.templately-frontpage .tf-content-box .tf-user-form .tfu-content .tf-input .has-icon > img,
.templately-wrapper .templately-container .templately-contents.templately-frontpage .tf-content-box .tf-user-form .tfu-content .tf-input .has-icon > svg,
.templately-wrapper .templately-container .templately-contents.templately-frontpage .tf-content-box .tf-user-form .tfu-content .tf-input .has-icon > object,
.templately-wrapper .templately-container .templately-contents.templately-frontpage .tf-content-box .tf-user-form .tfu-content .tf-input .has-change-button > img,
.templately-wrapper .templately-container .templately-contents.templately-frontpage .tf-content-box .tf-user-form .tfu-content .tf-input .has-change-button > svg,
.templately-wrapper .templately-container .templately-contents.templately-frontpage .tf-content-box .tf-user-form .tfu-content .tf-input .has-change-button > object {
  position: absolute;
  width: 18px;
  height: 18px;
  top: 50%;
  right: 18px;
  transform: translateY(-50%);
  pointer-events: none;
  fill: #bec4ce;
}
.templately-wrapper .templately-container .templately-contents.templately-frontpage .tf-content-box .tf-user-form .tfu-content .tf-input .has-icon > button,
.templately-wrapper .templately-container .templately-contents.templately-frontpage .tf-content-box .tf-user-form .tfu-content .tf-input .has-change-button > button {
  position: absolute;
  top: 50%;
  left: 26px;
  transform: translateY(-50%);
  border: none;
  background-color: transparent;
  cursor: pointer;
  padding: 0;
  display: inline-flex;
  align-items: center;
}
.templately-wrapper .templately-container .templately-contents.templately-frontpage .tf-content-box .tf-user-form .tfu-content .tf-input .has-icon > button > img,
.templately-wrapper .templately-container .templately-contents.templately-frontpage .tf-content-box .tf-user-form .tfu-content .tf-input .has-icon > button > svg,
.templately-wrapper .templately-container .templately-contents.templately-frontpage .tf-content-box .tf-user-form .tfu-content .tf-input .has-icon > button > object,
.templately-wrapper .templately-container .templately-contents.templately-frontpage .tf-content-box .tf-user-form .tfu-content .tf-input .has-change-button > button > img,
.templately-wrapper .templately-container .templately-contents.templately-frontpage .tf-content-box .tf-user-form .tfu-content .tf-input .has-change-button > button > svg,
.templately-wrapper .templately-container .templately-contents.templately-frontpage .tf-content-box .tf-user-form .tfu-content .tf-input .has-change-button > button > object {
  width: 18px;
  height: 18px;
  fill: #bec4ce;
}
.templately-wrapper .templately-container .templately-contents.templately-frontpage .tf-content-box .tf-user-form .tfu-content .tf-input .has-icon > button:hover > img,
.templately-wrapper .templately-container .templately-contents.templately-frontpage .tf-content-box .tf-user-form .tfu-content .tf-input .has-icon > button:hover > svg,
.templately-wrapper .templately-container .templately-contents.templately-frontpage .tf-content-box .tf-user-form .tfu-content .tf-input .has-icon > button:hover > object,
.templately-wrapper .templately-container .templately-contents.templately-frontpage .tf-content-box .tf-user-form .tfu-content .tf-input .has-change-button > button:hover > img,
.templately-wrapper .templately-container .templately-contents.templately-frontpage .tf-content-box .tf-user-form .tfu-content .tf-input .has-change-button > button:hover > svg,
.templately-wrapper .templately-container .templately-contents.templately-frontpage .tf-content-box .tf-user-form .tfu-content .tf-input .has-change-button > button:hover > object {
  fill: #20172f;
}
.templately-wrapper .templately-container .templately-contents.templately-frontpage .tf-content-box .tf-user-form .tfu-content .tf-input .has-icon input:not([type=checkbox]):not([type=radio]) {
  padding-right: 50px;
}
.templately-wrapper .templately-container .templately-contents.templately-frontpage .tf-content-box .tf-user-form .tfu-content .tf-input .has-error input:not([type=checkbox]):not([type=radio]) {
  border-color: #f04438;
}
.templately-wrapper .templately-container .templately-contents.templately-frontpage .tf-content-box .tf-user-form .tfu-content .tf-input .has-change-button input:not([type=checkbox]):not([type=radio]) {
  padding-left: 60px;
}
.templately-wrapper .templately-container .templately-contents.templately-frontpage .tf-content-box .tf-user-form .tfu-content .tf-input.tf-input-radio, .templately-wrapper .templately-container .templately-contents.templately-frontpage .tf-content-box .tf-user-form .tfu-content .tf-input.tf-input-checkbox {
  flex-direction: row;
  margin-bottom: 0;
  gap: 8px;
}
.templately-wrapper .templately-container .templately-contents.templately-frontpage .tf-content-box .tf-user-form .tfu-content .tf-input.tf-input-radio > div, .templately-wrapper .templately-container .templately-contents.templately-frontpage .tf-content-box .tf-user-form .tfu-content .tf-input.tf-input-checkbox > div {
  display: block;
}
.templately-wrapper .templately-container .templately-contents.templately-frontpage .tf-content-box .tf-user-form .tfu-content .tf-input.tf-input-radio > div input[type=radio],
.templately-wrapper .templately-container .templately-contents.templately-frontpage .tf-content-box .tf-user-form .tfu-content .tf-input.tf-input-radio > div input[type=checkbox], .templately-wrapper .templately-container .templately-contents.templately-frontpage .tf-content-box .tf-user-form .tfu-content .tf-input.tf-input-checkbox > div input[type=radio],
.templately-wrapper .templately-container .templately-contents.templately-frontpage .tf-content-box .tf-user-form .tfu-content .tf-input.tf-input-checkbox > div input[type=checkbox] {
  margin-left: 0;
}
.templately-wrapper .templately-container .templately-contents.templately-frontpage .tf-content-box .tf-user-form .tfu-content .tf-input.tf-input-radio .tf-input-label, .templately-wrapper .templately-container .templately-contents.templately-frontpage .tf-content-box .tf-user-form .tfu-content .tf-input.tf-input-checkbox .tf-input-label {
  color: #20172f;
  font-size: 14px;
  font-weight: 400;
}
.templately-wrapper .templately-container .templately-contents.templately-frontpage .tf-content-box .tf-user-form .tfu-content .tf-error-message {
  margin-top: 10px;
  font-size: 14px;
  font-weight: 400;
  line-height: 1.1;
  color: #f04438;
  display: flex;
  align-items: center;
  gap: 4px;
  box-sizing: border-box;
}
.templately-wrapper .templately-container .templately-contents.templately-frontpage .tf-content-box .tf-user-form .tfu-content .tf-error-message svg,
.templately-wrapper .templately-container .templately-contents.templately-frontpage .tf-content-box .tf-user-form .tfu-content .tf-error-message img,
.templately-wrapper .templately-container .templately-contents.templately-frontpage .tf-content-box .tf-user-form .tfu-content .tf-error-message object {
  width: 18px;
  height: auto;
  flex-shrink: 0;
  align-self: flex-start;
}
.templately-wrapper .templately-container .templately-contents.templately-frontpage .tf-content-box .tf-user-form .tfu-content .tf-global-error-message {
  border-radius: 6px;
  background: #fbf0f2;
  width: 100%;
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 10px 12px;
  align-items: center;
  flex-shrink: 0;
  margin-bottom: 12px;
  color: #f04438;
  font-size: 14px;
  font-weight: 400;
  line-height: 1.43;
  box-sizing: border-box;
}
.templately-wrapper .templately-container .templately-contents.templately-frontpage .tf-content-box .tf-user-form .tfu-content input:not([type=checkbox]):not([type=radio]) {
  width: 100%;
  padding: 10px 20px;
  background-color: transparent;
  color: #20172f;
  border: 1px solid rgba(222, 220, 236, 0.5);
  border-radius: 6px;
}
.templately-wrapper .templately-container .templately-contents.templately-frontpage .tf-content-box .tf-user-form .tfu-content input:not([type=checkbox]):not([type=radio])::-webkit-input-placeholder {
  color: #707c8e;
}
.templately-wrapper .templately-container .templately-contents.templately-frontpage .tf-content-box .tf-user-form .tfu-content input:not([type=checkbox]):not([type=radio])::-moz-placeholder {
  color: #707c8e;
}
.templately-wrapper .templately-container .templately-contents.templately-frontpage .tf-content-box .tf-user-form .tfu-content input:not([type=checkbox]):not([type=radio])::-ms-input-placeholder {
  color: #707c8e;
}
.templately-wrapper .templately-container .templately-contents.templately-frontpage .tf-content-box .tf-user-form .tfu-content input:not([type=checkbox]):not([type=radio])::-moz-placeholder {
  color: #707c8e;
}
.templately-wrapper .templately-container .templately-contents.templately-frontpage .tf-content-box .tf-user-form .tfu-content input:not([type=checkbox]):not([type=radio]):focus {
  box-shadow: none;
  outline: none;
  border-color: #dedcec;
}
.templately-wrapper .templately-container .templately-contents.templately-frontpage .tf-content-box .tf-user-form .tfu-content .tf-global_signin {
  box-sizing: border-box;
  width: 100%;
  display: flex;
  flex-direction: column;
  border-radius: 6px;
  background: #f6f6fe;
  gap: 12px;
  padding: 16px;
  margin-bottom: 18px;
}
.templately-wrapper .templately-container .templately-contents.templately-frontpage .tf-content-box .tf-user-form .tfu-content .tf-global_signin .global_signin-helptext {
  margin: 0;
  color: #707c8e;
  font-size: 12px;
  font-weight: 400;
  line-height: 1;
}
.templately-wrapper .templately-container .templately-contents.templately-frontpage .tf-content-box .tf-user-form .tfu-content .tfu-consent-wrapper {
  box-sizing: border-box;
  width: 100%;
  display: flex;
  flex-direction: column;
  border-radius: 6px;
  background: #f6f6fe;
  gap: 12px;
  padding: 16px;
  margin-bottom: 18px;
}
.templately-wrapper .templately-container .templately-contents.templately-frontpage .tf-content-box .tf-user-form .tfu-content .tfu-consent-wrapper .tf-input-radio .tf-input-label,
.templately-wrapper .templately-container .templately-contents.templately-frontpage .tf-content-box .tf-user-form .tfu-content .tfu-consent-wrapper .tf-input-checkbox .tf-input-label {
  color: #707c8e;
}
.templately-wrapper .templately-container .templately-contents.templately-frontpage .tf-content-box .tf-user-form .tfu-content .tfu-consent-wrapper .tf-input-radio .tf-input-label a,
.templately-wrapper .templately-container .templately-contents.templately-frontpage .tf-content-box .tf-user-form .tfu-content .tfu-consent-wrapper .tf-input-checkbox .tf-input-label a {
  color: #20172f;
  text-decoration: none;
}
.templately-wrapper .templately-container .templately-contents.templately-frontpage .tf-content-box .tf-user-form .tfu-content .tfu-consent-wrapper .tf-input-radio .tf-input-label a:hover,
.templately-wrapper .templately-container .templately-contents.templately-frontpage .tf-content-box .tf-user-form .tfu-content .tfu-consent-wrapper .tf-input-checkbox .tf-input-label a:hover {
  color: #5453f4;
  text-decoration: underline;
}
.templately-wrapper .templately-container .templately-contents.templately-frontpage .tf-content-box .tf-user-form .tfu-content .tfus-footer {
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 16px;
  align-items: center;
}
.templately-wrapper .templately-container .templately-contents.templately-frontpage .tf-content-box .tf-user-form .tfu-content .tfus-footer > span {
  color: #707c8e;
  font-size: 16px;
  font-weight: 500;
  text-align: center;
}
.templately-wrapper .templately-container .templately-contents.templately-frontpage .tf-content-box .tf-user-form .tfu-content .tfus-footer .templately-button {
  margin-top: 0;
  margin-bottom: 0;
}
.templately-wrapper .templately-container .templately-contents.templately-frontpage .tf-content-box .tf-user-form .tfu-content .tfus-footer .templately-button.tb-signin {
  width: 100%;
  min-height: 50px;
  background-color: #5453f4;
  color: #fff;
  padding: 2px 32px;
  border-radius: 50px;
  outline: 0px;
  border: 1px solid #5453f4;
  box-sizing: border-box;
  text-align: center;
  font-size: 16px;
  font-weight: 700;
}
.templately-wrapper .templately-container .templately-contents.templately-frontpage .tf-content-box .tf-user-form .tfu-content .tfus-footer .templately-button.tb-viapi {
  color: #5453f4;
  font-size: 16px;
  font-weight: 500;
  text-decoration-line: underline;
  background-color: transparent;
  border: none;
  width: -moz-fit-content;
  width: fit-content;
}
.templately-wrapper .templately-container .templately-contents.templately-frontpage .tf-content-box .tf-user-form .tfus-link-wrap {
  margin-top: 20px;
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
  justify-content: center;
}
.templately-wrapper .templately-container .templately-contents.templately-frontpage .tf-content-box .tf-user-form .tfus-link-wrap a {
  min-height: 26px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 2px 16px;
  border-radius: 50px;
  background: #f6f6fe;
  color: #5453f4;
  font-size: 12px;
  font-weight: 500;
  line-height: 1.25;
  text-decoration: none;
  cursor: pointer;
}
.templately-wrapper .templately-container .templately-contents.templately-frontpage .tf-content-box .tf-user-form .tfus-link-wrap a:hover {
  background: #5453f4;
  color: #f6f6fe;
}
.templately-wrapper .templately-container .templately-sidebar {
  flex-basis: 265px;
  max-width: 265px;
  min-width: 265px;
  overflow-y: auto;
  background-color: #fff;
  text-align: right;
  position: relative;
  z-index: 11;
  box-shadow: 0 -25px 30px 0px rgba(0, 0, 0, 0.1);
  border-left: 1px solid #f2f2f2;
  scrollbar-width: thin;
  scrollbar-color: rgb(167, 156, 156) rgba(0, 0, 0, 0.1);
}
.templately-wrapper .templately-container .templately-sidebar::-webkit-scrollbar {
  width: 2px;
}
.templately-wrapper .templately-container .templately-sidebar::-webkit-scrollbar-track {
  box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1);
}
.templately-wrapper .templately-container .templately-sidebar::-webkit-scrollbar-thumb {
  background-color: rgb(167, 156, 156);
  border-radius: 5px;
}
.templately-wrapper .templately-container .templately-sidebar::-webkit-scrollbar {
  display: block;
}
.templately-wrapper .templately-container .templately-sidebar.templately-clouds-sidebar {
  display: flex;
  flex-direction: column;
}
.templately-wrapper .templately-container .templately-sidebar.templately-clouds-sidebar .templately-clouds-menu {
  flex: 1 1;
}
.templately-wrapper .templately-container .templately-sidebar.templately-clouds-sidebar .templately-clouds-menu.templately-nav-sidebar li {
  padding: 0px;
}
.templately-wrapper .templately-container .templately-sidebar.templately-clouds-sidebar .templately-clouds-menu.templately-nav-sidebar li a {
  padding: 10px 30px;
}
.templately-wrapper .templately-container .templately-sidebar.templately-clouds-sidebar .templately-clouds-menu.templately-nav-sidebar li svg {
  fill: #979fa8;
}
.templately-wrapper .templately-container .templately-sidebar.templately-clouds-sidebar .templately-clouds-menu.templately-nav-sidebar li.nav-item-active svg {
  fill: #5453fd;
}
.templately-wrapper .templately-container .templately-sidebar.templately-clouds-sidebar .templately-clouds-size {
  font-weight: 500;
  display: flex;
  padding: 30px;
  box-sizing: border-box;
}
.templately-wrapper .templately-container .templately-sidebar.templately-clouds-sidebar .templately-clouds-size > a {
  flex: 1;
  text-align: right;
  text-decoration: none;
  position: relative;
}
.templately-wrapper .templately-container .templately-sidebar.templately-clouds-sidebar .templately-clouds-size > a::before {
  width: 100%;
  font-size: 11px;
  font-weight: 400;
  padding: 10px;
}
.templately-wrapper .templately-container .templately-sidebar.templately-clouds-sidebar .templately-clouds-size > a > div {
  width: 100%;
  height: 6px;
  background-color: #e5eaf0;
  position: relative;
  border-radius: 3px;
  overflow: hidden;
}
.templately-wrapper .templately-container .templately-sidebar.templately-clouds-sidebar .templately-clouds-size > a > div > span {
  position: absolute;
  right: 0px;
  top: 0px;
  width: 0%;
  height: 100%;
  background-color: #6072ff;
  display: block;
}
.templately-wrapper .templately-container .templately-sidebar.templately-clouds-sidebar .templately-clouds-size > a p {
  margin: 0;
  font-size: 16px;
  color: #031d3c;
  margin-bottom: 6px;
}
.templately-wrapper .templately-container .templately-sidebar.templately-clouds-sidebar .templately-clouds-size > a > p:last-of-type {
  font-size: 12px;
  color: #979fa8;
  margin-bottom: 0px;
  margin-top: 5px;
}
.templately-wrapper .templately-container .templately-sidebar.templately-templates-sidebar {
  display: flex;
  flex-direction: column;
}
.templately-screen-templately.templately-wrapper .templately-container .templately-sidebar.templately-templates-sidebar {
  min-height: calc(100vh - 141px);
}

.templately-wrapper .templately-container .templately-sidebar.templately-templates-sidebar .ts-single {
  padding: 20px 30px;
  background-color: #ffffff;
}
.templately-wrapper .templately-container .templately-sidebar.templately-templates-sidebar .ts-single:not(:first-of-type) {
  border-top: 1px solid #f2f5f7;
}
.templately-wrapper .templately-container .templately-sidebar.templately-templates-sidebar .templately-dependencies.ts-single {
  padding: 0;
}
.templately-wrapper .templately-container .templately-sidebar.templately-templates-sidebar .templately-dependencies .td-single {
  padding: 20px 30px;
  background-color: #ffffff;
}
.templately-wrapper .templately-container .templately-sidebar.templately-templates-sidebar .templately-dependencies .td-single:last-of-type {
  border-top: 1px solid #f2f5f7;
}
.templately-wrapper .templately-container .templately-sidebar.templately-templates-sidebar .templately-dependencies .td-single .t-dependencies-header {
  display: flex;
  align-items: center;
}
.templately-wrapper .templately-container .templately-sidebar.templately-templates-sidebar .templately-dependencies .td-single .t-dependencies-header .td-batch-select {
  display: flex;
  width: 100%;
  position: relative;
  justify-content: space-between;
  align-items: center;
}
.templately-wrapper .templately-container .templately-sidebar.templately-templates-sidebar .templately-dependencies .td-single .t-dependencies-header .td-batch-select::before {
  position: absolute;
  content: "";
  right: 0;
  top: 50%;
  width: 12px;
  height: 12px;
  border-radius: 2px;
  border: 1px solid #b6bdc7;
  background: #fff;
  margin-top: -7px;
}
.templately-wrapper .templately-container .templately-sidebar.templately-templates-sidebar .templately-dependencies .td-single .t-dependencies-header .td-batch-select[aria-checked=include]::before, .templately-wrapper .templately-container .templately-sidebar.templately-templates-sidebar .templately-dependencies .td-single .t-dependencies-header .td-batch-select[aria-checked=mixed]::before {
  background-color: #5d4fff;
  border-color: #5d4fff;
}
.templately-wrapper .templately-container .templately-sidebar.templately-templates-sidebar .templately-dependencies .td-single .t-dependencies-header .td-batch-select > span.tri-state-all-checkbox {
  cursor: pointer;
  padding-right: 25px;
  position: relative;
  z-index: 9;
  font-weight: 600;
  font-size: 14px;
}
.templately-wrapper .templately-container .templately-sidebar.templately-templates-sidebar .templately-dependencies .td-single .t-dependencies-header .td-batch-select > span.tri-state-all-checkbox::before, .templately-wrapper .templately-container .templately-sidebar.templately-templates-sidebar .templately-dependencies .td-single .t-dependencies-header .td-batch-select > span.tri-state-all-checkbox::after {
  content: "";
  position: absolute;
}
.templately-wrapper .templately-container .templately-sidebar.templately-templates-sidebar .templately-dependencies .td-single .t-dependencies-header .td-batch-select > span.tri-state-all-checkbox[aria-checked=include]::after {
  position: absolute;
  content: "";
  transform: rotate(-45deg);
  border-bottom: 2px solid #fff;
  border-left: 2px solid #fff;
  height: 8px;
  width: 4px;
  right: 4px;
  top: 50%;
  margin-top: -6px;
}
.templately-wrapper .templately-container .templately-sidebar.templately-templates-sidebar .templately-dependencies .td-single .t-dependencies-header .td-batch-select > span.tri-state-all-checkbox[aria-checked=exclude]::after, .templately-wrapper .templately-container .templately-sidebar.templately-templates-sidebar .templately-dependencies .td-single .t-dependencies-header .td-batch-select > span.tri-state-all-checkbox[aria-checked=exclude]::before {
  position: absolute;
  content: "";
  height: 12px;
  width: 2px;
  background-color: #d81c1c;
  right: 6px;
  top: 50%;
  margin-top: -6px;
}
.templately-wrapper .templately-container .templately-sidebar.templately-templates-sidebar .templately-dependencies .td-single .t-dependencies-header .td-batch-select > span.tri-state-all-checkbox[aria-checked=exclude]::after {
  transform: rotate(-45deg);
}
.templately-wrapper .templately-container .templately-sidebar.templately-templates-sidebar .templately-dependencies .td-single .t-dependencies-header .td-batch-select > span.tri-state-all-checkbox[aria-checked=exclude]::before {
  transform: rotate(45deg);
}
.templately-wrapper .templately-container .templately-sidebar.templately-templates-sidebar .templately-dependencies .td-single .t-dependencies-header .td-batch-select > span.tri-state-all-checkbox[aria-checked=mixed]::before {
  position: absolute;
  content: "";
  height: 12px;
  width: 2px;
  background-color: #fff;
  right: 6px;
  top: 50%;
  margin-top: -6px;
}
.templately-wrapper .templately-container .templately-sidebar.templately-templates-sidebar .templately-dependencies .td-single .t-dependencies-header .td-batch-select > span.tri-state-all-checkbox[aria-checked=mixed]::before {
  transform: rotate(45deg);
}
.templately-wrapper .templately-container .templately-sidebar.templately-templates-sidebar .templately-dependencies .td-single .t-dependencies-header .td-batch-select > span.tsdf-actions {
  display: inherit;
}
.templately-wrapper .templately-container .templately-sidebar.templately-templates-sidebar .templately-dependencies .td-single ul.t-dependencies-list {
  height: 0;
  margin: 0;
  padding-top: 20px;
  display: none;
  overflow: hidden;
  transition: all 300ms ease 0s;
}
.templately-wrapper .templately-container .templately-sidebar.templately-templates-sidebar .templately-dependencies .td-single ul.t-dependencies-list li {
  margin-bottom: 15px;
  position: relative;
}
.templately-wrapper .templately-container .templately-sidebar.templately-templates-sidebar .templately-dependencies .td-single ul.t-dependencies-list li:last-child {
  margin-bottom: 0px;
}
.templately-wrapper .templately-container .templately-sidebar.templately-templates-sidebar .templately-dependencies .td-single ul.t-dependencies-list li::before {
  position: absolute;
  content: "";
  right: 0;
  top: 50%;
  width: 12px;
  height: 12px;
  border-radius: 2px;
  border: 1px solid #b6bdc7;
  background: #fff;
  margin-top: -7px;
}
.templately-wrapper .templately-container .templately-sidebar.templately-templates-sidebar .templately-dependencies .td-single ul.t-dependencies-list li input {
  margin-left: 5px;
  display: none;
}
.templately-wrapper .templately-container .templately-sidebar.templately-templates-sidebar .templately-dependencies .td-single ul.t-dependencies-list li label {
  padding-right: 25px;
  position: relative;
  display: inline-block;
  font-size: 14px;
  color: #6d7c90;
}
.templately-wrapper .templately-container .templately-sidebar.templately-templates-sidebar .templately-dependencies .td-single ul.t-dependencies-list li label:hover {
  cursor: pointer;
}
.templately-wrapper .templately-container .templately-sidebar.templately-templates-sidebar .templately-dependencies .td-single ul.t-dependencies-list li label > span {
  margin-right: 22px;
  display: inline-block;
}
.templately-wrapper .templately-container .templately-sidebar.templately-templates-sidebar .templately-dependencies .td-single ul.t-dependencies-list li.tri-state-include::before {
  background-color: #5d4fff;
  border-color: #5d4fff;
}
.templately-wrapper .templately-container .templately-sidebar.templately-templates-sidebar .templately-dependencies .td-single ul.t-dependencies-list li.tri-state-include label::after {
  position: absolute;
  content: "";
  transform: rotate(-45deg);
  border-bottom: 2px solid #fff;
  border-left: 2px solid #fff;
  height: 8px;
  width: 4px;
  right: 4px;
  top: 50%;
  margin-top: -7px;
}
.templately-wrapper .templately-container .templately-sidebar.templately-templates-sidebar .templately-dependencies .td-single ul.t-dependencies-list li.tri-state-exclude label::after, .templately-wrapper .templately-container .templately-sidebar.templately-templates-sidebar .templately-dependencies .td-single ul.t-dependencies-list li.tri-state-exclude label::before {
  position: absolute;
  content: "";
  height: 12px;
  width: 2px;
  background-color: #d81c1c;
  right: 6px;
  top: 50%;
  margin-top: -7px;
}
.templately-wrapper .templately-container .templately-sidebar.templately-templates-sidebar .templately-dependencies .td-single ul.t-dependencies-list li.tri-state-exclude label::after {
  transform: rotate(-45deg);
}
.templately-wrapper .templately-container .templately-sidebar.templately-templates-sidebar .templately-dependencies .td-single ul.t-dependencies-list li.tri-state-exclude label::before {
  transform: rotate(45deg);
}
.templately-wrapper .templately-container .templately-sidebar.templately-templates-sidebar .templately-dependencies .td-single.tsdf-open {
  transition: all 300ms ease 0s;
}
.templately-wrapper .templately-container .templately-sidebar.templately-templates-sidebar .templately-dependencies .td-single.tsdf-open > ul.t-dependencies-list {
  height: auto;
  display: block;
  transition: all 300ms ease 0s;
}
.templately-wrapper .templately-container .templately-sidebar.templately-templates-sidebar .templately-tags.ts-single {
  padding-bottom: 120px;
}
.templately-wrapper .templately-container .templately-sidebar.templately-templates-sidebar .templately-tags .templately-tags__placeholder {
  color: #6d7c90;
  font-size: 12px;
  font-weight: 500;
}
.templately-wrapper .templately-container .templately-sidebar.templately-templates-sidebar .templately-tags > label {
  font-size: 14px;
  font-weight: 600;
}
.templately-wrapper .templately-container .templately-sidebar.templately-templates-sidebar .templately-tags > label + div {
  margin-top: 10px;
}
.templately-wrapper .templately-container .templately-sidebar.templately-templates-sidebar .templately-tags .templately-tags__menu {
  position: absolute;
  z-index: 9;
  bottom: 100%;
  top: auto;
}
.templately-wrapper .templately-container .templately-sidebar.templately-templates-sidebar .templately-tags input.templately-tags__input {
  box-shadow: none;
}
.templately-wrapper .templately-container .templately-sidebar.templately-templates-sidebar .templately-sidebar-filter-buttons {
  flex: 1;
  position: sticky;
  z-index: 9999;
  bottom: 0px;
  padding-top: 100px;
  overflow: hidden;
}
.templately-screen-templately.templately-wrapper .templately-container .templately-sidebar.templately-templates-sidebar .templately-sidebar-filter-buttons {
  width: 263px;
  position: fixed;
}

.templately-wrapper .templately-container .templately-sidebar.templately-templates-sidebar .templately-sidebar-filter-buttons .templately-filter-buttons {
  position: absolute;
  bottom: 0;
  right: 0;
  left: 0;
  box-shadow: 0 15px 40px -10px rgba(0, 0, 0, 0.5);
  border-bottom: 1px solid #f2f2f2;
}
.templately-wrapper .templately-container .templately-contents {
  flex-basis: 100%;
  overflow-y: scroll;
  padding-top: 30px;
  background-image: linear-gradient(-250deg, rgba(95, 90, 248, 0.06), transparent 90%);
  scrollbar-width: thin;
  scrollbar-color: rgb(167, 156, 156) rgba(0, 0, 0, 0.1);
}
.templately-wrapper .templately-container .templately-contents a:focus,
.templately-wrapper .templately-container .templately-contents a:hover {
  box-shadow: none;
  outline: none;
  border: none;
}
.templately-wrapper .templately-container .templately-contents.templately-sidebar-is-sticky {
  padding-right: 263px;
}
@media only screen and (max-width: 1450px) {
  .templately-wrapper .templately-container .templately-contents.templately-search-results .templately-contents-header .templately-header-title h3 {
    font-size: 16px;
  }
}
.templately-wrapper .templately-container .templately-contents::-webkit-scrollbar {
  width: 2px;
}
.templately-wrapper .templately-container .templately-contents::-webkit-scrollbar-track {
  box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1);
}
.templately-wrapper .templately-container .templately-contents::-webkit-scrollbar-thumb {
  background-color: rgb(167, 156, 156);
  border-radius: 5px;
}
.templately-wrapper .templately-container .templately-contents .templately-contents-header {
  display: flex;
  align-items: center;
  padding: 0 30px 0px;
  margin-bottom: 30px;
  transition: top 300ms ease-in 0ms;
  background: none;
  flex-wrap: wrap;
}
.templately-wrapper .templately-container .templately-contents .templately-contents-header .templately-contents-header-inner {
  width: 100%;
  display: flex;
  align-items: center;
}
.templately-wrapper .templately-container .templately-contents .templately-contents-header .templately-header-title {
  flex: 1;
  text-align: right;
}
.templately-wrapper .templately-container .templately-contents .templately-contents-header .templately-header-title h3 {
  margin: 0px;
  font-size: 24px;
  font-weight: 500;
  color: #000000;
  text-transform: capitalize;
}
.templately-wrapper .templately-container .templately-contents .templately-contents-header .templately-plan-switcher button.components-button {
  margin-right: 5px;
  border: 0px;
  padding: 9px 20px;
  background-color: #fff;
  text-transform: uppercase;
  border-radius: 3px;
  transition: background 200ms ease 0s;
  box-shadow: 0 0 25px -10px rgba(0, 0, 0, 0.3);
}
.templately-wrapper .templately-container .templately-contents .templately-contents-header .templately-plan-switcher button.components-button:focus, .templately-wrapper .templately-container .templately-contents .templately-contents-header .templately-plan-switcher button.components-button:hover, .templately-wrapper .templately-container .templately-contents .templately-contents-header .templately-plan-switcher button.components-button.templately-plan-active {
  background-color: #5453fd;
  color: #fff !important;
  outline: none;
  cursor: pointer;
}
.templately-wrapper .templately-container .templately-contents .templately-contents-header .templately-plan-switcher button.components-button.templately-plan-starter {
  color: #5453fd;
}
.templately-wrapper .templately-container .templately-contents .templately-contents-header .templately-plan-switcher button.components-button.templately-plan-pro {
  color: #ff7a8e;
}
.templately-wrapper .templately-container .templately-contents .templately-contents-header.templately-sticky {
  position: sticky;
  top: -30px;
  right: 0;
  z-index: 10;
  background: #fff;
  padding: 20px 30px;
  box-shadow: 0 0 30px 0px rgba(0, 0, 0, 0.25);
  transition: all 0.3s ease 0s;
}
.templately-wrapper .templately-container .templately-contents .templately-contents-header .templately-cloud-actions {
  display: flex;
}
.templately-wrapper .templately-container .templately-contents .templately-contents-header .templately-cloud-actions .templately-cloud-search {
  display: flex;
  align-items: center;
  background-color: #fff;
  border-radius: 42px;
  overflow: hidden;
}
.templately-wrapper .templately-container .templately-contents .templately-contents-header .templately-cloud-actions .templately-cloud-search .templately-cloud-search-btn {
  cursor: pointer;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  line-height: 40px;
  text-align: center;
  border: 1px solid #fff;
  background-color: #f2f2f2;
}
.templately-wrapper .templately-container .templately-contents .templately-contents-header .templately-cloud-actions .templately-cloud-search .templately-cloud-search-btn svg {
  fill: #000;
}
.templately-wrapper .templately-container .templately-contents .templately-contents-header .templately-cloud-actions .templately-cloud-search .templately-cloud-search-btn.active {
  background-color: #ff7f7f;
}
.templately-wrapper .templately-container .templately-contents .templately-contents-header .templately-cloud-actions .templately-cloud-search .templately-cloud-search-btn.active svg {
  fill: #fff;
}
.templately-wrapper .templately-container .templately-contents .templately-contents-header .templately-cloud-actions .templately-cloud-search input {
  flex: 1;
  padding: 6px 15px;
  border: 0px;
  background-color: #fff;
  color: inherit;
}
.templately-wrapper .templately-container .templately-contents .templately-contents-header .templately-cloud-actions .templately-cloud-search input:focus {
  outline: none;
  box-shadow: none;
  border: 0px;
}
.templately-wrapper .templately-container .templately-contents .templately-contents-header .templately-cloud-actions .templately-view-buttons {
  display: flex;
  justify-content: flex-end;
}
.templately-wrapper .templately-container .templately-contents .templately-contents-header .templately-cloud-actions .templately-view-buttons > button {
  height: 40px;
  width: 40px;
  font-size: 16px;
  margin-right: 15px;
  background: transparent;
  border-radius: 50%;
  background-color: #fff;
  border: 0px solid;
  color: #5352fc;
  cursor: pointer;
  transition: background 300ms;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
}
.templately-wrapper .templately-container .templately-contents .templately-contents-header .templately-cloud-actions .templately-view-buttons > button:focus {
  outline: none;
}
.templately-wrapper .templately-container .templately-contents .templately-contents-header .templately-cloud-actions .templately-view-buttons > button:hover, .templately-wrapper .templately-container .templately-contents .templately-contents-header .templately-cloud-actions .templately-view-buttons > button.active {
  background: #5352fc;
  color: #fff;
}
.templately-wrapper .templately-container .templately-contents .templately-contents-header .templately-sticky .templately-cloud-actions .templately-cloud-search {
  border: 1px solid #f2f2f2;
}
.templately-wrapper .templately-container .templately-contents .templately-contents-header .templately-sticky .templately-cloud-actions .templately-view-buttons button {
  border: 1px solid #f2f2f2;
}
.templately-wrapper .templately-container .templately-contents .templately-contents-header .templately-sticky .templately-cloud-actions .templately-view-buttons button:hover {
  border-color: #5352fc;
}
.templately-wrapper .templately-container .templately-contents .templately-items {
  padding-right: 30px;
  padding-left: 30px;
  display: grid;
  flex-wrap: wrap;
  grid-column-gap: 30px;
  grid-template-columns: repeat(auto-fill, minmax(270px, 1fr));
}
.templately-wrapper .templately-container .templately-contents .templately-items.templately-download-items, .templately-wrapper .templately-container .templately-contents .templately-items.templately-has-no-items {
  display: flex;
  padding-right: 15px;
  padding-left: 15px;
}
.templately-wrapper .templately-container .templately-contents .templately-items.templately-download-items .templately-no-items {
  padding-right: 0px;
  padding-left: 0px;
}
.templately-wrapper .templately-container .templately-contents .templately-items .templately-item {
  flex-basis: 25%;
  margin-bottom: 30px;
  box-sizing: border-box;
  display: flex;
}
.templately-wrapper .templately-container .templately-contents .templately-items .templately-item > div.templately-item-inner {
  overflow: hidden;
  height: 100%;
  width: 100%;
  display: inline-flex;
  flex-direction: column;
  border-radius: 10px;
}
.templately-wrapper .templately-container .templately-contents .templately-items .templately-item .templately-item-image-hover-wrapper {
  min-height: 200px;
}
.templately-wrapper .templately-container .templately-contents .templately-items .templately-item:not(.templately-pack-item) > div.templately-item-inner {
  background-color: #fff;
}
.templately-wrapper .templately-container .templately-contents .templately-items .templately-item:not(.templately-pack-item) img.templately-item-image {
  max-height: 300px;
  border-radius: 0px !important;
}
.templately-wrapper .templately-container .templately-contents .templately-items .templately-item:not(.templately-pack-item) div.templately-item-details {
  padding: 20px 17px;
}
.templately-wrapper .templately-container .templately-contents .templately-items .templately-item.templately-pack-item > div.templately-item-inner > a {
  background-color: rgba(95, 90, 248, 0.05);
  padding: 20px;
}
.templately-wrapper .templately-container .templately-contents .templately-items .templately-item.templately-pack-item > div.templately-item-inner > .templately-item-details {
  background-color: #fff;
  padding: 20px 17px;
}
.templately-wrapper .templately-container .templately-contents .templately-items .templately-item .templately-item-image-container {
  position: relative;
}
.templately-wrapper .templately-container .templately-contents .templately-items .templately-item .templately-item-image-container.templately-pack-images {
  position: relative;
  display: flex;
}
.templately-wrapper .templately-container .templately-contents .templately-items .templately-item .templately-item-image-container.templately-pack-images > div {
  display: flex;
  width: calc(100% - 40px);
  border-radius: 5px;
  overflow: hidden;
  box-shadow: 0 0 20px rgba(32, 41, 47, 0.05);
  position: relative;
  transition: all 0.3s ease-in-out 0.1s;
}
.templately-wrapper .templately-container .templately-contents .templately-items .templately-item .templately-item-image-container.templately-pack-images > div:not(:first-of-type) {
  position: absolute;
  z-index: 4;
}
.templately-wrapper .templately-container .templately-contents .templately-items .templately-item .templately-item-image-container.templately-pack-images > div:first-of-type {
  margin-right: 40px;
  margin-bottom: 40px;
  transform-origin: bottom left;
}
.templately-wrapper .templately-container .templately-contents .templately-items .templately-item .templately-item-image-container.templately-pack-images > div:nth-child(2) {
  top: 20px;
  left: 20px;
  transform-origin: top center;
}
.templately-wrapper .templately-container .templately-contents .templately-items .templately-item .templately-item-image-container.templately-pack-images > div:nth-child(3) {
  bottom: 0;
  right: 0;
  transform-origin: center right;
}
.templately-wrapper .templately-container .templately-contents .templately-items .templately-item .templately-item-image-container .templately-item-hover {
  position: absolute;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.4);
  display: flex;
  align-items: center;
  justify-content: center;
  visibility: hidden;
  opacity: 0;
  transition: all 0.3s ease-in 0s;
}
.templately-wrapper .templately-container .templately-contents .templately-items .templately-item .templately-item-image-container .templately-item-hover .templately-item-dependencies {
  position: absolute;
  bottom: 5px;
  background: rgba(255, 255, 255, 0.9);
  width: auto;
  display: flex;
  padding: 5px 20px;
  box-sizing: border-box;
  justify-content: center;
  align-items: center;
  border-radius: 5px;
}
.templately-wrapper .templately-container .templately-contents .templately-items .templately-item .templately-item-image-container .templately-item-hover .templately-item-dependencies .templately-dependency-icon {
  height: 30px;
  width: 30px;
  min-width: 30px;
  border-radius: 50px;
  display: flex;
  justify-content: center;
  align-items: center;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  line-height: 30px;
  cursor: pointer;
}
.templately-wrapper .templately-container .templately-contents .templately-items .templately-item .templately-item-image-container .templately-item-hover .templately-item-dependencies .templately-dependency-icon:not(:last-child) {
  margin-left: 5px;
}
.templately-wrapper .templately-container .templately-contents .templately-items .templately-item .templately-item-image-container .templately-item-hover .templately-item-dependencies .templately-dependency-icon img {
  width: 100%;
  height: 100%;
}
.templately-wrapper .templately-container .templately-contents .templately-items .templately-item .templately-item-image-container .templately-item-hover .templately-item-dependencies .templately-dependency-icon .templately-dependency-icon-text-icon {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: #6d7c90;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
}
.templately-wrapper .templately-container .templately-contents .templately-items .templately-item .templately-item-image-container .templately-item-hover > span.templately-item-dependencies {
  padding: 10px 20px;
  display: inherit;
}
.templately-wrapper .templately-container .templately-contents .templately-items .templately-item .templately-item-image-container .templately-item-hover > span.templately-item-dependencies > span {
  cursor: pointer;
}
.templately-wrapper .templately-container .templately-contents .templately-items .templately-item .templately-item-image-container img.templately-item-image {
  display: block;
  -o-object-fit: cover;
     object-fit: cover;
  -o-object-position: top;
     object-position: top;
  width: 100%;
  border-radius: 10px;
}
.templately-wrapper .templately-container .templately-contents .templately-items .templately-item .templately-item-details {
  padding: 10px 17px;
  transition: all 0.3s ease-in 0s;
  position: relative;
  flex: 1;
  border-top: 1px solid #f2f2f2;
}
.templately-wrapper .templately-container .templately-contents .templately-items .templately-item .templately-item-details > div {
  display: flex;
  align-items: stretch;
  justify-content: center;
}
.templately-wrapper .templately-container .templately-contents .templately-items .templately-item .templately-item-details .templately-item-meta > .templately-item-meta-single {
  margin-left: 5px;
  margin-right: 5px;
  background-color: #fff;
}
.templately-wrapper .templately-container .templately-contents .templately-items .templately-item .templately-item-details .templately-item-meta > .templately-item-meta-single.tb-full-site-import {
  background-color: #EAEAFF;
  color: #5453fd;
  padding: 5px 10px;
  font-size: 12px;
  font-weight: 500;
  align-self: center;
  border-radius: 3px;
}
.templately-wrapper .templately-container .templately-contents .templately-items .templately-item .templately-item-details .templately-item-meta > .templately-item-meta-single.tb-item-price {
  border: 1px solid #60c281;
  border-radius: 3px;
  padding: 0px 10px;
  color: #60c281;
  font-size: 12px;
  line-height: 12px;
  display: flex;
  align-items: center;
}
.templately-wrapper .templately-container .templately-contents .templately-items .templately-item .templately-item-details .templately-item-meta > .templately-item-meta-single.tb-item-price > a {
  color: #60c281;
  text-decoration: none;
}
.templately-wrapper .templately-container .templately-contents .templately-items .templately-item .templately-item-details .templately-item-meta > button.templately-item-meta-single {
  border: 1px solid #ecf0f7;
  border-radius: 3px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 5px 10px;
  color: #6d7c90;
}
.templately-wrapper .templately-container .templately-contents .templately-items .templately-item .templately-item-details .templately-item-meta > button.templately-item-meta-single > svg {
  margin-left: 6px;
}
.templately-wrapper .templately-container .templately-contents .templately-items .templately-item .templately-item-details .templately-item-meta > button.templately-item-meta-single.tb-item-rating {
  text-transform: uppercase;
  color: #6d7c90;
}
.templately-wrapper .templately-container .templately-contents .templately-items .templately-item .templately-item-details .templately-item-meta > button.templately-item-meta-single.tb-item-rating svg {
  color: #ffb45a;
}
.templately-wrapper .templately-container .templately-contents .templately-items .templately-item .templately-item-details .templately-item-meta > button.templately-item-meta-single.tb-item-rating.not-rated svg {
  color: #6d7c90;
}
.templately-wrapper .templately-container .templately-contents .templately-items .templately-item .templately-item-details .templately-item-meta > button.templately-item-meta-single.tb-item-favourite {
  cursor: pointer;
}
.templately-wrapper .templately-container .templately-contents .templately-items .templately-item .templately-item-details .templately-item-meta > button.templately-item-meta-single.tb-item-favourite svg {
  color: #6d7c90;
}
.templately-wrapper .templately-container .templately-contents .templately-items .templately-item .templately-item-details .templately-item-meta > button.templately-item-meta-single.tb-item-favourite.my-favourite svg {
  color: #ff3190;
}
.templately-wrapper .templately-container .templately-contents .templately-items .templately-item .templately-item-details .templately-item-meta > button.templately-item-meta-single.tb-item-insert {
  text-transform: uppercase;
  color: #000;
}
.templately-wrapper .templately-container .templately-contents .templately-items .templately-item .templately-item-details .templately-item-meta > button.templately-item-meta-single.tb-item-insert svg {
  color: #5453fd;
}
.templately-wrapper .templately-container .templately-contents .templately-items .templately-item .templately-item-details .templately-title {
  text-align: center;
  text-decoration: none;
  color: #333;
}
.templately-wrapper .templately-container .templately-contents .templately-items .templately-item .templately-item-details .templately-title > h4 {
  margin-bottom: 0;
  margin-top: 15px;
}
.templately-wrapper .templately-container .templately-contents .templately-items .templately-item .templately-item-details .templately-button {
  font-size: 12px;
  display: flex;
  align-items: center;
  padding: 6px 0px;
}
.templately-wrapper .templately-container .templately-contents .templately-items .templately-item:hover .templately-item-hover {
  visibility: visible;
  opacity: 1;
}
.templately-wrapper .templately-container .templately-contents .templately-items .templately-item:hover .templately-item-image-container.templately-pack-images > div {
  box-shadow: 0 0 80px -30px rgba(0, 0, 0, 0.5);
  transform: scale(0.6);
}
.templately-wrapper .templately-container .templately-contents .templately-items .templately-item:hover .templately-item-image-container.templately-pack-images > div:first-of-type {
  margin: 40px 40px 0 0;
}
.templately-wrapper .templately-container .templately-contents .templately-items .templately-item:hover .templately-item-image-container.templately-pack-images > div:nth-child(2) {
  top: 0;
}
.templately-wrapper .templately-container .templately-contents .templately-items .templately-item:hover .templately-item-image-container.templately-pack-images > div:nth-child(3) {
  right: 0;
}
.templately-wrapper .templately-container .templately-contents .templatey-cloud-header {
  display: flex;
  align-items: center;
}
.templately-wrapper .templately-container .templately-contents .templatey-cloud-header > img {
  margin-left: 10px;
  width: 40px;
  height: 40px;
  -o-object-fit: cover;
     object-fit: cover;
}
.templately-wrapper .templately-container .templately-contents .templately-my-clouds,
.templately-wrapper .templately-container .templately-contents .templately-workspaces {
  padding-right: 30px;
  padding-left: 30px;
  padding-bottom: 30px;
}
.templately-wrapper .templately-container .templately-contents .templately-my-clouds.templately-has-no-items,
.templately-wrapper .templately-container .templately-contents .templately-workspaces.templately-has-no-items {
  padding: 0 15px 30px;
  box-sizing: border-box;
}
.templately-wrapper .templately-container .templately-contents .templately-workspaces .templately-workspaces-row {
  flex-wrap: wrap;
  margin-right: -15px;
  margin-left: -15px;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
}
.templately-wrapper .templately-container .templately-contents .templately-workspaces .templately-workspaces-row .templately-workspaces-column {
  padding-right: 15px;
  padding-left: 15px;
  flex-basis: 25%;
  box-sizing: border-box;
}
.templately-wrapper .templately-container .templately-contents .templately-workspaces .templately-workspaces-row .templately-workspaces-column.templately-widefat {
  flex-basis: 100%;
}
.templately-wrapper .templately-container .templately-contents .templately-workspaces .templately-workspaces-row .templately-workspaces-column.templately-widefat .templately-workspace {
  min-height: auto;
  display: block;
}
.templately-wrapper .templately-container .templately-contents .templately-workspaces .templately-workspaces-row .templately-workspaces-column.templately-widefat .templately-workspace .templately-workspace-inner {
  flex-direction: row;
}
.templately-wrapper .templately-container .templately-contents .templately-workspaces .templately-workspaces-row .templately-workspaces-column.templately-widefat .templately-workspace .templately-workspace-inner > * {
  flex: 1;
}
.templately-wrapper .templately-container .templately-contents .templately-workspaces .templately-workspaces-row .templately-workspaces-column.templately-widefat .templately-workspace header {
  display: flex;
  align-items: center;
}
.templately-wrapper .templately-container .templately-contents .templately-workspaces .templately-workspaces-row .templately-workspaces-column.templately-widefat .templately-workspace header > .tw-title {
  margin-bottom: 0;
}
.templately-wrapper .templately-container .templately-contents .templately-workspaces .templately-workspaces-row .templately-workspaces-column.templately-widefat .templately-workspace header .tw-icon {
  margin-left: 15px;
  margin-bottom: 0px;
}
.templately-wrapper .templately-container .templately-contents .templately-workspaces .templately-workspaces-row .templately-workspaces-column.templately-widefat .templately-workspace.templately-create-workspace .templately-workspace-inner {
  flex-direction: column;
}
.templately-wrapper .templately-container .templately-contents .templately-workspaces .templately-workspaces-row .templately-workspaces-column.templately-widefat .templately-workspace.templately-create-workspace .templately-workspace-inner .tw-icon {
  min-height: 31px;
}
.templately-wrapper .templately-container .templately-contents .templately-workspaces .templately-workspace {
  background-color: #fff;
  padding: 20px;
  min-height: 194px;
  border-radius: 10px;
  box-sizing: border-box;
  margin-bottom: 30px;
  text-align: right;
  transition: background 300ms ease 0s;
  display: flex;
}
.templately-wrapper .templately-container .templately-contents .templately-workspaces .templately-workspace.templately-create-workspace {
  background: #f7f9fc;
  border: 3px solid #fff;
  box-sizing: border-box;
  cursor: pointer;
}
.templately-wrapper .templately-container .templately-contents .templately-workspaces .templately-workspace.templately-create-workspace .templately-workspace-inner {
  padding: 30px;
  box-sizing: border-box;
  border: 2px dashed #e5e8ec;
  border-radius: 10px;
  width: 100%;
  align-items: center;
  justify-content: center;
}
.templately-wrapper .templately-container .templately-contents .templately-workspaces .templately-workspace.templately-create-workspace .tw-title {
  color: #979fa8;
}
.templately-wrapper .templately-container .templately-contents .templately-workspaces .templately-workspace.templately-create-workspace:hover {
  background: #f7f9fc;
}
.templately-wrapper .templately-container .templately-contents .templately-workspaces .templately-workspace.templately-create-workspace:hover .tw-icon {
  background-image: url(../images/icon-workspace.png);
}
.templately-wrapper .templately-container .templately-contents .templately-workspaces .templately-workspace.templately-create-workspace.templately-no-workspaces {
  cursor: initial;
}
.templately-wrapper .templately-container .templately-contents .templately-workspaces .templately-workspace.templately-create-workspace.templately-no-workspaces .tw-icon {
  background-image: url(../images/not-found.svg);
}
.templately-wrapper .templately-container .templately-contents .templately-workspaces .templately-workspace .tw-share-with > p {
  margin: 0;
  font-size: 10px;
  margin-bottom: 5px;
}
.templately-wrapper .templately-container .templately-contents .templately-workspaces .templately-workspace .tw-share-with .tw-share-with-list {
  display: inline-flex;
  align-items: center;
}
.templately-wrapper .templately-container .templately-contents .templately-workspaces .templately-workspace .tw-share-with .tw-share-with-list > button, .templately-wrapper .templately-container .templately-contents .templately-workspaces .templately-workspace .tw-share-with .tw-share-with-list > img, .templately-wrapper .templately-container .templately-contents .templately-workspaces .templately-workspace .tw-share-with .tw-share-with-list > span#tw-shared-image-wrapper > img, .templately-wrapper .templately-container .templately-contents .templately-workspaces .templately-workspace .tw-share-with .tw-share-with-list > span {
  background: #e4e7ec;
  border: 1px solid #fff;
  box-shadow: 0 0 10px -5px #000;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  padding: 0;
  margin: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #6072ff;
}
.templately-wrapper .templately-container .templately-contents .templately-workspaces .templately-workspace .tw-share-with .tw-share-with-list > button:nth-child(2), .templately-wrapper .templately-container .templately-contents .templately-workspaces .templately-workspace .tw-share-with .tw-share-with-list > img:nth-child(2), .templately-wrapper .templately-container .templately-contents .templately-workspaces .templately-workspace .tw-share-with .tw-share-with-list > span#tw-shared-image-wrapper > img:nth-child(2), .templately-wrapper .templately-container .templately-contents .templately-workspaces .templately-workspace .tw-share-with .tw-share-with-list > span:nth-child(2) {
  transform: translateX(10px);
}
.templately-wrapper .templately-container .templately-contents .templately-workspaces .templately-workspace .tw-share-with .tw-share-with-list > button:nth-child(3), .templately-wrapper .templately-container .templately-contents .templately-workspaces .templately-workspace .tw-share-with .tw-share-with-list > img:nth-child(3), .templately-wrapper .templately-container .templately-contents .templately-workspaces .templately-workspace .tw-share-with .tw-share-with-list > span#tw-shared-image-wrapper > img:nth-child(3), .templately-wrapper .templately-container .templately-contents .templately-workspaces .templately-workspace .tw-share-with .tw-share-with-list > span:nth-child(3) {
  transform: translateX(20px);
}
.templately-wrapper .templately-container .templately-contents .templately-workspaces .templately-workspace .tw-share-with .tw-share-with-list > button:nth-child(4), .templately-wrapper .templately-container .templately-contents .templately-workspaces .templately-workspace .tw-share-with .tw-share-with-list > img:nth-child(4), .templately-wrapper .templately-container .templately-contents .templately-workspaces .templately-workspace .tw-share-with .tw-share-with-list > span#tw-shared-image-wrapper > img:nth-child(4), .templately-wrapper .templately-container .templately-contents .templately-workspaces .templately-workspace .tw-share-with .tw-share-with-list > span:nth-child(4) {
  transform: translateX(30px);
}
.templately-wrapper .templately-container .templately-contents .templately-workspaces .templately-workspace .tw-share-with .tw-share-with-list > button:nth-child(5), .templately-wrapper .templately-container .templately-contents .templately-workspaces .templately-workspace .tw-share-with .tw-share-with-list > img:nth-child(5), .templately-wrapper .templately-container .templately-contents .templately-workspaces .templately-workspace .tw-share-with .tw-share-with-list > span#tw-shared-image-wrapper > img:nth-child(5), .templately-wrapper .templately-container .templately-contents .templately-workspaces .templately-workspace .tw-share-with .tw-share-with-list > span:nth-child(5) {
  transform: translateX(40px);
}
.templately-wrapper .templately-container .templately-contents .templately-workspaces .templately-workspace .tw-share-with .tw-share-with-list > button > svg, .templately-wrapper .templately-container .templately-contents .templately-workspaces .templately-workspace .tw-share-with .tw-share-with-list > img > svg, .templately-wrapper .templately-container .templately-contents .templately-workspaces .templately-workspace .tw-share-with .tw-share-with-list > span#tw-shared-image-wrapper > img > svg, .templately-wrapper .templately-container .templately-contents .templately-workspaces .templately-workspace .tw-share-with .tw-share-with-list > span > svg {
  fill: #6072ff;
}
.templately-wrapper .templately-container .templately-contents .templately-workspaces .templately-workspace .tw-share-with .tw-share-with-list > button:focus, .templately-wrapper .templately-container .templately-contents .templately-workspaces .templately-workspace .tw-share-with .tw-share-with-list > img:focus, .templately-wrapper .templately-container .templately-contents .templately-workspaces .templately-workspace .tw-share-with .tw-share-with-list > span#tw-shared-image-wrapper > img:focus, .templately-wrapper .templately-container .templately-contents .templately-workspaces .templately-workspace .tw-share-with .tw-share-with-list > span:focus {
  outline: none;
}
.templately-wrapper .templately-container .templately-contents .templately-workspaces .templately-workspace .tw-share-with .tw-share-with-list > button {
  width: 42px;
  height: 42px;
}
.templately-wrapper .templately-container .templately-contents .templately-workspaces .templately-workspace .tw-share-with .tw-share-with-list > button:not(:first-child):nth-child(2) {
  transform: translateX(10px);
}
.templately-wrapper .templately-container .templately-contents .templately-workspaces .templately-workspace .tw-share-with .tw-share-with-list > button:not(:first-child):nth-child(3) {
  transform: translateX(20px);
}
.templately-wrapper .templately-container .templately-contents .templately-workspaces .templately-workspace .tw-share-with .tw-share-with-list > button:not(:first-child):nth-child(4) {
  transform: translateX(30px);
}
.templately-wrapper .templately-container .templately-contents .templately-workspaces .templately-workspace .tw-share-with .tw-share-with-list > button:not(:first-child):nth-child(5) {
  transform: translateX(40px);
}
.templately-wrapper .templately-container .templately-contents .templately-workspaces .templately-workspace .templately-workspace-inner {
  display: flex;
  flex-direction: column;
  position: relative;
  width: 100%;
}
.templately-wrapper .templately-container .templately-contents .templately-workspaces .templately-workspace .templately-workspace-inner .templately-context-menu {
  position: absolute;
}
.templately-wrapper .templately-container .templately-contents .templately-workspaces .templately-workspace .templately-workspace-inner > header {
  flex: 1;
}
.templately-wrapper .templately-container .templately-contents .templately-workspaces .templately-workspace .templately-workspace-inner > header > .tw-title {
  margin-bottom: 15px;
  font-size: 16px;
  font-weight: 500;
}
.templately-wrapper .templately-container .templately-contents .templately-workspaces .templately-workspace .tw-icon {
  background-image: url(../images/icon-workspace.png);
  width: 40px;
  height: 31px;
  display: block;
  margin-bottom: 15px;
  background-size: contain;
  background-repeat: no-repeat;
}
.templately-wrapper .templately-container .templately-contents .templately-workspaces .templately-workspace:hover, .templately-wrapper .templately-container .templately-contents .templately-workspaces .templately-workspace.templately-active {
  cursor: pointer;
  background: linear-gradient(-135deg, rgb(96, 114, 255) 50%, rgb(108, 125, 255) 50%);
  color: #fff;
}
.templately-wrapper .templately-container .templately-contents .templately-workspaces .templately-workspace:hover .tw-icon, .templately-wrapper .templately-container .templately-contents .templately-workspaces .templately-workspace.templately-active .tw-icon {
  background-image: url(../images/hover-workspace.png);
}
.templately-wrapper .templately-container .templately-contents .templately-workspaces .templately-workspace:hover .templately-context-btn, .templately-wrapper .templately-container .templately-contents .templately-workspaces .templately-workspace.templately-active .templately-context-btn {
  color: #fff;
}
.templately-wrapper .templately-container .templately-contents .templately-workspaces.templately-workspace-details > .templately-workspaces-row {
  display: flex;
}
.templately-wrapper .templately-container .templately-contents .templately-workspaces.templately-workspace-details .templately-workspace-filter-n-actions,
.templately-wrapper .templately-container .templately-contents .templately-workspaces.templately-workspace-details .templately-workspaces-files {
  padding-right: 15px;
  padding-left: 15px;
  flex-basis: 100%;
}
.templately-wrapper .templately-container .templately-contents .templately-workspaces.templately-workspace-details .templately-workspace-filter-n-actions {
  margin-bottom: 30px;
}
.templately-wrapper .templately-container .templately-contents .templately-workspaces.templately-workspace-details .templately-workspace-filter-n-actions,
.templately-wrapper .templately-container .templately-contents .templately-workspaces.templately-workspace-details .templately-workspace-filter-n-actions .twfa-header,
.templately-wrapper .templately-container .templately-contents .templately-workspaces.templately-workspace-details .templately-workspace-filter-n-actions .twfa-actions,
.templately-wrapper .templately-container .templately-contents .templately-workspaces.templately-workspace-details .templately-workspace-filter-n-actions .twfa-switcher-n-search {
  display: flex;
  align-items: center;
}
.templately-wrapper .templately-container .templately-contents .templately-workspaces.templately-workspace-details .templately-workspace-filter-n-actions .templately-button {
  height: 40px;
  width: 40px;
  font-size: 16px;
  border-radius: 50%;
  background-color: #fff;
  border: 0px;
  cursor: pointer;
  transition: background 300ms;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
}
.templately-wrapper .templately-container .templately-contents .templately-workspaces.templately-workspace-details .templately-workspace-filter-n-actions .templately-button.tb-add {
  margin-right: 15px;
  color: #fff;
  background: linear-gradient(-135deg, #6072ff 50%, #6c7dff 50%);
}
.templately-wrapper .templately-container .templately-contents .templately-workspaces.templately-workspace-details .templately-workspace-filter-n-actions .templately-button.tb-back {
  margin-left: 15px;
  color: #5453fd;
  background-color: rgba(84, 83, 253, 0.1);
}
.templately-wrapper .templately-container .templately-contents .templately-workspaces.templately-workspace-details .templately-workspace-filter-n-actions .templately-button.tb-back:hover {
  background-color: #5453fd;
  color: #fff;
}
.templately-wrapper .templately-container .templately-contents .templately-workspaces.templately-workspace-details .templately-workspace-filter-n-actions .twfa-actions {
  flex: 1;
  justify-content: flex-end;
}
.templately-wrapper .templately-container .templately-contents .templately-workspaces.templately-workspace-details .templately-workspace-filter-n-actions .twfa-actions .templately-view-buttons {
  display: flex;
  justify-content: flex-end;
}
.templately-wrapper .templately-container .templately-contents .templately-workspaces.templately-workspace-details .templately-workspace-filter-n-actions .twfa-actions .templately-view-buttons > button {
  height: 40px;
  width: 40px;
  font-size: 16px;
  margin-right: 15px;
  background: transparent;
  border-radius: 50%;
  background-color: #fff;
  border: 0px solid;
  color: #5352fc;
  cursor: pointer;
  transition: background 300ms;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
}
.templately-wrapper .templately-container .templately-contents .templately-workspaces.templately-workspace-details .templately-workspace-filter-n-actions .twfa-actions .templately-view-buttons > button:focus {
  outline: none;
}
.templately-wrapper .templately-container .templately-contents .templately-workspaces.templately-workspace-details .templately-workspace-filter-n-actions .twfa-actions .templately-view-buttons > button:hover, .templately-wrapper .templately-container .templately-contents .templately-workspaces.templately-workspace-details .templately-workspace-filter-n-actions .twfa-actions .templately-view-buttons > button.active {
  background: #5352fc;
  color: #fff;
}
.templately-wrapper .templately-container .templately-contents .templately-workspaces.templately-workspace-details .templately-workspace-filter-n-actions .twfa-actions .twfa-switcher-n-search {
  background-color: #fff;
  border-radius: 50px;
}
.templately-wrapper .templately-container .templately-contents .templately-workspaces.templately-workspace-details .templately-workspace-filter-n-actions .twfa-actions .twfa-switcher-n-search .twfa-switcher {
  border: none;
  width: 250px;
  border: none;
}
.templately-wrapper .templately-container .templately-contents .templately-workspaces.templately-workspace-details .templately-workspace-filter-n-actions .twfa-actions .twfa-switcher-n-search .twfa-switcher > .templately__control {
  border: none;
  border-radius: 0 50px 50px 0;
}
.templately-wrapper .templately-container .templately-contents .templately-workspaces.templately-workspace-details .templately-workspace-filter-n-actions .twfa-actions .twfa-switcher-n-search .twfa-switcher > .templately__control .templately__value-container {
  padding-right: 14px;
}
.templately-wrapper .templately-container .templately-contents .templately-workspaces.templately-workspace-details .templately-workspace-filter-n-actions .twfa-actions .twfa-switcher-n-search .twfa-switcher > .templately__control.templately__control--is-focused {
  border: none;
  box-shadow: none;
}
.templately-wrapper .templately-container .templately-contents .templately-workspaces.templately-workspace-details .templately-workspace-filter-n-actions .twfa-actions .twfa-switcher-n-search .twfa-switcher > .templately__menu {
  z-index: 999999999;
}
.templately-wrapper .templately-container .templately-contents .templately-workspaces.templately-workspace-details .templately-workspace-filter-n-actions .twfa-actions .twfa-switcher-n-search .twfa-switcher .templately__input:focus {
  box-shadow: none;
  border: none;
}
.templately-wrapper .templately-container .templately-contents .templately-workspaces.templately-workspace-details .templately-workspace-filter-n-actions .twfa-actions .twfa-switcher-n-search .twfa-search {
  display: flex;
}
.templately-wrapper .templately-container .templately-contents .templately-workspaces.templately-workspace-details .templately-workspace-filter-n-actions .twfa-actions .twfa-switcher-n-search .twfa-search > input {
  border: none;
  padding-left: 10px;
  border-radius: 50px 0 0 50px;
  background-color: #fff;
}
.templately-wrapper .templately-container .templately-contents .templately-workspaces.templately-workspace-details .templately-workspace-filter-n-actions .twfa-actions .twfa-switcher-n-search .twfa-search > input:focus {
  outline: none;
  box-shadow: none;
  border: none;
}
.templately-wrapper .templately-container .templately-contents .templately-workspaces.templately-workspace-details .templately-workspace-filter-n-actions .twfa-actions .twfa-switcher-n-search .twfa-search > .tb-wf-search {
  width: 42px;
  min-width: 42px;
  height: 42px;
  margin: 0;
  padding: 0;
  border: 0px solid;
  background: #ff7a8e;
  border-radius: 50px 50px 50px 50px;
  align-items: center;
  display: flex;
  justify-content: center;
  color: #fff;
  cursor: pointer;
}
.templately-wrapper .templately-container .templately-contents .templately-workspaces.templately-list-view .templately-workspaces-row {
  display: flex;
}
.templately-wrapper .templately-container .templately-contents .templately-workspaces.templately-list-view .templately-workspaces-column {
  flex: 1;
  flex-basis: 100%;
  width: 100%;
}
.templately-wrapper .templately-container .templately-contents .templately-workspaces.templately-list-view .templately-workspace {
  min-height: auto;
  display: block;
}
.templately-wrapper .templately-container .templately-contents .templately-workspaces.templately-list-view .templately-workspace .templately-workspace-inner {
  flex-direction: row;
}
.templately-wrapper .templately-container .templately-contents .templately-workspaces.templately-list-view .templately-workspace .templately-workspace-inner > * {
  flex: 1;
}
.templately-wrapper .templately-container .templately-contents .templately-workspaces.templately-list-view .templately-workspace header {
  display: flex;
  align-items: center;
}
.templately-wrapper .templately-container .templately-contents .templately-workspaces.templately-list-view .templately-workspace header > .tw-title {
  margin-bottom: 0;
}
.templately-wrapper .templately-container .templately-contents .templately-workspaces.templately-list-view .templately-workspace header .tw-icon {
  margin-left: 15px;
  margin-bottom: 0px;
}
.templately-wrapper .templately-container .templately-contents .templately-workspaces.templately-list-view .templately-workspace.templately-create-workspace .templately-workspace-inner {
  flex-direction: column;
}
.templately-wrapper .templately-container .templately-contents .templately-workspaces.templately-list-view .templately-workspace.templately-create-workspace .templately-workspace-inner .tw-icon {
  min-height: 31px;
}
.templately-wrapper .templately-container .templately-contents .templately-save-templates {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  margin-top: -30px;
}
.templately-wrapper .templately-container .templately-contents .templately-save-templates .tst-form-wrapper {
  background-color: #fff;
  padding: 40px;
  width: 50%;
}
.templately-wrapper .templately-container .templately-contents .templately-save-templates .tst-form-wrapper .tst-form {
  display: flex;
  align-items: center;
}
.templately-wrapper .templately-container .templately-contents .templately-save-templates .tst-form-wrapper .tst-form .tst-form-inputs {
  flex: 1;
}
.templately-wrapper .templately-container .templately-contents .templately-save-templates .tst-form-wrapper .tst-form .tst-form-inputs > * {
  width: 100%;
}
.templately-wrapper .templately-container .templately-contents .templately-save-templates .tst-form-wrapper .tst-form .tst-form-inputs > input {
  padding: 7px;
  margin-bottom: 15px;
  border: 1px solid #f2f2f2;
}
.templately-wrapper .templately-container .templately-contents .templately-profile {
  padding-right: 30px;
  padding-left: 30px;
  padding-bottom: 30px;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.templately-wrapper .templately-container .templately-contents .templately-profile .tp-image-wrap {
  width: 250px;
}
.templately-wrapper .templately-container .templately-contents .templately-profile .tp-image-wrap > img {
  max-width: 100%;
}
.templately-wrapper .templately-container .templately-contents .templately-profile .tp-managerial-links .templately-button {
  padding: 12px 20px;
  background-color: #6072ff;
  color: #fff;
  border-radius: 50px;
  margin-left: 15px;
  transition: all 300ms ease 0ms;
}
.templately-wrapper .templately-container .templately-contents .templately-profile .tp-managerial-links .templately-button:last-child {
  margin-left: 0px;
}
.templately-wrapper .templately-container .templately-contents .templately-profile .tp-managerial-links .templately-button:hover {
  background-color: #fff;
  color: #6072ff;
}
.templately-wrapper .templately-container .templately-contents .tca-favourites {
  width: 100%;
}
.templately-wrapper .templately-container .templately-contents .templatley-favourites-filter {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}
.templately-wrapper .templately-container .templately-contents .templatley-favourites-filter .templately-plan-switcher button.components-button {
  margin-right: 0;
  margin-left: 5px;
}
.templately-wrapper .templately-container .templately-contents .templately-my-favourites {
  padding-bottom: 30px;
}
.templately-wrapper .templately-container .templately-contents .templately-my-favourites .templately-item:not(.templately-pack-item) img.templately-item-image {
  max-height: 364px;
}
.templately-wrapper .templately-container .templately-contents .tca-purchasedItems {
  width: 100%;
}
.templately-wrapper .templately-container .templately-contents .templately-my-downloads {
  padding-bottom: 30px;
  padding-left: 15px;
  padding-right: 15px;
}
.templately-wrapper .templately-container .templately-contents .templately-my-downloads .templately-downloaded-item {
  color: #333;
  text-decoration: none;
}
.templately-wrapper .templately-container .templately-contents .templately-context-menu {
  display: flex;
  justify-content: right;
  width: 100%;
  left: 0;
  top: 0;
  align-items: center;
}
.templately-wrapper .templately-container .templately-contents .templately-context-menu > .tcm-pre-menus {
  margin-left: 15px;
}
.templately-wrapper .templately-container .templately-contents .templately-context-menu .templately-context-btn {
  line-height: 1;
  background: none;
  border: none;
  padding: 0;
  margin: 0;
  height: 14px;
  cursor: pointer;
}
.templately-wrapper .templately-container .templately-contents .templately-context-menu .templately-context-btn:focus {
  outline: none;
  box-shadow: none;
}
.templately-wrapper .templately-container .templately-contents .templately-context-menu .templately-context-menu-list {
  position: absolute;
  top: 14px;
  background-color: #fff;
  visibility: hidden;
  min-width: 220px;
  text-align: right;
  color: #000;
  padding: 10px 0px;
  border-radius: 5px;
  box-shadow: 0 0 10px 0px rgba(0, 0, 0, 0.3);
}
.templately-wrapper .templately-container .templately-contents .templately-context-menu .templately-context-menu-list > ul {
  margin: 0;
  padding: 0;
}
.templately-wrapper .templately-container .templately-contents .templately-context-menu .templately-context-menu-list > ul > li {
  padding: 7px 20px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}
.templately-wrapper .templately-container .templately-contents .templately-context-menu .templately-context-menu-list > ul > li a {
  text-decoration: none;
  display: flex;
  align-items: center;
  flex: 1;
}
.templately-wrapper .templately-container .templately-contents .templately-context-menu .templately-context-menu-list > ul > li.twm-preview, .templately-wrapper .templately-container .templately-contents .templately-context-menu .templately-context-menu-list > ul > li.twm-push-to-cloud {
  display: none;
}
@media only screen and (max-width: 786px) {
  .templately-wrapper .templately-container .templately-contents .templately-context-menu .templately-context-menu-list > ul > li.twm-preview, .templately-wrapper .templately-container .templately-contents .templately-context-menu .templately-context-menu-list > ul > li.twm-push-to-cloud {
    display: block;
  }
}
.templately-wrapper .templately-container .templately-contents .templately-context-menu .templately-context-menu-list > ul > li:hover {
  background-color: #f2f2f2;
}
.templately-wrapper .templately-container .templately-contents .templately-context-menu .templately-context-menu-list > ul > li[disabled] {
  opacity: 0.3;
  cursor: not-allowed;
}
.templately-wrapper .templately-container .templately-contents .templately-context-menu .templately-context-menu-list > ul > li:last-child {
  margin-bottom: 0;
}
.templately-wrapper .templately-container .templately-contents .templately-context-menu .templately-context-menu-list > ul > li > i, .templately-wrapper .templately-container .templately-contents .templately-context-menu .templately-context-menu-list > ul > li a > i {
  background-repeat: no-repeat;
  background-size: contain;
  width: 20px;
  height: 20px;
  display: inline-block;
  background-position: center center;
  margin-left: 15px;
}
.templately-wrapper .templately-container .templately-contents .templately-context-menu .templately-context-menu-list > ul > li.tw-insert i {
  background-image: url(../images/insert.png);
}
.templately-wrapper .templately-container .templately-contents .templately-context-menu .templately-context-menu-list > ul > li.tw-download i {
  background-image: url(../images/download.png);
}
.templately-wrapper .templately-container .templately-contents .templately-context-menu .templately-context-menu-list > ul > li.tw-preview > i {
  background-image: url(../images/preview.svg);
}
.templately-wrapper .templately-container .templately-contents .templately-context-menu .templately-context-menu-list > ul > li.tw-push-to-cloud > i {
  background-image: url(../images/push-to-cloud.svg);
}
.templately-wrapper .templately-container .templately-contents .templately-context-menu .templately-context-menu-list > ul > li.twm-preview > i {
  background-image: url(../images/preview.svg);
}
.templately-wrapper .templately-container .templately-contents .templately-context-menu .templately-context-menu-list > ul > li.twm-push-to-cloud > i {
  background-image: url(../images/push-to-cloud.svg);
}
.templately-wrapper .templately-container .templately-contents .templately-context-menu .templately-context-menu-list > ul > li > a {
  color: #000;
}
.templately-wrapper .templately-container .templately-contents .templately-context-menu .templately-context-menu-list > ul > li.tw-export a {
  display: flex;
  align-items: center;
  text-decoration: none;
}
.templately-wrapper .templately-container .templately-contents .templately-context-menu .templately-context-menu-list > ul > li.tw-export a > i {
  background-image: url(../images/export.svg);
}
.templately-wrapper .templately-container .templately-contents .templately-context-menu.templately-active .templately-context-menu-list {
  visibility: visible;
  position: absolute;
  z-index: 9999;
}
.templately-wrapper .templately-container .templately-contents .templately-not-verified-account {
  padding: 0 15px;
  font-size: 16px;
  margin-bottom: 10px;
}
.templately-wrapper .templately-container .templately-contents .templately-not-verified-account .templately-sync-link {
  border: none;
  background: none;
  color: #c0392b;
  cursor: pointer;
  text-decoration: underline;
}
.templately-wrapper .templately-container .templately-contents .templately-no-items {
  padding: 0 15px;
  width: 100%;
  display: flex;
  align-items: stretch;
  justify-content: center;
  box-sizing: border-box;
  height: 100%;
}
.templately-wrapper .templately-container .templately-contents .templately-no-items > div {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fff;
  border-radius: 10px;
  padding: 30px 0px;
}
.templately-wrapper .templately-container .templately-contents.templately-item-details {
  padding-right: 30px;
  padding-left: 30px;
  position: relative;
  z-index: 1;
  /* BANNER CSS */
}
.templately-wrapper .templately-container .templately-contents.templately-item-details:before {
  content: "";
  background-image: url(../images/shape1.png);
  position: absolute;
  left: 180px;
  top: -110px;
  width: 800px;
  height: 500px;
  z-index: -1;
  opacity: 0.4;
}
.templately-wrapper .templately-container .templately-contents.templately-item-details .templately-item-details-header {
  margin-bottom: 30px;
}
.templately-wrapper .templately-container .templately-contents.templately-item-details .templately-item-description {
  background-color: #fff;
  padding: 30px;
  margin-top: 30px;
  border-radius: 10px;
}
.templately-wrapper .templately-container .templately-contents.templately-item-details .templately-item-description p {
  margin: 0;
}
.templately-wrapper .templately-container .templately-contents.templately-item-details .templately-items-banner-wrapper .templately-items-banner {
  height: 565px;
  border-radius: 10px;
  position: relative;
}
.templately-wrapper .templately-container .templately-contents.templately-item-details .templately-items-banner-wrapper .templately-items-banner img {
  display: block;
  max-width: 100%;
  width: 100%;
  height: 100%;
  border-radius: 10px;
  -o-object-fit: cover;
     object-fit: cover;
  -o-object-position: top;
     object-position: top;
}
.templately-wrapper .templately-container .templately-contents.templately-item-details .templately-items-banner-wrapper .templately-items-banner > .tib-hover {
  border-radius: 10px;
  background-color: rgba(25, 24, 67, 0.6);
  position: absolute;
  right: 0;
  top: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: opacity 0.3s ease 0s;
  opacity: 0;
  visibility: hidden;
}
.templately-wrapper .templately-container .templately-contents.templately-item-details .templately-items-banner-wrapper .templately-items-banner:hover .tib-hover {
  opacity: 1;
  visibility: visible;
}
.templately-wrapper .templately-container .templately-contents.templately-item-details .templately-items-banner-wrapper .templately-button {
  background: #fff;
  padding: 15px 35px;
  margin: 0 15px;
  border-radius: 30px;
  text-transform: uppercase;
  transition: all 0.3s ease 0s;
  margin-right: 0px;
  margin-left: 15px;
}
.templately-wrapper .templately-container .templately-contents.templately-item-details .templately-items-banner-wrapper .templately-button.btn-live-demo {
  background-color: #ff7a8e;
  color: #fff;
}
.templately-wrapper .templately-container .templately-contents.templately-item-details .templately-items-banner-wrapper .templately-button:hover {
  background-color: #ff6178;
  color: #fff;
}
.templately-wrapper .templately-container .templately-contents.templately-item-details .templately-items-banner-wrapper .templately-packs-filters {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  position: relative;
  background-color: #fff;
  z-index: 9;
  padding: 15px 30px;
  border-radius: 10px;
}
.templately-wrapper .templately-container .templately-contents.templately-item-details .templately-items-banner-wrapper .templately-packs-filters button.templately-button,
.templately-wrapper .templately-container .templately-contents.templately-item-details .templately-items-banner-wrapper .templately-packs-filters a.templately-button {
  padding: 15px 25px;
  border: 1px solid #f2f2f2;
}
.templately-wrapper .templately-container .templately-contents.templately-item-details .templately-items-banner-wrapper .templately-packs-filters button.templately-button.btn-live-demo,
.templately-wrapper .templately-container .templately-contents.templately-item-details .templately-items-banner-wrapper .templately-packs-filters a.templately-button.btn-live-demo {
  margin-right: auto;
  margin-left: 0px;
  background-color: #fff;
  color: #5453fd;
}
.templately-wrapper .templately-container .templately-contents.templately-item-details .templately-items-banner-wrapper .templately-packs-filters button.templately-button.btn-live-demo:hover,
.templately-wrapper .templately-container .templately-contents.templately-item-details .templately-items-banner-wrapper .templately-packs-filters a.templately-button.btn-live-demo:hover {
  background-color: #5453fd;
  color: #fff;
}
.templately-wrapper .templately-container .templately-contents.templately-item-details .templately-items-banner-wrapper .templately-packs-filters button.templately-button {
  padding: 15px 25px;
  border: 1px solid #f2f2f2;
}
.templately-wrapper .templately-container .templately-contents.templately-item-details .templately-items-banner-wrapper .templately-packs-filters button.templately-button > span {
  color: #fff;
  display: inline-block;
  width: 20px;
  height: 20px;
  text-align: center;
  line-height: 20px;
  border-radius: 50%;
  margin-left: 6px;
}
.templately-wrapper .templately-container .templately-contents.templately-item-details .templately-items-banner-wrapper .templately-packs-filters button.templately-button.btn-all {
  color: #5453fd;
}
.templately-wrapper .templately-container .templately-contents.templately-item-details .templately-items-banner-wrapper .templately-packs-filters button.templately-button.btn-all:hover, .templately-wrapper .templately-container .templately-contents.templately-item-details .templately-items-banner-wrapper .templately-packs-filters button.templately-button.btn-all.active {
  background-color: #5453fd;
  color: #fff;
}
.templately-wrapper .templately-container .templately-contents.templately-item-details .templately-items-banner-wrapper .templately-packs-filters button.templately-button.btn-blocks {
  color: #5453fd;
}
.templately-wrapper .templately-container .templately-contents.templately-item-details .templately-items-banner-wrapper .templately-packs-filters button.templately-button.btn-blocks span {
  background-color: #5453fd;
}
.templately-wrapper .templately-container .templately-contents.templately-item-details .templately-items-banner-wrapper .templately-packs-filters button.templately-button.btn-blocks:hover, .templately-wrapper .templately-container .templately-contents.templately-item-details .templately-items-banner-wrapper .templately-packs-filters button.templately-button.btn-blocks.active {
  background-color: rgba(84, 83, 253, 0.1);
}
.templately-wrapper .templately-container .templately-contents.templately-item-details .templately-items-banner-wrapper .templately-packs-filters button.templately-button.btn-pages {
  color: #ff7a8e;
}
.templately-wrapper .templately-container .templately-contents.templately-item-details .templately-items-banner-wrapper .templately-packs-filters button.templately-button.btn-pages span {
  background-color: #ff7a8e;
}
.templately-wrapper .templately-container .templately-contents.templately-item-details .templately-items-banner-wrapper .templately-packs-filters button.templately-button.btn-pages:hover, .templately-wrapper .templately-container .templately-contents.templately-item-details .templately-items-banner-wrapper .templately-packs-filters button.templately-button.btn-pages.active {
  background-color: rgba(255, 122, 142, 0.1);
}
.templately-wrapper .templately-container .templately-contents.templately-item-details .templately-items-banner-wrapper .templately-packs-filters > div {
  position: relative;
}
.templately-wrapper .templately-container .templately-contents.templately-item-details .templately-items-banner-wrapper .templately-packs-filters > div > ul {
  margin: 0;
  display: none;
  position: absolute;
  right: 0;
  top: 100%;
  background-color: #5453fd;
  min-width: 200px;
  padding: 15px;
  border-radius: 5px;
}
.templately-wrapper .templately-container .templately-contents.templately-item-details .templately-items-banner-wrapper .templately-packs-filters > div > ul > li {
  color: hsla(0, 0%, 100%, 0.7);
}
.templately-wrapper .templately-container .templately-contents.templately-item-details .templately-items-banner-wrapper .templately-packs-filters > div > ul > li:not(:last-child) {
  margin-bottom: 5px;
}
.templately-wrapper .templately-container .templately-contents.templately-item-details .templately-items-banner-wrapper .templately-packs-filters > div > ul > li:hover {
  color: #fff;
  cursor: pointer;
}
.templately-wrapper .templately-container .templately-contents.templately-item-details .templately-items-banner-wrapper .templately-packs-filters > div.active.menu-open > ul {
  display: block;
}
.templately-wrapper .templately-container .templately-contents.templately-item-details .templately-items-banner-wrapper.is-pack .templately-items-banner,
.templately-wrapper .templately-container .templately-contents.templately-item-details .templately-items-banner-wrapper.is-pack .templately-items-banner img {
  border-radius: 10px 10px 0 0;
}
.templately-wrapper .templately-container .templately-contents.templately-item-details .templately-item-details-sidebar-wrapper > div {
  border-radius: 10px;
}
.templately-wrapper .templately-container .templately-contents.templately-item-details .templately-item-details-sidebar-wrapper .full-site-doc {
  display: block;
  margin: 10px;
  font-size: 12px;
}
.templately-wrapper .templately-container .templately-contents.templately-item-details .templately-item-details-sidebar-wrapper .full-site-doc i {
  font-size: 18px;
  margin-left: 3px;
  color: #6d7c90;
  line-height: 1.1;
}
.templately-wrapper .templately-container .templately-contents.templately-item-details .templately-item-details-sidebar-wrapper .full-site-doc a {
  color: rgb(84, 83, 253) !important;
  text-decoration: none;
}
.templately-wrapper .templately-container .templately-contents.templately-item-details .templately-item-details-sidebar-wrapper .full-site-doc a:hover {
  text-decoration: underline;
}
.templately-wrapper .templately-container .templately-contents.templately-item-details .templately-pack-items {
  padding-top: 40px;
  padding-bottom: 40px;
}
.templately-wrapper .templately-container .templately-contents.templately-item-details .templately-pack-items .templately-items {
  padding-right: 0;
  padding-left: 0;
}
.templately-wrapper .templately-container .templately-contents.templately-item-details .templately-details-dependencies-wrapper {
  display: flex;
  flex-wrap: wrap !important;
  align-items: center;
  row-gap: 0.25rem;
}
.templately-wrapper .templately-container .templately-contents.templately-item-details .templately-details-dependencies-wrapper .templately-details-dependencies {
  position: relative;
  display: inline-flex;
}
.templately-wrapper .templately-container .templately-contents.templately-item-details .templately-details-dependencies-wrapper .templately-details-dependencies .templately-item-dependencies {
  position: absolute;
  bottom: 5px;
  background: rgba(255, 255, 255, 0.9);
  width: auto;
  display: flex;
  padding: 5px 20px;
  box-sizing: border-box;
  justify-content: center;
  align-items: center;
  border-radius: 5px;
}
.templately-wrapper .templately-container .templately-contents.templately-item-details .templately-details-dependencies-wrapper .templately-details-dependencies .templately-item-dependencies .templately-dependency-icon {
  height: 30px;
  width: 30px;
  min-width: 30px;
  border-radius: 50px;
  display: flex;
  justify-content: center;
  align-items: center;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  line-height: 30px;
  cursor: pointer;
}
.templately-wrapper .templately-container .templately-contents.templately-item-details .templately-details-dependencies-wrapper .templately-details-dependencies .templately-item-dependencies .templately-dependency-icon:not(:last-child) {
  margin-left: 5px;
}
.templately-wrapper .templately-container .templately-contents.templately-item-details .templately-details-dependencies-wrapper .templately-details-dependencies .templately-item-dependencies .templately-dependency-icon img {
  width: 100%;
  height: 100%;
}
.templately-wrapper .templately-container .templately-contents.templately-item-details .templately-details-dependencies-wrapper .templately-details-dependencies .templately-item-dependencies .templately-dependency-icon .templately-dependency-icon-text-icon {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: #6d7c90;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
}
.templately-wrapper .templately-container .templately-contents.templately-item-details .templately-details-dependencies-wrapper .templately-details-dependencies .templately-item-dependencies {
  position: relative;
  bottom: auto;
  padding: 0px;
  display: inline-flex;
  flex-wrap: wrap;
  align-items: start !important;
  justify-content: start !important;
  row-gap: 0.15rem !important;
}
.templately-wrapper .templately-container .templately-contents.templately-item-details .templately-item-widget {
  background-color: #fff;
  padding: 30px;
  border-radius: 15px;
  margin-bottom: 30px;
}
.templately-wrapper .templately-container .templately-contents.templately-item-details .templately-item-widget > h3 {
  margin-top: 0px;
}
.templately-wrapper .templately-container .templately-contents.templately-item-details .templately-item-widget p {
  margin: 0;
}
.templately-wrapper .templately-container .templately-contents.templately-item-details .templately-item-widget.templately-item-tags {
  margin-top: 30px;
}
.templately-wrapper .templately-container .templately-contents.templately-item-details .templately-item-widget.templately-item-tags p span {
  background: rgba(84, 83, 253, 0.1);
  color: #5453fd;
  margin-left: 5px;
  margin-bottom: 5px;
  display: inline-block;
  padding: 7px 13px;
  border-radius: 50px;
  font-weight: 100;
  line-height: 1;
  font-size: 13px;
}
.templately-wrapper .templately-container .templately-contents.templately-item-details .templately-item-widget.templately-item-tags p span:hover {
  background-color: #5453fd;
  color: #fff;
}
.templately-wrapper .templately-container .templately-contents.templately-item-details .templately-items-sidebar ul {
  margin: 0;
  padding: 0;
}
.templately-wrapper .templately-container .templately-contents.templately-item-details .templately-items-sidebar ul li {
  flex-wrap: nowrap;
  color: #5453fd;
  font-size: 16px;
}
.templately-wrapper .templately-container .templately-contents.templately-item-details .templately-items-sidebar ul li:not(:last-child) {
  margin-bottom: 15px;
}
.templately-wrapper .templately-container .templately-contents.templately-item-details .templately-items-sidebar ul li .label {
  margin-left: 5px;
  color: #6d7c90;
  white-space: nowrap;
}
.templately-wrapper .templately-container .templately-contents.templately-item-details .templately-items-sidebar ul li:last-child {
  margin-bottom: 0px;
}
.templately-wrapper .templately-container .templately-contents.templately-item-details .templately-items-sidebar ul li a {
  text-decoration: none;
}
.templately-wrapper .templately-container .templately-contents.templately-item-details .templately-items-sidebar ul li a:not(.templately-button) {
  color: currentColor;
}
.templately-wrapper .templately-container .templately-contents.templately-item-details .templately-items-sidebar ul li a:not(.templately-button):hover {
  text-decoration: underline;
}
.templately-wrapper .templately-container .templately-contents.templately-item-details .templately-items-sidebar ul li .templately-details-rating-wrapper {
  display: flex;
  align-items: center;
  margin-bottom: var(--base-gutter);
  gap: 0.444444rem;
}
.templately-wrapper .templately-container .templately-contents.templately-item-details .templately-items-sidebar ul li .templately-details-rating-wrapper .templately-details-rating {
  margin-left: 5px;
  margin-right: 0px;
  background-color: #fff;
  border: 1px solid #ecf0f7;
  border-radius: 3px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 5px 10px;
  color: #6d7c90;
  text-transform: uppercase;
}
.templately-wrapper .templately-container .templately-contents.templately-item-details .templately-items-sidebar ul li .templately-details-rating-wrapper .templately-details-rating > svg {
  margin-left: 6px;
}
.templately-wrapper .templately-container .templately-contents.templately-item-details .templately-items-sidebar ul li .templately-details-rating-wrapper .templately-details-rating svg {
  color: #ffb45a;
}
.templately-wrapper .templately-container .templately-contents.templately-item-details .templately-items-sidebar ul li .templately-details-rating-wrapper .templately-details-rating.not-rated svg {
  color: #6d7c90;
}
.templately-wrapper .templately-container .templately-contents.templately-item-details .templately-items-sidebar ul li .templately-details-rating-wrapper .templately-details-rating-submit {
  flex-wrap: wrap;
  align-items: center;
  display: flex;
}
.templately-wrapper .templately-container .templately-contents.templately-item-details .templately-items-sidebar ul li .templately-details-rating-wrapper .templately-details-rating-submit label {
  font-size: 0.8333333em;
  letter-spacing: -0.05em;
  color: #6d7c90;
}
.templately-wrapper .templately-container .templately-contents.templately-item-details .templately-items-sidebar ul li .templately-details-rating-wrapper .templately-details-rating-submit ol, .templately-wrapper .templately-container .templately-contents.templately-item-details .templately-items-sidebar ul li .templately-details-rating-wrapper .templately-details-rating-submit ul {
  margin-bottom: 0;
  list-style: none;
}
.templately-wrapper .templately-container .templately-contents.templately-item-details .templately-items-sidebar ul li .templately-details-rating-wrapper .templately-details-rating-submit .tly__rate__with__star {
  display: flex;
  gap: 0.222222em;
}
.templately-wrapper .templately-container .templately-contents.templately-item-details .templately-items-sidebar ul li .templately-details-rating-wrapper .templately-details-rating-submit .tly__rate__with__star .tly__star {
  color: #dae2ef;
  display: inline-flex;
  font-size: 1em;
  margin-bottom: 0;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}
.templately-wrapper .templately-container .templately-contents.templately-item-details .templately-items-sidebar ul li .templately-details-rating-wrapper .templately-details-rating-submit .tly__rate__with__star .tly__star.active {
  color: #ffb45a;
}
.templately-wrapper .templately-container .templately-contents.templately-item-details .templately-layouts-in-packs {
  background-color: #fff;
  border-radius: 10px;
  padding: 30px;
}
.templately-wrapper .templately-container .templately-contents.templately-item-details .templately-layouts-in-packs h3 {
  margin-top: 0;
}
.templately-wrapper .templately-container .templately-contents.templately-item-details .templately-layouts-in-packs .templately-carousel-wrapper {
  margin-right: -10px;
  margin-left: -10px;
}
.templately-wrapper .templately-container .templately-contents.templately-item-details .templately-layouts-in-packs .templately-layouts-in-packs-item {
  border-radius: 10px;
  margin: 0px 10px;
  background-color: #fff;
}
.templately-wrapper .templately-container .templately-contents.templately-item-details .templately-layouts-in-packs .templately-layouts-in-packs-item a {
  text-decoration: none;
  color: #333;
}
.templately-wrapper .templately-container .templately-contents.templately-item-details .templately-layouts-in-packs .templately-layouts-in-packs-item h4 {
  padding: 10px;
  margin: 0px;
  text-align: center;
}
.templately-wrapper .templately-container .templately-contents.templately-item-details .templately-layouts-in-packs .templately-layouts-in-packs-item .templately-layouts-in-packs-item-image {
  margin-top: 10px;
  position: relative;
}
.templately-wrapper .templately-container .templately-contents.templately-item-details .templately-layouts-in-packs .templately-layouts-in-packs-item.templately-layouts-in-packs-item-active {
  position: relative;
}
.templately-wrapper .templately-container .templately-contents.templately-item-details .templately-layouts-in-packs .templately-layouts-in-packs-item.templately-layouts-in-packs-item-active:before {
  content: "";
  background-color: rgb(147, 159.2264150943, 255);
  opacity: 0.4;
  position: absolute;
  right: 0;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}
.templately-wrapper .templately-container .templately-contents.templately-item-details .templately-item-details-meta > button.templately-button:first-of-type {
  margin-right: 0;
  padding-right: 0;
}
.templately-wrapper .templately-container .templately-contents.templately-item-details .templately-item-details-meta .templately-button,
.templately-wrapper .templately-container .templately-contents.templately-item-details .templately-item-details-meta span {
  margin-left: 15px;
}
.templately-wrapper .templately-container .templately-contents.templately-item-details .templately-item-details-meta .templately-button svg,
.templately-wrapper .templately-container .templately-contents.templately-item-details .templately-item-details-meta span svg {
  margin-left: 8px;
}
.templately-wrapper .templately-container .templately-contents.templately-item-details .templately-item-details-meta .t-full-site-import {
  background-color: #EAEAFF;
  color: #5453fd;
  padding: 2px 6px;
  font-size: 12px;
  font-weight: 500;
  align-self: center;
  border-radius: 2px;
}
.templately-wrapper .templately-container .templately-contents.templately-item-details .tly__site__promote__widget {
  padding: 0 !important;
  position: relative;
  background: unset;
  box-shadow: 0px 5px 40px rgba(32, 23, 47, 0.05);
}
.templately-wrapper .templately-container .templately-contents.templately-item-details .tly__site__promote__widget .tly__promo_heading {
  position: relative;
  width: 100%;
  overflow: hidden;
  border-radius: 10px 10px 0 0;
  background: #333;
}
.templately-wrapper .templately-container .templately-contents.templately-item-details .tly__site__promote__widget .tly__promo_heading > img {
  width: 100%;
}
.templately-wrapper .templately-container .templately-contents.templately-item-details .tly__site__promote__widget .tly__promo_heading .tly__plan__card {
  position: absolute;
  bottom: 0;
  padding-bottom: 1.111111rem;
  padding-right: 0.555555rem;
  padding-left: 0.555555rem;
  z-index: 1;
}
.templately-wrapper .templately-container .templately-contents.templately-item-details .tly__site__promote__widget .tly__promo_heading .tly__plan__card .tly__plan__heading {
  color: #fff !important;
  margin-top: 0 !important;
  margin-bottom: 0.555555rem;
  letter-spacing: -0.075em;
}
.templately-wrapper .templately-container .templately-contents.templately-item-details .tly__site__promote__widget .tly__promo_heading .tly__plan__card .tly__plan__description {
  color: #fff !important;
  letter-spacing: -0.025em;
}
.templately-wrapper .templately-container .templately-contents.templately-item-details .tly__site__promote__widget .tly__promo_body {
  padding: 30px;
  background: #fff;
  border-radius: 0 0 10px 10px;
}
.templately-wrapper .templately-container .templately-contents.templately-item-details .tly__site__promote__widget .tly__plan__card {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}
.templately-wrapper .templately-container .templately-contents.templately-item-details .tly__site__promote__widget .tly__plan__card.tly__center {
  align-items: center;
  text-align: center;
}
.templately-wrapper .templately-container .templately-contents.templately-item-details .tly__site__promote__widget .tly__plan__card .tly__plan__heading {
  font-weight: 500;
  font-size: 1rem;
  line-height: 1.1;
  color: #000;
  margin-top: 0 !important;
  margin-bottom: 1.111111rem;
}
.templately-wrapper .templately-container .templately-contents.templately-item-details .tly__site__promote__widget .tly__plan__card .tly__plan__heading.tly__lg {
  font-weight: 700;
  font-size: 1.333333rem;
  line-height: 1;
}
.templately-wrapper .templately-container .templately-contents.templately-item-details .tly__site__promote__widget .tly__plan__card .tly__plan__wrapper {
  display: flex;
  row-gap: 0.555555rem;
  -moz-column-gap: 1.333333rem;
       column-gap: 1.333333rem;
  width: 100%;
}
.templately-wrapper .templately-container .templately-contents.templately-item-details .tly__site__promote__widget .tly__plan__card .tly__plan__wrapper .tly__plan {
  position: relative;
  flex: 1;
  display: flex;
}
.templately-wrapper .templately-container .templately-contents.templately-item-details .tly__site__promote__widget .tly__plan__card .tly__plan__wrapper .tly__plan input {
  display: none;
}
.templately-wrapper .templately-container .templately-contents.templately-item-details .tly__site__promote__widget .tly__plan__card .tly__plan__wrapper .tly__plan input + .tly__plan_content {
  height: 3.111111rem;
  border: 1px solid #e2e5ec;
  border-radius: 0.555555rem;
  padding: 0.222222rem 1.111111rem;
  display: inline-flex;
  align-items: center;
  width: 100%;
  font-size: 0.888888rem;
  line-height: 1;
  text-transform: uppercase;
  position: relative;
}
.templately-wrapper .templately-container .templately-contents.templately-item-details .tly__site__promote__widget .tly__plan__card .tly__plan__wrapper .tly__plan input + .tly__plan_content:before {
  content: "";
  width: 5px;
  height: 5px;
  box-shadow: 0 0 0 0px #fff, 0 0 0 2px #fff, 0 0 0 3px #ccc;
  background-color: #fff;
  border-radius: 50%;
  margin-left: 0.555555rem;
  filter: alpha(opacity=70);
  font-size: 0.888888rem;
}
.templately-wrapper .templately-container .templately-contents.templately-item-details .tly__site__promote__widget .tly__plan__card .tly__plan__wrapper .tly__plan input + .tly__plan_content .tly__plan__badge {
  position: absolute;
  left: 0.333333rem;
  background: #5453fd;
  border-radius: 0.333333rem;
  padding: 0.333333rem 0.444444rem;
  font-weight: 500;
  font-size: 0.555555rem;
  line-height: 1;
  text-align: center;
  text-transform: uppercase;
  color: #ffffff;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  top: -0.444444rem;
}
.templately-wrapper .templately-container .templately-contents.templately-item-details .tly__site__promote__widget .tly__plan__card .tly__plan__wrapper .tly__plan input:checked + .tly__plan_content {
  border-color: #5453fd;
}
.templately-wrapper .templately-container .templately-contents.templately-item-details .tly__site__promote__widget .tly__plan__card .tly__plan__wrapper .tly__plan input:checked + .tly__plan_content:before {
  content: "";
  color: #5453fd;
  box-shadow: 0 0 0 0px #5453fd, 0 0 0 2px white, 0 0 0 3px #5453fd;
  background-color: #5453fd;
}
.templately-wrapper .templately-container .templately-contents.templately-item-details .tly__site__promote__widget .tly__plan__card .tly__plan__description_wrapper {
  margin-top: 1.666666rem;
}
.templately-wrapper .templately-container .templately-contents.templately-item-details .tly__site__promote__widget .tly__plan__card .tly__plan__description_wrapper.tly__mt_0 {
  margin-top: 0;
}
.templately-wrapper .templately-container .templately-contents.templately-item-details .tly__site__promote__widget .tly__plan__card .tly__plan__description_wrapper .tly__plan__description_list {
  color: #999;
  display: flex;
  flex-direction: column;
  gap: 0.555555rem;
  padding-right: 0.977777rem;
}
.templately-wrapper .templately-container .templately-contents.templately-item-details .tly__site__promote__widget .tly__plan__card .tly__plan__description_wrapper .tly__plan__description_list li {
  font-size: 0.888888rem;
  line-height: 1.1;
  list-style: disc;
}
.templately-wrapper .templately-container .templately-contents.templately-item-details .tly__site__promote__widget .tly__plan__card .tly__plan__description_wrapper .tly__plan__description {
  font-weight: 400;
  font-size: 0.777777rem;
  line-height: 1.4;
  text-align: center;
  color: #999999;
}
.templately-wrapper .templately-container .templately-contents.templately-item-details .tly__site__promote__widget .tly__site__promote__widget__title {
  color: #fff;
  font-size: 1.888888em;
  margin: var(--base-gutter) 0;
}
.templately-wrapper .templately-container .templately-contents.templately-item-details .tly__button {
  padding: 2px 23px;
  font-size: 0.888888em;
  font-weight: 500;
  line-height: 1.2;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  border-radius: 50px;
  min-height: 50px;
  border: 2px solid;
  text-transform: uppercase;
  transition: all 0.3s ease-in-out 0s;
  transition-property: background, color, box-shadow;
}
.templately-wrapper .templately-container .templately-contents.templately-item-details .tly__success__button {
  background: #2bcf81;
  color: white;
  border-color: #2bcf81;
  transition: all 0.3s ease-in-out 0s;
  transition-property: background, color, box-shadow, border;
}
.templately-wrapper .templately-container .templately-contents.templately-item-details .tly__success__button:disabled {
  cursor: not-allowed;
  background: #6d7c90;
  border-color: #6d7c90;
}
.templately-wrapper .templately-container .templately-contents.templately-item-details .tly__success__button .tly__icon {
  color: #2bcf81;
  background: white;
}
.templately-wrapper .templately-container .templately-contents.templately-item-details .tly__success__button.tly__active,
.templately-wrapper .templately-container .templately-contents.templately-item-details .tly__success__button:hover {
  background: #27ba74;
  color: white;
  border-color: #27ba74;
  text-shadow: 0 1px 1px rgba(255, 255, 255, 0.3);
  box-shadow: 0 15px 40px rgba(43, 207, 129, 0.15);
}
.templately-wrapper .templately-container .templately-contents.templately-item-details .tly__success__button.tly__active:disabled,
.templately-wrapper .templately-container .templately-contents.templately-item-details .tly__success__button:hover:disabled {
  cursor: not-allowed;
  background: #6d7c90;
  box-shadow: none;
  border-color: #6d7c90;
}
.templately-wrapper .templately-container .templately-contents.templately-item-details .tly__success__button.tly__hover__highlight:hover .tly__icon {
  background: white;
  color: #2bcf81;
}
@keyframes closeWindow {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}
.templately-wrapper .templately-container .ril__outer {
  background-color: rgba(0, 0, 0, 0.85);
  outline: none;
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;
  z-index: 1000;
  width: 100%;
  height: 100%;
  -ms-content-zooming: none;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  -ms-touch-select: none;
  touch-action: none;
}
.templately-wrapper .templately-container .ril__outerClosing {
  opacity: 0;
}
.templately-wrapper .templately-container .ril__inner {
  position: absolute;
  top: 50px;
  right: 0;
  left: 0;
  bottom: 0;
}
.templately-wrapper .templately-container .ril__image,
.templately-wrapper .templately-container .ril__imagePrev,
.templately-wrapper .templately-container .ril__imageNext {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  margin: auto;
  max-width: 100%;
  -ms-content-zooming: none;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  -ms-touch-select: none;
  touch-action: none;
}
.templately-wrapper .templately-container .ril__imageDiscourager {
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}
.templately-wrapper .templately-container .ril__navButtons {
  border: none;
  position: absolute;
  top: 0;
  bottom: 0;
  width: 20px;
  height: 34px;
  padding: 40px 30px;
  margin: auto;
  cursor: pointer;
  opacity: 0.7;
}
.templately-wrapper .templately-container .ril__navButtons:hover {
  opacity: 1;
}
.templately-wrapper .templately-container .ril__navButtons:active {
  opacity: 0.7;
}
.templately-wrapper .templately-container .ril__navButtonPrev {
  right: 0;
  background: rgba(0, 0, 0, 0.2) url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZlcnNpb249IjEuMSIgd2lkdGg9IjIwIiBoZWlnaHQ9IjM0Ij48cGF0aCBkPSJtIDE5LDMgLTIsLTIgLTE2LDE2IDE2LDE2IDEsLTEgLTE1LC0xNSAxNSwtMTUgeiIgZmlsbD0iI0ZGRiIvPjwvc3ZnPg==) no-repeat center;
}
.templately-wrapper .templately-container .ril__navButtonNext {
  left: 0;
  background: rgba(0, 0, 0, 0.2) url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZlcnNpb249IjEuMSIgd2lkdGg9IjIwIiBoZWlnaHQ9IjM0Ij48cGF0aCBkPSJtIDEsMyAyLC0yIDE2LDE2IC0xNiwxNiAtMSwtMSAxNSwtMTUgLTE1LC0xNSB6IiBmaWxsPSIjRkZGIi8+PC9zdmc+) no-repeat center;
}
.templately-wrapper .templately-container .ril__downloadBlocker {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;
  background-image: url(data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7);
  background-size: cover;
}
.templately-wrapper .templately-container .ril__caption,
.templately-wrapper .templately-container .ril__toolbar {
  background-color: rgba(0, 0, 0, 0.5);
  position: absolute;
  right: 0;
  left: 0;
  display: flex;
  justify-content: space-between;
}
.templately-wrapper .templately-container .ril__caption {
  bottom: 0;
  max-height: 150px;
  overflow: auto;
}
.templately-wrapper .templately-container .ril__captionContent {
  padding: 10px 20px;
  color: #fff;
}
.templately-wrapper .templately-container .ril__toolbar {
  top: 0;
  height: 50px;
}
.templately-wrapper .templately-container .ril__toolbarSide {
  height: 50px;
  margin: 0;
}
.templately-wrapper .templately-container .ril__toolbarLeftSide {
  padding-right: 20px;
  padding-left: 0;
  flex: 0 1 auto;
  overflow: hidden;
  text-overflow: ellipsis;
}
.templately-wrapper .templately-container .ril__toolbarRightSide {
  padding-right: 0;
  padding-left: 20px;
  flex: 0 0 auto;
}
.templately-wrapper .templately-container .ril__toolbarItem {
  display: inline-block;
  line-height: 50px;
  padding: 0;
  color: #fff;
  font-size: 120%;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.templately-wrapper .templately-container .ril__toolbarItemChild {
  vertical-align: middle;
}
.templately-wrapper .templately-container .ril__builtinButton {
  width: 40px;
  height: 35px;
  cursor: pointer;
  border: none;
  opacity: 0.7;
}
.templately-wrapper .templately-container .ril__builtinButton:hover {
  opacity: 1;
}
.templately-wrapper .templately-container .ril__builtinButton:active {
  outline: none;
}
.templately-wrapper .templately-container .ril__builtinButtonDisabled {
  cursor: default;
  opacity: 0.5;
}
.templately-wrapper .templately-container .ril__builtinButtonDisabled:hover {
  opacity: 0.5;
}
.templately-wrapper .templately-container .ril__closeButton {
  background: url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZlcnNpb249IjEuMSIgd2lkdGg9IjIwIiBoZWlnaHQ9IjIwIj48cGF0aCBkPSJtIDEsMyAxLjI1LC0xLjI1IDcuNSw3LjUgNy41LC03LjUgMS4yNSwxLjI1IC03LjUsNy41IDcuNSw3LjUgLTEuMjUsMS4yNSAtNy41LC03LjUgLTcuNSw3LjUgLTEuMjUsLTEuMjUgNy41LC03LjUgLTcuNSwtNy41IHoiIGZpbGw9IiNGRkYiLz48L3N2Zz4=) no-repeat center;
}
.templately-wrapper .templately-container .ril__zoomInButton {
  background: url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyMCIgaGVpZ2h0PSIyMCI+PGcgc3Ryb2tlPSIjZmZmIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCI+PHBhdGggZD0iTTEgMTlsNi02Ii8+PHBhdGggZD0iTTkgOGg2Ii8+PHBhdGggZD0iTTEyIDV2NiIvPjwvZz48Y2lyY2xlIGN4PSIxMiIgY3k9IjgiIHI9IjciIGZpbGw9Im5vbmUiIHN0cm9rZT0iI2ZmZiIgc3Ryb2tlLXdpZHRoPSIyIi8+PC9zdmc+) no-repeat center;
}
.templately-wrapper .templately-container .ril__zoomOutButton {
  background: url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyMCIgaGVpZ2h0PSIyMCI+PGcgc3Ryb2tlPSIjZmZmIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCI+PHBhdGggZD0iTTEgMTlsNi02Ii8+PHBhdGggZD0iTTkgOGg2Ii8+PC9nPjxjaXJjbGUgY3g9IjEyIiBjeT0iOCIgcj0iNyIgZmlsbD0ibm9uZSIgc3Ryb2tlPSIjZmZmIiBzdHJva2Utd2lkdGg9IjIiLz48L3N2Zz4=) no-repeat center;
}
.templately-wrapper .templately-container .ril__outerAnimating {
  animation-name: closeWindow;
}
@keyframes pointFade {
  0%, 19.999%, 100% {
    opacity: 0;
  }
  20% {
    opacity: 1;
  }
}
.templately-wrapper .templately-container .ril__loadingCircle {
  width: 60px;
  height: 60px;
  position: relative;
}
.templately-wrapper .templately-container .ril__loadingCirclePoint {
  width: 100%;
  height: 100%;
  position: absolute;
  right: 0;
  top: 0;
}
.templately-wrapper .templately-container .ril__loadingCirclePoint::before {
  content: "";
  display: block;
  margin: 0 auto;
  width: 11%;
  height: 30%;
  background-color: #fff;
  border-radius: 30%;
  animation: pointFade 800ms infinite ease-in-out both;
}
.templately-wrapper .templately-container .ril__loadingCirclePoint:nth-of-type(1) {
  transform: rotate(0deg);
}
.templately-wrapper .templately-container .ril__loadingCirclePoint:nth-of-type(7) {
  transform: rotate(-180deg);
}
.templately-wrapper .templately-container .ril__loadingCirclePoint:nth-of-type(1)::before,
.templately-wrapper .templately-container .ril__loadingCirclePoint:nth-of-type(7)::before {
  animation-delay: -800ms;
}
.templately-wrapper .templately-container .ril__loadingCirclePoint:nth-of-type(2) {
  transform: rotate(-30deg);
}
.templately-wrapper .templately-container .ril__loadingCirclePoint:nth-of-type(8) {
  transform: rotate(-210deg);
}
.templately-wrapper .templately-container .ril__loadingCirclePoint:nth-of-type(2)::before,
.templately-wrapper .templately-container .ril__loadingCirclePoint:nth-of-type(8)::before {
  animation-delay: -666ms;
}
.templately-wrapper .templately-container .ril__loadingCirclePoint:nth-of-type(3) {
  transform: rotate(-60deg);
}
.templately-wrapper .templately-container .ril__loadingCirclePoint:nth-of-type(9) {
  transform: rotate(-240deg);
}
.templately-wrapper .templately-container .ril__loadingCirclePoint:nth-of-type(3)::before,
.templately-wrapper .templately-container .ril__loadingCirclePoint:nth-of-type(9)::before {
  animation-delay: -533ms;
}
.templately-wrapper .templately-container .ril__loadingCirclePoint:nth-of-type(4) {
  transform: rotate(-90deg);
}
.templately-wrapper .templately-container .ril__loadingCirclePoint:nth-of-type(10) {
  transform: rotate(-270deg);
}
.templately-wrapper .templately-container .ril__loadingCirclePoint:nth-of-type(4)::before,
.templately-wrapper .templately-container .ril__loadingCirclePoint:nth-of-type(10)::before {
  animation-delay: -400ms;
}
.templately-wrapper .templately-container .ril__loadingCirclePoint:nth-of-type(5) {
  transform: rotate(-120deg);
}
.templately-wrapper .templately-container .ril__loadingCirclePoint:nth-of-type(11) {
  transform: rotate(-300deg);
}
.templately-wrapper .templately-container .ril__loadingCirclePoint:nth-of-type(5)::before,
.templately-wrapper .templately-container .ril__loadingCirclePoint:nth-of-type(11)::before {
  animation-delay: -266ms;
}
.templately-wrapper .templately-container .ril__loadingCirclePoint:nth-of-type(6) {
  transform: rotate(-150deg);
}
.templately-wrapper .templately-container .ril__loadingCirclePoint:nth-of-type(12) {
  transform: rotate(-330deg);
}
.templately-wrapper .templately-container .ril__loadingCirclePoint:nth-of-type(6)::before,
.templately-wrapper .templately-container .ril__loadingCirclePoint:nth-of-type(12)::before {
  animation-delay: -133ms;
}
.templately-wrapper .templately-container .ril__loadingCirclePoint:nth-of-type(7) {
  transform: rotate(-180deg);
}
.templately-wrapper .templately-container .ril__loadingCirclePoint:nth-of-type(13) {
  transform: rotate(-360deg);
}
.templately-wrapper .templately-container .ril__loadingCirclePoint:nth-of-type(7)::before,
.templately-wrapper .templately-container .ril__loadingCirclePoint:nth-of-type(13)::before {
  animation-delay: 0ms;
}
.templately-wrapper .templately-container .ril__loadingContainer {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
}
.templately-wrapper .templately-container .ril__imagePrev .ril__loadingContainer,
.templately-wrapper .templately-container .ril__imageNext .ril__loadingContainer {
  display: none;
}
.templately-wrapper .templately-container .ril__errorContainer {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
}
.templately-wrapper .templately-container .ril__imagePrev .ril__errorContainer,
.templately-wrapper .templately-container .ril__imageNext .ril__errorContainer {
  display: none;
}
.templately-wrapper .templately-container .ril__loadingContainer__icon {
  color: #fff;
  position: absolute;
  top: 50%;
  right: 50%;
  transform: translateX(50%) translateY(-50%);
}
.templately-wrapper .templately-container.templately-templates-container .templately-saved-templates {
  padding: 0 30px 30px;
  box-sizing: border-box;
}
.templately-wrapper .templately-container.templately-templates-container .templately-saved-templates.templately-nas-no-items {
  padding: 0 15px 30px;
  height: 100%;
}
.templately-wrapper .templately-container.templately-templates-container .templately-saved-templates.templately-nas-no-items > .templately-no-items {
  height: 100%;
  align-items: stretch;
}
.templately-wrapper .templately-container.templately-templates-container .templately-saved-templates .tt-actions {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.templately-wrapper .templately-container.templately-templates-container .templately-saved-templates .tt-actions .templately-button.tw-preview:not(li), .templately-wrapper .templately-container.templately-templates-container .templately-saved-templates .tt-actions .templately-button.tw-push2cloud:not(li) {
  background-color: #fff;
  border: 1px dashed #f2f2f2;
  padding: 9px 15px;
  border-radius: 30px;
  margin-right: 10px;
  text-transform: uppercase;
  font-size: 13px;
  transition: all 0.3s ease 0s;
  color: #585858;
  text-decoration: none;
}
.templately-wrapper .templately-container.templately-templates-container .templately-saved-templates .tt-actions .templately-button.tw-preview:not(li):hover {
  background-color: #5453fd;
}
.templately-wrapper .templately-container.templately-templates-container .templately-saved-templates .tt-actions .templately-button.tw-preview i {
  background-image: url(../images/preview.svg);
}
.templately-wrapper .templately-container.templately-templates-container .templately-saved-templates .tt-actions .templately-button.tw-push2cloud:not(li):hover {
  background-color: #5ac0ff;
}
.templately-wrapper .templately-container.templately-templates-container .templately-saved-templates .tt-actions .templately-button.tw-push2cloud i {
  background-image: url(../images/push-to-cloud.svg);
}
.templately-wrapper .templately-container.templately-templates-container .templately-saved-templates .tt-actions .templately-button:not(li):hover {
  color: #fff;
}
.templately-wrapper .templately-container.templately-templates-container .templately-saved-templates .tt-actions .templately-context-menu {
  width: auto;
  margin-right: 15px;
}
.templately-wrapper .templately-video-container {
  background: rgba(0, 0, 0, 0.5);
  padding: 50px 10px;
}
.templately-wrapper .templately-video-container .templately-video-popup {
  width: calc(100% - 55px);
  max-width: 850px;
  min-width: initial !important;
  margin-left: 55px;
  border-radius: 24px;
  background: #3d3f54;
  padding: 0;
  position: relative;
}
.templately-wrapper .templately-video-container .templately-video-popup .templately-video-close-button {
  width: 40px;
  height: 40px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: #ffffff;
  color: #3d3f54;
  position: absolute;
  top: 0;
  left: -55px;
  transition: all 0.2s ease-in-out;
  box-shadow: none !important;
}
.templately-wrapper .templately-video-container .templately-video-popup .templately-video-close-button:hover {
  color: #000000;
}
.templately-wrapper .templately-video-container .templately-video-popup .templately-video-html {
  margin: 0 !important;
  padding: 20px !important;
  display: flex !important;
  flex-direction: column;
  align-items: flex-start;
  border-radius: 24px;
  overflow: hidden !important;
}
.templately-wrapper .templately-video-container .templately-video-popup .templately-video-html iframe {
  width: 100% !important;
}
.templately-wrapper .templately-verification {
  z-index: 9999;
  position: absolute;
  bottom: 30px;
  left: 30px;
  max-width: 300px;
  background: #fff;
  color: #000;
  padding: 30px;
  border-radius: 10px;
  box-shadow: 0 0 30px 0 rgba(0, 0, 0, 0.2);
}
.templately-screen-templately.templately-wrapper .templately-verification {
  bottom: 40px;
  position: fixed;
}

.templately-wrapper .templately-verification > p {
  margin: 0;
}
.templately-wrapper .templately-verification a {
  color: #5453fd;
}
.templately-wrapper .templately-verification > button.components-button {
  background: #5453fd;
  color: #fff;
  padding: 8px 15px;
  border-radius: 5px;
  border: 0;
  margin-top: 20px;
  cursor: pointer;
}
.templately-wrapper .templately-verification > button.components-button:focus {
  outline: none;
  box-shadow: none;
}
.templately-wrapper .templately-feedback-form {
  font-family: "Inter", sans-serif;
  z-index: 9999;
  position: fixed;
  bottom: 0;
  left: 0;
  max-width: 508px;
  width: 98%;
}
.templately-wrapper .templately-feedback-form .review-header {
  background-color: #ffffff;
  background-image: url(../images/customizer-header.svg);
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  padding: 24px;
  display: flex;
  flex-direction: column;
  position: relative;
  gap: 10px;
  border-bottom: 1px solid #eaecf0;
  border-top-right-radius: 16px;
  border-top-left-radius: 16px;
}
.templately-wrapper .templately-feedback-form .review-header .templately-review-header-title {
  margin: 0;
  color: #1d2939;
  font-size: 24px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
}
.templately-wrapper .templately-feedback-form .review-header .templately-review-header-sub-title {
  margin: 0;
  color: #475467;
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}
.templately-wrapper .templately-feedback-form .review-header .templately-review-header-close-button {
  height: 32px;
  width: 32px;
  border: none;
  background-color: transparent;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  left: 24px;
  border-radius: 50%;
  cursor: pointer;
}
.templately-wrapper .templately-feedback-form .review-header .templately-review-header-close-button svg {
  width: 11px;
  height: auto;
  fill: #475467;
}
.templately-wrapper .templately-feedback-form .review-body {
  padding: 24px;
  background-color: #ffffff;
}
.templately-wrapper .templately-feedback-form .review-body:last-child {
  border-bottom-right-radius: 16px;
}
.templately-wrapper .templately-feedback-form .review-body .review-wrapper {
  display: flex;
  flex-direction: column;
  gap: 24px;
  background: #ffffff;
  border: 1px solid #eaecf0;
  border-radius: 8px;
  padding: 16px;
}
.templately-wrapper .templately-feedback-form .review-body .review-wrapper .title {
  margin: 0;
  color: #1d2939;
  font-size: 16px;
  font-style: normal;
  font-weight: 500;
  line-height: 1.2;
}
.templately-wrapper .templately-feedback-form .review-body .review-wrapper .description {
  margin: 0;
  color: #667085;
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 1.4;
}
.templately-wrapper .templately-feedback-form .review-body .review-wrapper .description strong {
  font-weight: 600;
}
.templately-wrapper .templately-feedback-form .review-body .review-wrapper .ratting-wrapper {
  display: flex;
  flex-direction: column;
  gap: 10px;
}
.templately-wrapper .templately-feedback-form .review-body .review-wrapper .ratting-wrapper .review-ratings {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(75px, 1fr));
  gap: 10px;
}
.templately-wrapper .templately-feedback-form .review-body .review-wrapper .ratting-wrapper .review-ratings .rating {
  background-color: #f9fafb;
  border: 1px solid #f9fafb;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 8px;
  gap: 12px;
  text-align: center;
  cursor: pointer;
}
.templately-wrapper .templately-feedback-form .review-body .review-wrapper .ratting-wrapper .review-ratings .rating .icon {
  height: 40px;
  width: 40px;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}
.templately-wrapper .templately-feedback-form .review-body .review-wrapper .ratting-wrapper .review-ratings .rating.rating-1 .icon {
  background-image: url(../images/ratting1.png);
}
.templately-wrapper .templately-feedback-form .review-body .review-wrapper .ratting-wrapper .review-ratings .rating.rating-2 .icon {
  background-image: url(../images/ratting2.png);
}
.templately-wrapper .templately-feedback-form .review-body .review-wrapper .ratting-wrapper .review-ratings .rating.rating-3 .icon {
  background-image: url(../images/ratting3.png);
}
.templately-wrapper .templately-feedback-form .review-body .review-wrapper .ratting-wrapper .review-ratings .rating.rating-4 .icon {
  background-image: url(../images/ratting4.png);
}
.templately-wrapper .templately-feedback-form .review-body .review-wrapper .ratting-wrapper .review-ratings .rating.rating-5 .icon {
  background-image: url(../images/ratting5.png);
}
.templately-wrapper .templately-feedback-form .review-body .review-wrapper .ratting-wrapper .review-ratings .rating span {
  color: #1d2939;
  font-size: 16px;
  font-weight: 16px;
  line-height: 1.2;
  margin: 0;
}
.templately-wrapper .templately-feedback-form .review-body .review-wrapper .ratting-wrapper .review-ratings .rating:hover {
  border-color: #5453f4;
}
.templately-wrapper .templately-feedback-form .review-body .review-wrapper .ratting-wrapper .review-scale {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
}
.templately-wrapper .templately-feedback-form .review-body .review-wrapper .ratting-wrapper .review-scale .not-satisfied,
.templately-wrapper .templately-feedback-form .review-body .review-wrapper .ratting-wrapper .review-scale .very-satisfied {
  font-size: 14px;
  margin: 0;
  font-weight: 400;
  line-height: 1.2;
  color: #667085;
}
.templately-wrapper .templately-feedback-form .review-body .review-form {
  display: flex;
  flex-direction: column;
  gap: 24px;
}
.templately-wrapper .templately-feedback-form .review-body .review-form .form-control {
  display: flex;
  flex-direction: column;
  gap: 6px;
}
.templately-wrapper .templately-feedback-form .review-body .review-form .form-control label {
  color: #344054;
  font-size: 14px;
  font-weight: 500;
  line-height: 1.43;
}
.templately-wrapper .templately-feedback-form .review-body .review-form .form-control input {
  height: 44px;
  border: 1px solid #d0d5dd;
  background-color: transparent;
  padding: 2px 14px;
  border-radius: 8px;
  color: #1d2939;
}
.templately-wrapper .templately-feedback-form .review-body .review-form .form-control input::-moz-placeholder {
  color: #667085;
}
.templately-wrapper .templately-feedback-form .review-body .review-form .form-control input::placeholder {
  color: #667085;
}
.templately-wrapper .templately-feedback-form .review-body .review-form .form-control textarea {
  height: 100px;
  border: 1px solid #d0d5dd;
  background-color: transparent;
  padding: 10px 14px;
  border-radius: 8px;
  color: #1d2939;
}
.templately-wrapper .templately-feedback-form .review-body .review-form .form-control textarea::-moz-placeholder {
  color: #667085;
}
.templately-wrapper .templately-feedback-form .review-body .review-form .form-control textarea::placeholder {
  color: #667085;
}
.templately-wrapper .templately-feedback-form .review-body .review-form .form-control .error {
  margin: 0;
  color: #f9365b;
  font-size: 14px;
  font-weight: 400;
  line-height: 1.4;
}
.templately-wrapper .templately-feedback-form .review-body .review-form .review-submit {
  text-transform: capitalize;
  min-height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  gap: 8px;
  padding: 2px 24px;
  border-radius: 50px;
  background: #5453f4;
  color: #ffffff;
  font-size: 16px;
  font-weight: 500;
  line-height: normal;
  border: none;
  cursor: pointer;
}
.templately-wrapper .templately-feedback-form .review-footer {
  padding: 24px;
  background-color: #ffffff;
  border-top: 1px solid #eaecf0;
  display: flex;
  align-items: center;
  gap: 8px;
  border-bottom-right-radius: 16px;
}
.templately-wrapper .templately-feedback-form .review-footer .button {
  text-transform: capitalize;
  margin-right: auto;
  min-height: 48px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
  padding: 2px 24px;
  border-radius: 50px;
  background: #5453f4;
  color: #ffffff;
  font-size: 16px;
  font-weight: 500;
  line-height: normal;
  align-self: end;
  border: none;
  cursor: pointer;
}
.templately-wrapper .templately-feedback-form .review-footer .button > svg {
  margin-right: 10px;
}
.templately-wrapper .templately-collapse .tc-panel-item .tc-panel-header {
  display: flex;
  align-items: center;
  cursor: pointer;
}
.templately-wrapper .templately-collapse .tc-panel-item .tc-panel-header > h4 {
  margin: 0;
  flex: 1;
  font-size: 14px;
  font-weight: 600;
}
.templately-wrapper .templately-collapse .tc-panel-item .tc-panel-header.tc-panel-header-active {
  margin-bottom: 15px;
}
.templately-wrapper .templately-collapse .tc-panel-item .tc-panel-header > span.t-collapse-action-btn {
  display: inline-block;
  line-height: 1;
  cursor: pointer;
}
.templately-wrapper .templately-collapse .tc-panel-item .tc-panel-body ul,
.templately-wrapper .templately-collapse .tc-panel-item .tc-panel-body li:last-of-type {
  margin: 0;
}
.templately-wrapper .templately-sweetalert {
  z-index: 9999;
}
.templately-screen-templately.templately-wrapper .templately-sweetalert {
  margin-right: 160px;
}
.wp-admin.folded .templately-screen-templately.templately-wrapper .templately-sweetalert {
  margin-right: 36px !important;
}

@media only screen and (max-width: 960px) {
  .templately-screen-templately.templately-wrapper .templately-sweetalert {
    margin-right: 36px;
  }
}
@media screen and (max-width: 782px) {
  .templately-screen-templately.templately-wrapper .templately-sweetalert {
    margin-right: 0;
  }
}

.templately-wrapper .templately-sweetalert.swal2-backdrop-show {
  background: rgba(0, 0, 0, 0.7);
}
.templately-wrapper .templately-sweetalert .swal2-popup {
  padding: 0px;
  min-width: 600px;
}
.templately-wrapper .templately-sweetalert .swal2-popup.tp-large {
  width: 40em;
}
.templately-wrapper .templately-sweetalert .swal2-popup .swal2-icon.templately-warning-icon {
  border: 0px;
}
.templately-wrapper .templately-sweetalert .swal2-popup .swal2-html-container {
  margin: 30px;
  text-align: right;
  overflow: initial;
}
.templately-wrapper .templately-sweetalert .swal2-popup .swal2-html-container h3,
.templately-wrapper .templately-sweetalert .swal2-popup .swal2-html-container h4 {
  margin-top: 0;
}
.templately-wrapper .templately-sweetalert .swal2-popup .swal2-html-container .templately-sweetalert-icon {
  position: absolute;
  width: 100px;
  height: 100px;
  opacity: 0.1;
}
.templately-wrapper .templately-sweetalert .swal2-popup .swal2-html-container .templately-sweetalert-icon.--shape-1 {
  bottom: 0px;
  right: 0px;
}
.templately-wrapper .templately-sweetalert .swal2-popup .swal2-close {
  font-size: 25px;
  outline: none;
  box-shadow: none;
  z-index: 1;
}
.templately-wrapper .templately-sweetalert .swal2-popup .swal2-close.is-disabled {
  cursor: not-allowed;
}
.templately-wrapper .templately-sweetalert .swal2-popup .swal2-close:focus {
  outline: none;
  box-shadow: none;
}
.templately-wrapper .templately-sweetalert .swal2-popup .swal2-actions {
  margin: 0;
}
.templately-wrapper .templately-sweetalert .swal2-popup .swal2-actions .swal2-loader.templately-sweetalert-loader {
  position: absolute;
  right: 0;
  top: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 0px;
  animation: none;
  margin: 0;
  background: #fff;
  border-radius: 0;
}
.templately-wrapper .templately-sweetalert .tm-container {
  max-width: 90%;
  margin: 20px auto;
}
.templately-wrapper .templately-sweetalert .tm-container.text-center {
  text-align: center;
}
.templately-wrapper .templately-sweetalert .tm-container .tm-row {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}
.templately-wrapper .templately-sweetalert .tm-container .tm-row:last-of-type {
  margin-bottom: 0;
}
.templately-wrapper .templately-sweetalert .tm-container .tm-row > .tm-column:first-of-type:not(:last-of-type) {
  flex-basis: 25%;
  padding-left: 10px;
  text-align: left;
}
.templately-wrapper .templately-sweetalert .tm-container .tm-row > .tm-column:last-of-type {
  flex: 1;
}
.templately-wrapper .templately-sweetalert .tm-container .tm-column {
  box-sizing: border-box;
}
.templately-wrapper .templately-sweetalert .tm-container .tm-column.bg-light {
  padding: 10px;
  background-color: #f7f8ff;
  border: 1px solid #e6e9ff;
}
.templately-wrapper .templately-sweetalert .tm-container .tm-column.flex-end {
  display: flex;
  justify-content: flex-end;
}
.templately-wrapper .templately-sweetalert .tm-container input {
  padding: 0;
  padding-right: 10px;
  padding-left: 10px;
  min-height: 100%;
  box-shadow: none;
}
.templately-wrapper .templately-sweetalert .tm-container input:not([type=checkbox]) {
  width: 100%;
}
.templately-wrapper .templately-sweetalert .tm-container input:focus {
  border: none;
  outline: none;
  box-shadow: none;
}
.templately-wrapper .templately-sweetalert .tm-container .bg-light input {
  background: none;
  border: none;
}
.templately-wrapper .templately-sweetalert button:not(.swal2-close) {
  background-color: #6072ff;
  border: none;
  font-size: 16px;
  color: #fff;
  border-radius: 50px;
  padding: 10px 30px;
  text-transform: uppercase;
}
.templately-wrapper .templately-sweetalert button:not(.swal2-close)[disabled] {
  background-color: rgba(96, 114, 255, 0.62);
}
.templately-wrapper .templately-sweetalert button:not(.swal2-close):focus {
  outline: none;
  box-shadow: none;
}
.templately-wrapper .templately-sweetalert button:not(.swal2-close):hover {
  cursor: pointer;
}
.templately-wrapper .templately-sweetalert button:not(.swal2-close).tm-btn-cancel {
  background: linear-gradient(-135deg, #c0392b 50%, #c75347 50%);
}
.templately-wrapper .templately-sweetalert button:not(.swal2-close).tm-btn-confirm {
  background: linear-gradient(-135deg, #6072ff 50%, #6c7dff 50%);
}
.templately-wrapper .templately-sweetalert .tm-message.text-center {
  text-align: center;
}
.templately-wrapper .templately-sweetalert .templately-workspace-modal .tm-title span.tm-title-icon {
  background-image: url(../images/icon-workspace.png);
  width: 40px;
  height: 31px;
  display: inline-block;
  background-size: contain;
  background-repeat: no-repeat;
}
.templately-wrapper .templately-sweetalert .templately-workspace-modal .templately-shared-with {
  max-height: 200px;
  overflow-y: auto;
}
.templately-wrapper .templately-sweetalert .templately-workspace-modal .templately-shared-with ul.templately-added-email-list {
  margin-top: 0;
  margin-bottom: 10px;
  text-align: right;
  margin-bottom: 0px;
}
.templately-wrapper .templately-sweetalert .templately-workspace-modal .templately-shared-with ul.templately-added-email-list > li {
  display: inline-flex;
  font-size: 13px;
  align-items: center;
  justify-content: center;
  background: #e6e9ff;
  padding: 5px 10px;
  border-radius: 30px;
  color: #6072ff;
  margin-left: 3px;
}
.templately-wrapper .templately-sweetalert .templately-workspace-modal .templately-shared-with ul.templately-added-email-list > li > span:not(.tae-name) {
  display: inline-block;
  background: linear-gradient(45deg, transparent 0%, transparent 46%, #f84647 46%, #f84647 56%, transparent 56%, transparent 100%), linear-gradient(-45deg, transparent 0%, transparent 46%, #f84647 46%, #f84647 56%, transparent 56%, transparent 100%);
  width: 15px;
  height: 15px;
  border: 3px solid #e9d0e3;
  border-radius: 50%;
  margin-right: 5px;
  background-color: #e9d0e3;
  cursor: pointer;
}
.templately-wrapper .templately-sweetalert .templately-workspace-modal .templately-shared-with ul.templately-added-email-list > li > span.tae-undo, .templately-wrapper .templately-sweetalert .templately-workspace-modal .templately-shared-with ul.templately-added-email-list > li.removed > span.tae-remove {
  display: none;
}
.templately-wrapper .templately-sweetalert .templately-workspace-modal .templately-shared-with ul.templately-added-email-list > li.removed > span.tae-undo {
  display: block;
  background-image: url(../images/correct.svg);
  background-color: #d0e9d6;
  border-color: #d0e9d6;
  background-repeat: no-repeat;
  background-position: center center;
}
.templately-wrapper .templately-sweetalert .templately-workspace-modal .templately-shared-with ul.templately-added-email-list li {
  display: flex;
  justify-content: left;
  margin-left: 0;
  margin-bottom: 6px;
}
.templately-wrapper .templately-sweetalert .templately-workspace-modal .templately-shared-with ul.templately-added-email-list li:last-of-type {
  margin-bottom: 0;
}
.templately-wrapper .templately-sweetalert .templately-workspace-modal .templately-shared-with ul.templately-added-email-list li > span.tae-name {
  flex: 1;
}
.templately-wrapper .templately-sweetalert .templately-workspace-modal .templately-shared-with ul.templately-added-email-list li > span.tae-name > em {
  background: #6072ff;
  color: #fff;
  margin-right: 5px;
  padding: 2px 10px;
  border-radius: 30px;
  font-size: 10px;
}
.templately-wrapper .templately-sweetalert .templately-workspace-modal .templately-shared-with ul.templately-added-email-list li.removed > span.tae-name {
  text-decoration: line-through;
}
.templately-wrapper .templately-sweetalert .templately-workspace-modal .templately-shared-with ul.templately-added-email-list li > img {
  width: 30px;
  border-radius: 50%;
  margin-left: 10px;
}
.templately-wrapper .templately-sweetalert .templately-workspace-modal .templately-new-shared ul.templately-wem-new-email-list {
  margin-top: 0;
  margin-bottom: 10px;
  text-align: right;
}
.templately-wrapper .templately-sweetalert .templately-workspace-modal .templately-new-shared ul.templately-wem-new-email-list > li {
  display: inline-flex;
  font-size: 13px;
  align-items: center;
  justify-content: center;
  background: #e6e9ff;
  padding: 5px 10px;
  border-radius: 30px;
  color: #6072ff;
  margin-left: 3px;
}
.templately-wrapper .templately-sweetalert .templately-workspace-modal .templately-new-shared ul.templately-wem-new-email-list > li > span:not(.tae-name) {
  display: inline-block;
  background: linear-gradient(45deg, transparent 0%, transparent 46%, #f84647 46%, #f84647 56%, transparent 56%, transparent 100%), linear-gradient(-45deg, transparent 0%, transparent 46%, #f84647 46%, #f84647 56%, transparent 56%, transparent 100%);
  width: 15px;
  height: 15px;
  border: 3px solid #e9d0e3;
  border-radius: 50%;
  margin-right: 5px;
  background-color: #e9d0e3;
  cursor: pointer;
}
.templately-wrapper .templately-sweetalert .templately-workspace-modal .templately-new-shared ul.templately-wem-new-email-list > li > span.tae-undo, .templately-wrapper .templately-sweetalert .templately-workspace-modal .templately-new-shared ul.templately-wem-new-email-list > li.removed > span.tae-remove {
  display: none;
}
.templately-wrapper .templately-sweetalert .templately-workspace-modal .templately-new-shared ul.templately-wem-new-email-list > li.removed > span.tae-undo {
  display: block;
  background-image: url(../images/correct.svg);
  background-color: #d0e9d6;
  border-color: #d0e9d6;
  background-repeat: no-repeat;
  background-position: center center;
}
.templately-wrapper .templately-sweetalert .templately-workspace-modal .templately-new-shared > div {
  display: flex;
  border-radius: 50px;
  border: 1px solid #6072ff;
  overflow: hidden;
}
.templately-wrapper .templately-sweetalert .templately-workspace-modal .templately-new-shared > div > div {
  flex: 1;
}
.templately-wrapper .templately-sweetalert .templately-workspace-modal .templately-new-shared > div > div input {
  border: none;
}
.templately-wrapper .templately-sweetalert .templately-workspace-modal .templately-new-shared > div > div input:focus {
  outline: none;
  border: none;
  box-shadow: none;
}
.templately-wrapper .templately-sweetalert .templately-buttons-group {
  display: flex;
  gap: 5px;
}
.templately-wrapper .templately-sweetalert .tm-import-links {
  display: flex;
  gap: 5px;
  flex-wrap: wrap;
}
.templately-wrapper .templately-sweetalert .templately-import-button {
  background-color: #6072ff;
  border: none;
  font-size: 14px;
  color: #fff;
  border-radius: 50px;
  padding: 8px 16px;
}
.templately-wrapper .templately-sweetalert .templately-workspace-modal.delete-file {
  text-align: center;
}
.templately-wrapper .templately-sweetalert .templately-workspace-modal.delete-file .twm-df-actions {
  margin-top: 20px;
}
.templately-wrapper .templately-sweetalert .templately-workspace-modal.delete-file .twm-df-actions button.tb-delete {
  background-color: #ff6060;
  margin-right: 10px;
}
.templately-wrapper .templately-sweetalert .templately-workspace-modal.delete-workspace-modal {
  text-align: center;
}
.templately-wrapper .templately-sweetalert .templately-workspace-modal.delete-workspace-modal .tm-container .tm-row {
  margin-top: 40px;
  justify-content: center;
}
.templately-wrapper .templately-sweetalert .templately-workspace-modal.delete-workspace-modal .tm-container .tm-row .tm-column {
  flex-basis: auto !important;
  flex: none;
  padding: 0 !important;
}
.templately-wrapper .templately-sweetalert .templately-workspace-modal.delete-workspace-modal button {
  margin-left: 5px;
  margin-right: 5px;
  border-radius: 5px;
}
.templately-wrapper .templately-sweetalert .templately-import-modal p {
  font-size: 14px;
  line-height: 2;
}
.templately-wrapper .templately-sweetalert .templately-import-modal .templately-create-page {
  margin-bottom: 30px;
}
.templately-wrapper .templately-sweetalert .templately-import-modal .templately-create-page .templately-create-page-form {
  display: flex;
  gap: 5px;
}
.templately-wrapper .templately-sweetalert .templately-import-modal .templately-create-page .templately-create-page-form input {
  flex: 1;
  background-color: rgb(232, 240, 247);
  border: 0px;
  padding: 8px 15px;
}
.templately-wrapper .templately-sweetalert .templately-import-modal .templately-create-page .templately-create-page-form input:focus {
  box-shadow: none;
  outline: none;
}
.templately-wrapper .templately-sweetalert .templately-import-modal .templately-create-page .templately-create-page-form button.tb-create-page {
  border-radius: 5px;
  background-color: #5453fd;
  margin-right: 10px;
}
.templately-wrapper .templately-sweetalert .templately-import-modal .templately-import button.tb-import {
  background-color: transparent;
  color: #5453fd;
  padding: 0px;
}
.templately-wrapper .templately-sweetalert .templately-import-modal .templately-import button.tb-import:hover {
  background-color: transparent;
  color: rgb(4.0344827586, 2.6896551724, 231.3103448276);
}
.templately-wrapper .templately-sweetalert .templately-import-modal .templately-import button.tb-import > svg {
  margin-left: 10px;
}
.templately-wrapper .templately-sweetalert .tm-import-failed > h4 {
  margin-bottom: 10px;
}
.templately-wrapper .templately-sweetalert .templately-dependency-installer-modal > h4 {
  margin-bottom: 10px;
}
.templately-wrapper .templately-sweetalert .templately-dependency-installer-modal > p {
  margin-top: 0px;
  margin-bottom: 10px;
  font-size: 13px;
  line-height: 1.5;
}
.templately-wrapper .templately-sweetalert .templately-dependency-installer-modal .templately-dependency-list {
  margin: 18px 0px;
}
.templately-wrapper .templately-sweetalert .templately-dependency-installer-modal .templately-dependency-list .templately-dependency-item {
  display: flex;
  align-items: center;
  min-height: 26px;
  color: #5453fd;
}
.templately-wrapper .templately-sweetalert .templately-dependency-installer-modal .templately-dependency-list .templately-dependency-item .plugin-installed {
  color: #039855;
}
.templately-wrapper .templately-sweetalert .templately-dependency-installer-modal .templately-dependency-list .templately-dependency-item > .tdi-checkbox {
  display: flex;
  align-items: center;
}
.templately-wrapper .templately-sweetalert .templately-dependency-installer-modal .templately-dependency-list .templately-dependency-item > .tdi-checkbox .tdi-checkbox-input {
  margin: 0;
  margin-left: 5px;
}
.templately-wrapper .templately-sweetalert .templately-dependency-installer-modal .templately-dependency-list .templately-dependency-item a {
  color: #5453fd;
  text-decoration: none;
}
.templately-wrapper .templately-sweetalert .templately-dependency-installer-modal .templately-dependency-list .templately-dependency-item button.templately-button.tb-retry-install {
  background-color: transparent;
  margin: 0 10px 0 0;
  font-size: 10px;
  text-transform: none;
  color: #c0392b;
  border: 1px solid #c0392b;
  border-radius: 0;
  padding: 2px 5px;
}
.templately-wrapper .templately-sweetalert .templately-dependency-installer-modal .templately-dependency-list .templately-dependency-item button.templately-button.tb-retry-install:hover {
  background-color: #c0392b;
  color: #fff;
}
.templately-wrapper .templately-sweetalert .templately-dependency-installer-modal .templately-dependency-list .templately-dependency-item button.templately-button.tb-retry-install:hover span.text-danger {
  color: #fff !important;
}
.templately-wrapper .templately-sweetalert .templately-dependency-installer-modal .templately-dependency-list .templately-dependency-item .templately-installation-info {
  margin-right: 10px;
  margin-left: 10px;
}
.templately-wrapper .templately-sweetalert .templately-dependency-installer-modal .templately-dependency-list .templately-dependency-item .templately-installation-info > span {
  margin-left: 5px;
}
.templately-wrapper .templately-sweetalert .templately-dependency-installer-modal .templately-dependency-list .templately-dependency-item .templately-installation-info p {
  margin: 0;
}
.templately-wrapper .templately-sweetalert .templately-dependency-installer-modal .templately-buttons-group {
  margin-top: 15px;
}
.templately-wrapper .templately-sweetalert .templately-dependency-installer-modal .templately-buttons-group button {
  border-radius: 5px;
  margin-left: 10px;
  font-size: 13px;
  padding: 10px 15px;
}
.templately-wrapper .templately-dependency-modal-container {
  font-family: "Inter", system-ui;
  background: rgba(0, 0, 0, 0.7) !important;
  padding: 60px 10px 50px !important;
  z-index: 10000 !important;
}
.templately-wrapper .templately-dependency-modal-container.full-screen {
  padding: 0 !important;
  z-index: 159999 !important;
  margin-right: 0 !important;
}
.templately-wrapper .templately-dependency-modal-container .templately-dependency-modal-popup {
  width: calc(100% - 55px);
  max-width: 550px;
  min-width: initial !important;
  margin-left: 55px;
  border-radius: 24px;
  background: #ffffff;
  padding: 0;
  position: relative;
}
.templately-wrapper .templately-dependency-modal-container .templately-dependency-modal-popup.full-screen {
  width: 100vw !important;
  height: 100vh !important;
  min-width: 100vw !important;
  margin-left: 0 !important;
  border-radius: 0 !important;
}
.templately-wrapper .templately-dependency-modal-container .templately-dependency-modal-popup.full-screen .templately-dependency-modal-close-button {
  display: none !important;
}
.templately-wrapper .templately-dependency-modal-container .templately-dependency-modal-popup.full-screen .templately-dependency-modal-html {
  padding: 0 !important;
  border-radius: 0 !important;
}
.templately-wrapper .templately-dependency-modal-container .templately-dependency-modal-popup .templately-dependency-modal-close-button {
  width: 40px;
  height: 40px;
  display: inline-flex !important;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: #ffffff;
  color: #475467;
  position: absolute;
  top: 0;
  left: -55px;
  font-size: 32px !important;
  transition: all 0.2s ease-in-out;
  box-shadow: none !important;
}
.templately-wrapper .templately-dependency-modal-container .templately-dependency-modal-popup .templately-dependency-modal-close-button:hover {
  color: #000000;
}
.templately-wrapper .templately-dependency-modal-container .templately-dependency-modal-popup .templately-dependency-modal-html {
  margin: 0 !important;
  display: flex !important;
  flex-direction: column;
  align-items: flex-start;
  border-radius: 24px;
  overflow: hidden !important;
}
.templately-wrapper .templately-dependency-modal-container .templately-dependency-modal-popup .templately-dependency-modal-html .templately-confetti-canvas {
  right: 50% !important;
  top: 50% !important;
  bottom: auto !important;
  left: auto !important;
  transform: translate(50%, -50%) !important;
  pointer-events: none !important;
}
.templately-wrapper .templately-dependency-modal-container .templately-dependency-modal-popup .templately-dependency-modal-html .templately-dependency-modal-html-header {
  display: flex;
  flex-direction: column;
  gap: 10px;
  border-bottom: 1px solid var(--gray-200, #eaecf0);
  background-color: #f9fafb;
  background-image: url(../images/customizer-header.svg);
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  border-start-start-radius: 16px;
  border-start-end-radius: 16px;
  padding: 24px;
  width: 100%;
  box-sizing: border-box;
}
.templately-wrapper .templately-dependency-modal-container .templately-dependency-modal-popup .templately-dependency-modal-html .templately-dependency-modal-html-header .templately-title {
  color: #1d2939;
  font-size: 24px;
  font-weight: 500;
  line-height: 1.2;
  margin: 0;
}
.templately-wrapper .templately-dependency-modal-container .templately-dependency-modal-popup .templately-dependency-modal-html .templately-dependency-modal-html-header .templately-details {
  color: #475467;
  font-size: 16px;
  font-weight: 400;
  line-height: 1.3;
  margin: 0;
}
.templately-wrapper .templately-dependency-modal-container .templately-dependency-modal-popup .templately-dependency-modal-html .templately-dependency-modal-html-body {
  display: flex;
  flex-direction: column;
  gap: 13px;
  padding: 24px;
  width: 100%;
  box-sizing: border-box;
  background: #f9fafb;
}
.templately-wrapper .templately-dependency-modal-container .templately-dependency-modal-popup .templately-dependency-modal-html .templately-dependency-modal-html-body .templately-dependency-modal-html-body-inner {
  display: flex;
  flex-direction: column;
  gap: 10px;
  border-radius: 8px;
  border: 1px solid var(--gray-200, #eaecf0);
  background: #fff;
  padding: 24px;
}
.templately-wrapper .templately-dependency-modal-container .templately-dependency-modal-popup .templately-dependency-modal-html .templately-dependency-modal-html-body .templately-dmc-input {
  display: flex;
  flex-direction: column;
}
.templately-wrapper .templately-dependency-modal-container .templately-dependency-modal-popup .templately-dependency-modal-html .templately-dependency-modal-html-body .templately-dmc-input .templately-dmc-input-label {
  color: #344054;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 4px;
}
.templately-wrapper .templately-dependency-modal-container .templately-dependency-modal-popup .templately-dependency-modal-html .templately-dependency-modal-html-body .templately-dmc-input .has-error input:not([type=checkbox]):not([type=radio]) {
  border-color: #f04438;
}
.templately-wrapper .templately-dependency-modal-container .templately-dependency-modal-popup .templately-dependency-modal-html .templately-dependency-modal-html-body .templately-dmc-input input:not([type=checkbox]):not([type=radio]) {
  width: 100%;
  padding: 9px 20px;
  background-color: #ffffff;
  color: #20172f;
  border: 1px solid rgba(208, 213, 221, 0.5);
  border-radius: 6px;
  box-shadow: 0px 1px 2px 0px rgba(16, 24, 40, 0.05);
  font-size: 16px;
  line-height: 1.5;
}
.templately-wrapper .templately-dependency-modal-container .templately-dependency-modal-popup .templately-dependency-modal-html .templately-dependency-modal-html-body .templately-dmc-input input:not([type=checkbox]):not([type=radio])::-webkit-input-placeholder {
  color: #707c8e;
}
.templately-wrapper .templately-dependency-modal-container .templately-dependency-modal-popup .templately-dependency-modal-html .templately-dependency-modal-html-body .templately-dmc-input input:not([type=checkbox]):not([type=radio])::-moz-placeholder {
  color: #707c8e;
}
.templately-wrapper .templately-dependency-modal-container .templately-dependency-modal-popup .templately-dependency-modal-html .templately-dependency-modal-html-body .templately-dmc-input input:not([type=checkbox]):not([type=radio])::-ms-input-placeholder {
  color: #707c8e;
}
.templately-wrapper .templately-dependency-modal-container .templately-dependency-modal-popup .templately-dependency-modal-html .templately-dependency-modal-html-body .templately-dmc-input input:not([type=checkbox]):not([type=radio])::-moz-placeholder {
  color: #707c8e;
}
.templately-wrapper .templately-dependency-modal-container .templately-dependency-modal-popup .templately-dependency-modal-html .templately-dependency-modal-html-body .templately-dmc-input input:not([type=checkbox]):not([type=radio]):focus {
  box-shadow: none;
  outline: none;
  border-color: #bfc9d7;
}
.templately-wrapper .templately-dependency-modal-container .templately-dependency-modal-popup .templately-dependency-modal-html .templately-dependency-modal-html-body .templately-dmc-file-uploader-wrapper .templately-title {
  margin-top: 0;
  color: #1d2939;
  font-size: 16px;
  font-weight: 500;
  line-height: 1.3;
  margin-bottom: 4px;
}
.templately-wrapper .templately-dependency-modal-container .templately-dependency-modal-popup .templately-dependency-modal-html .templately-dependency-modal-html-body .templately-dmc-file-uploader-wrapper .templately-info {
  margin-top: 0;
  margin-bottom: 16px;
  color: #667085;
  font-size: 14px;
  font-weight: 400;
  line-height: 1.3;
}
.templately-wrapper .templately-dependency-modal-container .templately-dependency-modal-popup .templately-dependency-modal-html .templately-dependency-modal-html-body .templately-dmc-file-uploader-wrapper .templately-dmc-file-uploader {
  border-radius: 8px;
  border: 1px solid #eaecf0;
  background: #fff;
  display: flex;
  padding: 16px 24px;
  align-items: center;
  gap: 12px;
}
.templately-wrapper .templately-dependency-modal-container .templately-dependency-modal-popup .templately-dependency-modal-html .templately-dependency-modal-html-body .templately-dmc-file-uploader-wrapper .templately-dmc-file-uploader:hover {
  border-color: #bfc9d7;
}
.templately-wrapper .templately-dependency-modal-container .templately-dependency-modal-popup .templately-dependency-modal-html .templately-dependency-modal-html-body .templately-dmc-file-uploader-wrapper .templately-dmc-file-uploader input {
  display: none;
}
.templately-wrapper .templately-dependency-modal-container .templately-dependency-modal-popup .templately-dependency-modal-html .templately-dependency-modal-html-body .templately-dmc-file-uploader-wrapper .templately-dmc-file-uploader .icon {
  height: 80px;
  width: 80px;
  flex-shrink: 0;
  border-radius: 8px;
  background: #f5f7ff;
  display: flex;
  align-items: center;
  justify-content: center;
}
.templately-wrapper .templately-dependency-modal-container .templately-dependency-modal-popup .templately-dependency-modal-html .templately-dependency-modal-html-body .templately-dmc-file-uploader-wrapper .templately-dmc-file-uploader .icon img {
  width: 100%;
  height: 100%;
  border-radius: 8px;
  -o-object-fit: cover;
     object-fit: cover;
}
.templately-wrapper .templately-dependency-modal-container .templately-dependency-modal-popup .templately-dependency-modal-html .templately-dependency-modal-html-body .templately-dmc-file-uploader-wrapper .templately-dmc-file-uploader .content {
  display: flex;
  flex-direction: column;
}
.templately-wrapper .templately-dependency-modal-container .templately-dependency-modal-popup .templately-dependency-modal-html .templately-dependency-modal-html-body .templately-dmc-file-uploader-wrapper .templately-dmc-file-uploader .content .title {
  color: #667085;
  font-size: 12px;
  font-style: normal;
  font-weight: 500;
  line-height: 1.5;
  margin: 0;
  color: #475467;
}
.templately-wrapper .templately-dependency-modal-container .templately-dependency-modal-popup .templately-dependency-modal-html .templately-dependency-modal-html-body .templately-dmc-file-uploader-wrapper .templately-dmc-file-uploader .content .info {
  color: #667085;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 1.5;
}
.templately-wrapper .templately-dependency-modal-container .templately-dependency-modal-popup .templately-dependency-modal-html .templately-dependency-modal-html-body .templately-dmc-file-uploader-wrapper .templately-dmc-file-uploader .templately-file-upload-button {
  margin-right: auto;
  color: #597dfc;
  font-size: 12px;
  font-style: normal;
  font-weight: 700;
  line-height: 1.5;
  cursor: pointer;
}
.templately-wrapper .templately-dependency-modal-container .templately-dependency-modal-popup .templately-dependency-modal-html .templately-dependency-modal-html-footer {
  display: flex;
  align-items: center;
  gap: 10px;
  width: 100%;
  padding: 24px;
  box-sizing: border-box;
  border-top: 1px solid #eaecf0;
  background: #f9fafb;
}
.templately-wrapper .templately-dependency-modal-container .templately-dependency-modal-popup .templately-dependency-modal-html .templately-dependency-modal-html-footer .templately-button {
  text-transform: capitalize;
}
.templately-wrapper .templately-dependency-modal-container .templately-dependency-modal-popup .templately-dependency-modal-html .templately-dependency-modal-html-footer .templately-button.submit-button {
  min-height: 48px;
  padding: 10px 16px;
  background-color: #5453f4;
  margin-right: auto;
}
.templately-wrapper .templately-dependency-modal-container .templately-dependency-modal-popup .templately-dependency-modal-html .templately-dependency-modal-html-footer .templately-button.close-button {
  padding-right: 0;
  padding-left: 0;
  background-color: transparent;
  color: #475467;
}
.templately-wrapper .templately-dependency-modal-container .templately-dependency-modal-popup .templately-dependency-modal-html .templately-dependency-modal-html-footer .templately-button.close-button:hover {
  color: #f04438;
}
.templately-wrapper .templately-dependency-modal-container .templately-dependency-modal-popup .templately-dependency-modal-html .templately-dependency-modal-html-footer .templately-dmc-submit-button {
  text-transform: capitalize;
  margin-right: auto;
  min-height: 48px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 2px 24px;
  border-radius: 50px;
  background: #5453f4;
  color: #ffffff;
  font-size: 16px;
  font-weight: 500;
  line-height: normal;
  align-self: end;
}
.templately-wrapper .templately-dependency-modal-container .templately-dependency-modal-popup .templately-dependency-modal-html .templately-dependency-modal-html-footer .templately-dmc-submit-button > svg {
  margin-right: 10px;
}
.templately-wrapper .templately-dependency-modal-container .templately-dependency-modal-popup .templately-dependency-modal-html .templately-dependency-modal-html-footer.templately-button {
  color: #fff;
}
.templately-wrapper .templately-dependency-modal-customizer {
  width: 100%;
  height: 100%;
  display: flex;
}
.templately-wrapper .templately-dependency-modal-customizer .templately-dependency-modal-customizer-left {
  flex: 0 0 300px;
  max-width: 300px;
  border-left: 1px solid #eaecf0;
  background: #f9fafb;
  box-sizing: border-box;
  transition: all 0.2s ease-in-out;
  display: flex;
  flex-direction: column;
  position: relative;
}
.templately-wrapper .templately-dependency-modal-customizer .templately-dependency-modal-customizer-left .templately-dmc-header {
  background-image: url(../images/customizer-header.svg);
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  padding: 24px;
  display: flex;
  flex-direction: column;
  position: relative;
  gap: 10px;
  border-bottom: 1px solid #eaecf0;
}
.templately-wrapper .templately-dependency-modal-customizer .templately-dependency-modal-customizer-left .templately-dmc-header .templately-dmc-header-label {
  margin: 0;
  color: #475467;
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}
.templately-wrapper .templately-dependency-modal-customizer .templately-dependency-modal-customizer-left .templately-dmc-header .templately-dmc-header-title {
  margin: 0;
  color: #1d2939;
  font-size: 24px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
  margin-bottom: 3px;
}
.templately-wrapper .templately-dependency-modal-customizer .templately-dependency-modal-customizer-left .templately-dmc-header .templately-dmc-header-close-button {
  height: 32px;
  width: 32px;
  border: none;
  background-color: #fff;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  position: absolute;
  top: 8px;
  left: 8px;
}
.templately-wrapper .templately-dependency-modal-customizer .templately-dependency-modal-customizer-left .templately-dmc-header .templately-dmc-header-close-button svg {
  width: 10px;
  height: auto;
  fill: #475467;
}
.templately-wrapper .templately-dependency-modal-customizer .templately-dependency-modal-customizer-left .templately-dmc-header .templately-dmc-header-toggle-button {
  height: 24px;
  width: 24px;
  border: none;
  background-color: #fff;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  position: absolute;
  bottom: 0;
  left: 0;
  transform: translate(-50%, 50%);
  border: 1px solid #eaecf0;
  transition: all 0.2s ease-in-out;
  z-index: 2;
}
.templately-wrapper .templately-dependency-modal-customizer .templately-dependency-modal-customizer-left .templately-dmc-header .templately-dmc-header-toggle-button svg {
  width: 10px;
  height: auto;
  fill: #475467;
}
.templately-wrapper .templately-dependency-modal-customizer .templately-dependency-modal-customizer-left .templately-dmc-body {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  overflow: hidden;
}
.templately-wrapper .templately-dependency-modal-customizer .templately-dependency-modal-customizer-left .templately-dmc-body .templately-dmc-body-inner {
  padding: 40px 24px 0px;
  flex-grow: 1;
  overflow-y: auto;
}
.templately-wrapper .templately-dependency-modal-customizer .templately-dependency-modal-customizer-left .templately-dmc-body .templately-dmc-button-wrapper {
  padding: 0px 24px 40px;
  margin-top: auto;
}
.templately-wrapper .templately-dependency-modal-customizer .templately-dependency-modal-customizer-left .templately-dmc-body .templately-dmc-input {
  display: flex;
  flex-direction: column;
  margin-bottom: 16px;
}
.templately-wrapper .templately-dependency-modal-customizer .templately-dependency-modal-customizer-left .templately-dmc-body .templately-dmc-input .templately-dmc-input-label {
  color: #344054;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 6px;
}
.templately-wrapper .templately-dependency-modal-customizer .templately-dependency-modal-customizer-left .templately-dmc-body .templately-dmc-input .has-error input:not([type=checkbox]):not([type=radio]) {
  border-color: #f04438;
}
.templately-wrapper .templately-dependency-modal-customizer .templately-dependency-modal-customizer-left .templately-dmc-body .templately-dmc-input input:not([type=checkbox]):not([type=radio]) {
  width: 100%;
  padding: 9px 20px;
  background-color: #ffffff;
  color: #20172f;
  border: 1px solid rgba(208, 213, 221, 0.5);
  border-radius: 6px;
  box-shadow: 0px 1px 2px 0px rgba(16, 24, 40, 0.05);
  font-size: 16px;
  line-height: 1.5;
}
.templately-wrapper .templately-dependency-modal-customizer .templately-dependency-modal-customizer-left .templately-dmc-body .templately-dmc-input input:not([type=checkbox]):not([type=radio])::-webkit-input-placeholder {
  color: #707c8e;
  font-size: 12px;
}
.templately-wrapper .templately-dependency-modal-customizer .templately-dependency-modal-customizer-left .templately-dmc-body .templately-dmc-input input:not([type=checkbox]):not([type=radio])::-moz-placeholder {
  color: #707c8e;
  font-size: 12px;
}
.templately-wrapper .templately-dependency-modal-customizer .templately-dependency-modal-customizer-left .templately-dmc-body .templately-dmc-input input:not([type=checkbox]):not([type=radio])::-ms-input-placeholder {
  color: #707c8e;
  font-size: 12px;
}
.templately-wrapper .templately-dependency-modal-customizer .templately-dependency-modal-customizer-left .templately-dmc-body .templately-dmc-input input:not([type=checkbox]):not([type=radio])::-moz-placeholder {
  color: #707c8e;
  font-size: 12px;
}
.templately-wrapper .templately-dependency-modal-customizer .templately-dependency-modal-customizer-left .templately-dmc-body .templately-dmc-input input:not([type=checkbox]):not([type=radio]):focus {
  box-shadow: none;
  outline: none;
  border-color: #bfc9d7;
}
.templately-wrapper .templately-dependency-modal-customizer .templately-dependency-modal-customizer-left .templately-dmc-body .templately-dmc-file-uploader-wrapper {
  margin-bottom: 16px;
  display: block;
}
.templately-wrapper .templately-dependency-modal-customizer .templately-dependency-modal-customizer-left .templately-dmc-body .templately-dmc-file-uploader-wrapper .templately-title {
  margin-top: 0;
  margin-bottom: 8px;
  color: #1d2939;
  font-size: 16px;
  font-weight: 500;
  line-height: 1.3;
}
.templately-wrapper .templately-dependency-modal-customizer .templately-dependency-modal-customizer-left .templately-dmc-body .templately-dmc-file-uploader-wrapper .templately-info {
  margin-top: 0;
  margin-bottom: 16px;
  color: #667085;
  font-size: 14px;
  font-weight: 400;
  line-height: 1.3;
}
.templately-wrapper .templately-dependency-modal-customizer .templately-dependency-modal-customizer-left .templately-dmc-body .templately-dmc-file-uploader-wrapper .templately-dmc-file-uploader {
  border-radius: 8px;
  border: 1px solid #eaecf0;
  background: #fff;
  display: flex;
  padding: 16px 24px;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 4px;
  text-align: center;
  cursor: pointer;
}
.templately-wrapper .templately-dependency-modal-customizer .templately-dependency-modal-customizer-left .templately-dmc-body .templately-dmc-file-uploader-wrapper .templately-dmc-file-uploader:hover {
  border-color: #bfc9d7;
}
.templately-wrapper .templately-dependency-modal-customizer .templately-dependency-modal-customizer-left .templately-dmc-body .templately-dmc-file-uploader-wrapper .templately-dmc-file-uploader input {
  display: none;
}
.templately-wrapper .templately-dependency-modal-customizer .templately-dependency-modal-customizer-left .templately-dmc-body .templately-dmc-file-uploader-wrapper .templately-dmc-file-uploader .icon {
  margin-bottom: 8px;
  display: inline-flex;
  position: relative;
}
.templately-wrapper .templately-dependency-modal-customizer .templately-dependency-modal-customizer-left .templately-dmc-body .templately-dmc-file-uploader-wrapper .templately-dmc-file-uploader .icon .templately-dmc-file-remove-button {
  position: absolute;
  left: 3px;
  top: 3px;
  cursor: pointer;
  width: 20px;
  height: 20px;
  border-radius: 3px;
  background: #475467;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}
.templately-wrapper .templately-dependency-modal-customizer .templately-dependency-modal-customizer-left .templately-dmc-body .templately-dmc-file-uploader-wrapper .templately-dmc-file-uploader .icon .templately-dmc-file-remove-button:hover {
  background: #272e3a;
}
.templately-wrapper .templately-dependency-modal-customizer .templately-dependency-modal-customizer-left .templately-dmc-body .templately-dmc-file-uploader-wrapper .templately-dmc-file-uploader .icon img {
  max-width: 100%;
  max-height: 80px;
  padding: 10px;
  box-sizing: border-box;
  background-color: rgba(0, 0, 0, 0.1);
  box-shadow: 0 0 1px rgba(0, 0, 0, 0.25);
}
.templately-wrapper .templately-dependency-modal-customizer .templately-dependency-modal-customizer-left .templately-dmc-body .templately-dmc-file-uploader-wrapper .templately-dmc-file-uploader .title {
  display: block;
  color: #667085;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 1.43;
  margin: 0;
  text-transform: none;
}
.templately-wrapper .templately-dependency-modal-customizer .templately-dependency-modal-customizer-left .templately-dmc-body .templately-dmc-file-uploader-wrapper .templately-dmc-file-uploader .title .highlighted {
  color: #5453f4;
  font-weight: 500;
}
.templately-wrapper .templately-dependency-modal-customizer .templately-dependency-modal-customizer-left .templately-dmc-body .templately-dmc-file-uploader-wrapper .templately-dmc-file-uploader .info {
  color: #667085;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 1.5;
}
.templately-wrapper .templately-dependency-modal-customizer .templately-dependency-modal-customizer-left .templately-dmc-body .templately-dmc-file-uploader-wrapper .components-range-control label {
  text-transform: unset;
  font-size: 14px;
}
.templately-wrapper .templately-dependency-modal-customizer .templately-dependency-modal-customizer-left .templately-dmc-body .templately-dmc-file-uploader-wrapper .components-range-control .components-range-control__tooltip {
  min-width: 39px;
}
.templately-wrapper .templately-dependency-modal-customizer .templately-dependency-modal-customizer-left .templately-dmc-body .templately-dmc-submit-button {
  min-height: 48px;
  display: flex;
  width: 100%;
  align-items: center;
  justify-content: space-between;
  padding: 2px 24px;
  border-radius: 50px;
  background: #5453f4;
  color: #ffffff;
  font-size: 16px;
  font-weight: 500;
  line-height: normal;
  margin-top: 32px;
  text-transform: capitalize;
}
.templately-wrapper .templately-dependency-modal-customizer .templately-dependency-modal-customizer-left .templately-dmc-body .templately-dmc-back-button {
  min-height: 48px;
  display: flex;
  width: 100%;
  align-items: center;
  justify-content: space-between;
  padding: 2px 24px;
  border-radius: 50px;
  background: transparent;
  border: 1px solid #d0d5dd;
  color: #667085;
  font-size: 16px;
  font-weight: 500;
  line-height: normal;
  margin-top: 8px;
  text-transform: capitalize;
}
.templately-wrapper .templately-dependency-modal-customizer .templately-dependency-modal-customizer-left .templately-dmc-body .templately-dmc-submit-button:disabled, .templately-wrapper .templately-dependency-modal-customizer .templately-dependency-modal-customizer-left .templately-dmc-body .templately-dmc-back-button:disabled {
  border-color: #d0d5dd;
  color: #d0d5dd;
  opacity: 0.8;
  cursor: not-allowed;
}
.templately-wrapper .templately-dependency-modal-customizer .templately-dependency-modal-customizer-left .templately-dmc-body .templately-dmc-submit-button:disabled svg path, .templately-wrapper .templately-dependency-modal-customizer .templately-dependency-modal-customizer-left .templately-dmc-body .templately-dmc-back-button:disabled svg path {
  fill: #d0d5dd;
}
.templately-wrapper .templately-dependency-modal-customizer .templately-dependency-modal-customizer-left.show {
  margin-right: 0px;
}
.templately-wrapper .templately-dependency-modal-customizer .templately-dependency-modal-customizer-left.show .templately-dmc-header-toggle-button {
  transform: translate(-50%, 50%) rotate(-180deg);
}
.templately-wrapper .templately-dependency-modal-customizer .templately-dependency-modal-customizer-left.hide {
  margin-right: -300px;
}
.templately-wrapper .templately-dependency-modal-customizer .templately-dependency-modal-customizer-left.hide .templately-dmc-header-toggle-button {
  transform: translate(-150%, 50%) rotate(0deg);
}
.templately-wrapper .templately-dependency-modal-customizer .templately-dependency-modal-customizer-right {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  position: relative;
  background-color: #1d2327;
}
.templately-wrapper .templately-dependency-modal-customizer .templately-dependency-modal-customizer-right .templately-dependency-modal-customizer-right-overlay {
  position: absolute;
  inset: 0;
  z-index: 1;
}
.templately-wrapper .templately-dependency-modal-customizer .templately-dependency-modal-customizer-right .templately-dependency-modal-customizer-right-loader {
  position: absolute;
  inset: 0;
  z-index: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fff;
}
.templately-wrapper .templately-color-picker-section {
  display: flex;
  flex-direction: column;
  margin-bottom: 40px;
}
.templately-wrapper .templately-color-picker-section .templately-color-picker-section-label-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 4px;
}
.templately-wrapper .templately-color-picker-section .templately-color-picker-section-label {
  color: #344054;
  font-size: 18px;
  font-weight: 500;
}
.templately-wrapper .templately-color-picker-section .templately-color-picker-reset-button {
  background-color: transparent !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-size: 10px !important;
  padding: 0 !important;
  border-radius: 0 !important;
  color: #b7b9bd !important;
}
.templately-wrapper .templately-color-picker-section .templately-device-tab {
  display: flex;
  align-items: center;
}
.templately-wrapper .templately-color-picker-section .templately-device-tab .templately-device-tab-button {
  background: transparent !important;
  color: #d0d3da !important;
  height: 20px !important;
  width: 20px !important;
  margin: 0 !important;
  padding: 0 !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  border-radius: 0 !important;
}
.templately-wrapper .templately-color-picker-section .templately-device-tab .templately-device-tab-button .dashicons {
  height: 14px !important;
  width: 14px !important;
  margin: 0 !important;
  padding: 0 !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-size: 14px !important;
}
.templately-wrapper .templately-color-picker-section .templately-device-tab .templately-device-tab-button:not(:last-child) {
  border-left: 1px solid #d3d7de !important;
}
.templately-wrapper .templately-color-picker-section .templately-device-tab .templately-device-tab-button:first-child {
  border-top-right-radius: 4px !important;
  border-bottom-right-radius: 4px !important;
}
.templately-wrapper .templately-color-picker-section .templately-device-tab .templately-device-tab-button:last-child {
  border-top-left-radius: 4px !important;
  border-bottom-left-radius: 4px !important;
}
.templately-wrapper .templately-color-picker-section .templately-device-tab .templately-device-tab-button.active {
  color: #344054 !important;
}
.templately-wrapper .templately-color-picker-section .templately-device-tab .templately-device-tab-button:hover {
  background: #f0f0f0 !important;
}
.templately-wrapper .templately-color-picker-section .templately-color-picker-panel {
  padding-top: 20px;
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
}
.templately-wrapper .templately-color-picker-section .templately-color-picker-panel .templately-colorpicker-wrap .templately-colorpicker-screen {
  position: relative;
  cursor: pointer;
}
.templately-wrapper .templately-color-picker-section .templately-color-picker-panel .templately-colorpicker-wrap .templately-colorpicker-screen .templately-picker-display {
  display: block;
  height: 40px;
  width: 40px;
  border-radius: 100%;
  border: 1px solid #ccc;
}
.templately-wrapper .templately-color-picker-section .templately-color-picker-panel .templately-colorpicker-wrap .templately-colorpicker {
  padding: 12px 16px 16px;
  border-radius: 4px;
  position: absolute;
  bottom: 24px;
  right: calc(100% + 24px);
  z-index: 2;
  background-color: #fff;
  box-shadow: 15px 15px 50px 4px rgba(129, 129, 129, 0.2);
  width: 320px;
}
.templately-wrapper .templately-color-picker-section .templately-color-picker-panel .templately-colorpicker-wrap .templately-colorpicker .templately-title {
  margin-bottom: 8px;
  color: #1d2939;
  font-size: 16px;
  font-weight: 500;
  display: block;
}
.templately-wrapper .templately-color-picker-section .templately-color-picker-panel .templately-colorpicker-wrap .templately-colorpicker .components-color-picker {
  width: 100%;
}
.templately-wrapper .templately-color-picker-section .templately-color-picker-panel .templately-colorpicker-wrap .templately-colorpicker .components-color-picker .react-colorful {
  width: 100%;
}
.templately-wrapper .templately-color-picker-section .templately-color-picker-panel .templately-colorpicker-wrap .templately-colorpicker .components-color-picker .react-colorful .react-colorful__saturation {
  height: 290px;
  margin-bottom: 12px;
}
.templately-wrapper .templately-color-picker-section .templately-color-picker-panel .templately-colorpicker-wrap .templately-colorpicker .components-color-picker .react-colorful .react-colorful__hue,
.templately-wrapper .templately-color-picker-section .templately-color-picker-panel .templately-colorpicker-wrap .templately-colorpicker .components-color-picker .react-colorful .react-colorful__alpha {
  width: calc(100% - 12px);
  margin-bottom: 12px;
}
.templately-wrapper .templately-color-picker-section .templately-color-picker-panel .templately-colorpicker-wrap .templately-colorpicker .components-color-picker > div:not(.react-colorful) {
  display: flex;
  position: relative;
  padding: 0;
}
.templately-wrapper .templately-color-picker-section .templately-color-picker-panel .templately-colorpicker-wrap .templately-colorpicker .components-color-picker > div:not(.react-colorful) > .components-h-stack {
  width: 100%;
  position: relative;
}
.templately-wrapper .templately-color-picker-section .templately-color-picker-panel .templately-colorpicker-wrap .templately-colorpicker .components-color-picker > div:not(.react-colorful) > .components-h-stack .components-input-control__container {
  background: transparent;
}
.templately-wrapper .templately-color-picker-section .templately-color-picker-panel .templately-colorpicker-wrap .templately-colorpicker .components-color-picker > div:not(.react-colorful) > .components-h-stack .components-select-control__input {
  height: 44px;
  padding: 2px 14px;
  border-radius: 8px;
  border: 1px solid #d0d5dd;
  background: #ffffff;
  color: #101828;
  box-shadow: 0px 1px 2px 0px rgba(16, 24, 40, 0.05);
}
.templately-wrapper .templately-color-picker-section .templately-color-picker-panel .templately-colorpicker-wrap .templately-colorpicker .components-color-picker > div:not(.react-colorful) > .components-h-stack .components-input-control__suffix svg {
  fill: #101828;
}
.templately-wrapper .templately-color-picker-section .templately-color-picker-panel .templately-colorpicker-wrap .templately-colorpicker .components-color-picker > div:not(.react-colorful) > .components-h-stack .components-input-control__backdrop {
  display: none;
}
.templately-wrapper .templately-color-picker-section .templately-color-picker-panel .templately-colorpicker-wrap .templately-colorpicker .components-color-picker > div:not(.react-colorful) > .components-h-stack button.components-button {
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  background: transparent;
  border: none;
  padding: 0;
  z-index: 2;
  display: inline-flex;
}
.templately-wrapper .templately-color-picker-section .templately-color-picker-panel .templately-colorpicker-wrap .templately-colorpicker .components-color-picker > div:not(.react-colorful) > .components-h-stack button.components-button i,
.templately-wrapper .templately-color-picker-section .templately-color-picker-panel .templately-colorpicker-wrap .templately-colorpicker .components-color-picker > div:not(.react-colorful) > .components-h-stack button.components-button svg,
.templately-wrapper .templately-color-picker-section .templately-color-picker-panel .templately-colorpicker-wrap .templately-colorpicker .components-color-picker > div:not(.react-colorful) > .components-h-stack button.components-button img {
  width: 16px;
  font-size: 16px;
  fill: #101828;
}
.templately-wrapper .templately-color-picker-section .templately-color-picker-panel .templately-colorpicker-wrap .templately-colorpicker .components-color-picker > div:not(.react-colorful) > .components-flex:last-child {
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  z-index: 1;
  padding: 0;
  display: inline-flex;
  flex-direction: row;
  gap: 4px;
}
.templately-wrapper .templately-color-picker-section .templately-color-picker-panel .templately-colorpicker-wrap .templately-colorpicker .components-color-picker > div:not(.react-colorful) > .components-flex:last-child .components-input-control {
  height: 44px;
  display: flex;
  align-items: center;
  padding: 2px 14px;
  border-radius: 8px;
  border: 1px solid #d0d5dd;
  background: #ffffff;
  box-shadow: 0px 1px 2px 0px rgba(16, 24, 40, 0.05);
}
.templately-wrapper .templately-color-picker-section .templately-color-picker-panel .templately-colorpicker-wrap .templately-colorpicker .components-color-picker > div:not(.react-colorful) > .components-flex:last-child .components-input-control .components-input-control__container {
  background: transparent;
}
.templately-wrapper .templately-color-picker-section .templately-color-picker-panel .templately-colorpicker-wrap .templately-colorpicker .components-color-picker > div:not(.react-colorful) > .components-flex:last-child .components-input-control .components-input-control__container .components-input-control__prefix {
  margin: 0;
}
.templately-wrapper .templately-color-picker-section .templately-color-picker-panel .templately-colorpicker-wrap .templately-colorpicker .components-color-picker > div:not(.react-colorful) > .components-flex:last-child .components-input-control .components-input-control__container .components-input-control__prefix, .templately-wrapper .templately-color-picker-section .templately-color-picker-panel .templately-colorpicker-wrap .templately-colorpicker .components-color-picker > div:not(.react-colorful) > .components-flex:last-child .components-input-control .components-input-control__container .components-input-control__prefix span {
  color: #101828;
  margin: 0;
}
.templately-wrapper .templately-color-picker-section .templately-color-picker-panel .templately-colorpicker-wrap .templately-colorpicker .components-color-picker > div:not(.react-colorful) > .components-flex:last-child .components-input-control .components-input-control__container .components-input-control__input {
  padding: 0;
  min-width: auto;
  height: auto;
  min-height: auto;
  color: #101828;
}
.templately-wrapper .templately-color-picker-section .templately-color-picker-panel .templately-colorpicker-wrap .templately-colorpicker .components-color-picker > div:not(.react-colorful) > .components-flex:last-child .components-input-control .components-input-control__container .components-input-control__backdrop {
  display: none;
}
.templately-wrapper .templately-color-picker-section .templately-color-picker-panel .templately-colorpicker-wrap .templately-colorpicker .components-color-picker > div:not(.react-colorful) > .components-flex:last-child .components-h-stack .components-input-control {
  padding-right: 8px;
  padding-left: 8px;
  width: 57px;
}
.templately-wrapper .templately-color-picker-section .templately-color-picker-panel .templately-colorpicker-wrap .templately-colorpicker .components-color-picker > div:not(.react-colorful) > .components-flex:last-child .components-h-stack .components-input-control .components-input-control__prefix {
  display: none;
}
.templately-wrapper .templately-color-picker-section .templately-color-picker-panel .templately-colorpicker-wrap .templately-colorpicker .components-color-picker > div:not(.react-colorful) > .components-flex:last-child .components-h-stack:last-child .components-input-control {
  padding-left: 22px;
}
.templately-wrapper .templately-color-picker-section .templately-color-picker-panel .templately-colorpicker-wrap .templately-colorpicker .components-color-picker > div:not(.react-colorful) > .components-flex:last-child .components-range-control {
  display: none;
}
.templately-wrapper .templately-color-picker-section .templately-typography-panel-wrapper {
  padding-top: 16px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}
.templately-wrapper .templately-color-picker-section .templately-typography-panel-wrapper .templately-typography-panel {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 8px;
}
.templately-wrapper .templately-color-picker-section .templately-typography-panel-wrapper .templately-typography-panel .templately-typography-panel-item {
  display: flex;
  align-items: center;
  gap: 8px;
}
.templately-wrapper .templately-color-picker-section .templately-typography-panel-wrapper .templately-typography-panel .templately-typography-panel-item .templately-typography-mini-preview {
  height: 32px;
  width: 32px;
  border-radius: 6px;
  border: 1px solid #d0d5dd;
  display: inline-flex;
  flex-shrink: 0;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-size: 400;
  color: #667085;
}
.templately-wrapper .templately-color-picker-section .templately-typography-panel-wrapper .templately-typography-panel .templately-typography-panel-item .templately-typography-title {
  font-size: 14px;
  font-weight: 400;
  color: #667085;
}
.templately-wrapper .templately-color-picker-section .templately-typography-panel-wrapper .templately-typography-panel .templately-typography-panel-item .templately-typography-popup-button {
  margin-right: auto;
  height: 20px;
  width: 20px;
  border-radius: 100%;
  background-color: #f2f4f7;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  cursor: pointer;
}
.templately-wrapper .templately-color-picker-section .templately-typography-panel-wrapper .templately-typography-panel .templately-typography-panel-item .templately-typography-popup-button svg {
  width: 14px;
  height: 14px;
  fill: #667085;
}
.templately-wrapper .templately-color-picker-section .templately-typography-panel-wrapper .templately-typography-panel .templately-typography-picker {
  padding: 12px 16px 16px;
  border-radius: 4px;
  position: absolute;
  bottom: 24px;
  right: calc(100% + 24px);
  z-index: 2;
  background-color: #fff;
  box-shadow: 15px 15px 50px 4px rgba(129, 129, 129, 0.2);
  width: 320px;
  display: flex;
  flex-direction: column;
}
.templately-wrapper .templately-color-picker-section .templately-typography-panel-wrapper .templately-typography-panel .templately-typography-picker .templately-typography-picker-title-wrapper {
  margin-bottom: 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 8px;
}
.templately-wrapper .templately-color-picker-section .templately-typography-panel-wrapper .templately-typography-panel .templately-typography-picker .templately-typography-picker-title-wrapper .templately-typography-picker-title-inner {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  gap: 8px;
}
.templately-wrapper .templately-color-picker-section .templately-typography-panel-wrapper .templately-typography-panel .templately-typography-picker .templately-typography-picker-title-wrapper .templately-typography-picker-title-inner .templately-typography-picker-title {
  font-size: 16px;
  line-height: 1.25;
  margin-bottom: 0;
  font-weight: 500;
  color: #344054;
}
.templately-wrapper .templately-color-picker-section .templately-typography-panel-wrapper .templately-typography-panel .templately-typography-picker .templately-typography-picker-title-wrapper .templately-color-picker-close-button {
  background-color: transparent !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-size: 10px !important;
  padding: 0 !important;
  border-radius: 0 !important;
  color: #667085 !important;
  width: 20px;
  height: 20px;
}
.templately-wrapper .templately-color-picker-section .templately-typography-panel-wrapper .templately-typography-panel .templately-typography-picker .templately-typography-picker-preview {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px 4px;
  background-color: #f0f0f0;
  box-sizing: border-box;
  margin-bottom: 14px;
  color: #344054;
}
.templately-wrapper .templately-color-picker-section .templately-typography-panel-wrapper .templately-typography-panel .templately-typography-picker .templately-typography-picker-field {
  display: flex;
  gap: 5px;
  align-items: center;
}
.templately-wrapper .templately-color-picker-section .templately-typography-panel-wrapper .templately-typography-panel .templately-typography-picker .templately-typography-picker-field:not(:last-child) {
  margin-bottom: 14px;
}
.templately-wrapper .templately-color-picker-section .templately-typography-panel-wrapper .templately-typography-panel .templately-typography-picker .templately-typography-picker-field .templately-typography-picker-field-label {
  flex: 0 0 100px;
  font-size: 12px;
  font-weight: 500;
  color: #344054;
  line-height: 20px;
  display: inline-flex;
  align-items: center;
  gap: 6px;
}
.templately-wrapper .templately-color-picker-section .templately-typography-panel-wrapper .templately-typography-panel .templately-typography-picker .templately-typography-picker-field .templately-typography-picker-field-label .templately-device-dropdown {
  position: relative;
  display: flex;
  flex-direction: column;
  height: 20px;
  justify-content: center;
}
.templately-wrapper .templately-color-picker-section .templately-typography-panel-wrapper .templately-typography-panel .templately-typography-picker .templately-typography-picker-field .templately-typography-picker-field-label .templately-device-dropdown .templately-device-dropdown-selected {
  background: transparent !important;
  color: #667085 !important;
  height: 12px !important;
  width: 12px !important;
  margin: 0 !important;
  padding: 0 !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  border-radius: 0 !important;
}
.templately-wrapper .templately-color-picker-section .templately-typography-panel-wrapper .templately-typography-panel .templately-typography-picker .templately-typography-picker-field .templately-typography-picker-field-label .templately-device-dropdown .templately-device-dropdown-selected .dashicons {
  height: 12px !important;
  width: 12px !important;
  margin: 0 !important;
  padding: 0 !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-size: 12px !important;
}
.templately-wrapper .templately-color-picker-section .templately-typography-panel-wrapper .templately-typography-panel .templately-typography-picker .templately-typography-picker-field .templately-typography-picker-field-label .templately-device-dropdown .templately-device-dropdown-menu {
  position: absolute;
  top: 2px;
  right: -2px;
  margin: 0;
  display: flex;
  flex-direction: column;
  z-index: 1;
  visibility: hidden;
  opacity: 0;
}
.templately-wrapper .templately-color-picker-section .templately-typography-panel-wrapper .templately-typography-panel .templately-typography-picker .templately-typography-picker-field .templately-typography-picker-field-label .templately-device-dropdown .templately-device-dropdown-menu .templately-device-dropdown-menu-item {
  background: transparent !important;
  color: #d9d9d9 !important;
  height: 20px !important;
  width: 20px !important;
  margin: 0 !important;
  padding: 0 !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  cursor: pointer;
  border: 0.5px solid #d3d7de;
  background: #ffffff !important;
}
.templately-wrapper .templately-color-picker-section .templately-typography-panel-wrapper .templately-typography-panel .templately-typography-picker .templately-typography-picker-field .templately-typography-picker-field-label .templately-device-dropdown .templately-device-dropdown-menu .templately-device-dropdown-menu-item:first-child {
  border-top-right-radius: 2px;
  border-top-left-radius: 2px;
}
.templately-wrapper .templately-color-picker-section .templately-typography-panel-wrapper .templately-typography-panel .templately-typography-picker .templately-typography-picker-field .templately-typography-picker-field-label .templately-device-dropdown .templately-device-dropdown-menu .templately-device-dropdown-menu-item:not(:last-child) {
  border-bottom-width: 0px !important;
}
.templately-wrapper .templately-color-picker-section .templately-typography-panel-wrapper .templately-typography-panel .templately-typography-picker .templately-typography-picker-field .templately-typography-picker-field-label .templately-device-dropdown .templately-device-dropdown-menu .templately-device-dropdown-menu-item:last-child {
  border-bottom-right-radius: 2px;
  border-bottom-left-radius: 2px;
}
.templately-wrapper .templately-color-picker-section .templately-typography-panel-wrapper .templately-typography-panel .templately-typography-picker .templately-typography-picker-field .templately-typography-picker-field-label .templately-device-dropdown .templately-device-dropdown-menu .templately-device-dropdown-menu-item.active {
  color: #344054 !important;
}
.templately-wrapper .templately-color-picker-section .templately-typography-panel-wrapper .templately-typography-panel .templately-typography-picker .templately-typography-picker-field .templately-typography-picker-field-label .templately-device-dropdown .templately-device-dropdown-menu .templately-device-dropdown-menu-item:hover {
  background: #f0f0f0 !important;
}
.templately-wrapper .templately-color-picker-section .templately-typography-panel-wrapper .templately-typography-panel .templately-typography-picker .templately-typography-picker-field .templately-typography-picker-field-label .templately-device-dropdown .templately-device-dropdown-menu .templately-device-dropdown-menu-item .dashicons {
  height: 12px !important;
  width: 12px !important;
  margin: 0 !important;
  padding: 0 !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-size: 12px !important;
}
.templately-wrapper .templately-color-picker-section .templately-typography-panel-wrapper .templately-typography-panel .templately-typography-picker .templately-typography-picker-field .templately-typography-picker-field-label .templately-device-dropdown:hover .templately-device-dropdown-menu {
  visibility: visible;
  opacity: 1;
}
.templately-wrapper .templately-color-picker-section .templately-typography-panel-wrapper .templately-typography-panel .templately-typography-picker .templately-typography-picker-field .templately-typography-picker-field-inner {
  flex-grow: 1;
  max-width: calc(100% - 105px);
  display: flex;
  align-items: center;
  gap: 4px;
}
.templately-wrapper .templately-color-picker-section .templately-typography-panel-wrapper .templately-typography-panel .templately-typography-picker .templately-typography-picker-field .templately-typography-picker-field-inner .templately-typography-picker-select {
  width: 100%;
}
.templately-wrapper .templately-color-picker-section .templately-typography-panel-wrapper .templately-typography-panel .templately-typography-picker .templately-typography-picker-field .templately-typography-picker-field-inner .templately-typography-picker-select .templately-typography-picker-select__control {
  height: 34px;
  min-height: 34px;
  border-radius: 8px;
  border-color: #d0d5dd;
}
.templately-wrapper .templately-color-picker-section .templately-typography-panel-wrapper .templately-typography-panel .templately-typography-picker .templately-typography-picker-field .templately-typography-picker-field-inner .templately-typography-picker-select .templately-typography-picker-select__control .templately-typography-picker-select__value-container .templately-typography-picker-select__placeholder {
  font-size: 12px;
}
.templately-wrapper .templately-color-picker-section .templately-typography-panel-wrapper .templately-typography-panel .templately-typography-picker .templately-typography-picker-field .templately-typography-picker-field-inner .templately-typography-picker-select .templately-typography-picker-select__control .templately-typography-picker-select__value-container .templately-typography-picker-select__single-value {
  color: #667085;
  font-size: 12px;
  font-weight: 400;
}
.templately-wrapper .templately-color-picker-section .templately-typography-panel-wrapper .templately-typography-panel .templately-typography-picker .templately-typography-picker-field .templately-typography-picker-field-inner .templately-typography-picker-select .templately-typography-picker-select__control .templately-typography-picker-select__value-container .templately-typography-picker-select__input-container {
  margin-top: 0;
  margin-bottom: 0;
  padding-top: 0;
  padding-bottom: 0;
}
.templately-wrapper .templately-color-picker-section .templately-typography-panel-wrapper .templately-typography-panel .templately-typography-picker .templately-typography-picker-field .templately-typography-picker-field-inner .templately-typography-picker-select .templately-typography-picker-select__control .templately-typography-picker-select__value-container .templately-typography-picker-select__input-container .templately-typography-picker-select__input {
  min-height: 24px;
  color: #667085;
  font-size: 12px;
  font-weight: 400;
}
.templately-wrapper .templately-color-picker-section .templately-typography-panel-wrapper .templately-typography-panel .templately-typography-picker .templately-typography-picker-field .templately-typography-picker-field-inner .templately-typography-picker-select .templately-typography-picker-select__control .templately-typography-picker-select__value-container .templately-typography-picker-select__input-container .templately-typography-picker-select__input:focus {
  box-shadow: none !important;
}
.templately-wrapper .templately-color-picker-section .templately-typography-panel-wrapper .templately-typography-panel .templately-typography-picker .templately-typography-picker-field .templately-typography-picker-field-inner .templately-typography-picker-select .templately-typography-picker-select__control .templately-typography-picker-select__indicators .templately-typography-picker-select__indicator-separator {
  margin-top: 0;
  margin-bottom: 0;
  background-color: #d0d5dd;
}
.templately-wrapper .templately-color-picker-section .templately-typography-panel-wrapper .templately-typography-panel .templately-typography-picker .templately-typography-picker-field .templately-typography-picker-field-inner .templately-typography-picker-select .templately-typography-picker-select__control .templately-typography-picker-select__indicators .templately-typography-picker-select__indicator svg {
  height: 16px;
  width: 16px;
  color: #667085;
}
.templately-wrapper .templately-color-picker-section .templately-typography-panel-wrapper .templately-typography-panel .templately-typography-picker .templately-typography-picker-field .templately-typography-picker-field-inner .templately-typography-picker-select .templately-typography-picker-select__menu {
  padding: 5px 0px;
  line-height: 18px;
}
.templately-wrapper .templately-color-picker-section .templately-typography-panel-wrapper .templately-typography-panel .templately-typography-picker .templately-typography-picker-field .templately-typography-picker-field-inner .templately-typography-picker-select .templately-typography-picker-select__menu .templately-typography-picker-select__option {
  font-size: 13px;
}
.templately-wrapper .templately-color-picker-section .templately-typography-panel-wrapper .templately-typography-panel .templately-typography-picker .templately-typography-picker-field .templately-typography-picker-field-inner .components-text-control__input {
  height: 34px;
  min-height: 34px;
  border-radius: 8px;
  border-color: #d0d5dd;
  color: #667085;
  font-size: 12px;
  font-weight: 400;
  width: 65px;
}
.templately-wrapper .templately-color-picker-section .templately-typography-panel-wrapper .templately-typography-panel .templately-typography-picker .templately-typography-picker-field .templately-typography-picker-field-inner .components-select-control .components-select-control__input {
  width: 42px;
  padding: 0;
  border: none;
  color: #667085;
  font-size: 12px;
  font-weight: 400;
  text-transform: lowercase;
}
.templately-wrapper .templately-color-picker-section .templately-typography-panel-wrapper .templately-typography-panel .templately-typography-picker .templately-typography-picker-field .templately-typography-picker-field-inner .components-select-control .components-input-control__backdrop {
  display: none;
}
.templately-wrapper .templately-color-picker-section .templately-typography-panel-wrapper .templately-typography-panel .templately-typography-picker .templately-typography-picker-field .templately-typography-picker-field-inner .components-range-control {
  width: 100%;
}
.templately-wrapper .templately-color-picker-section .templately-typography-panel-wrapper .templately-typography-panel .templately-typography-picker .templately-typography-picker-field .templately-typography-picker-field-inner .components-range-control .components-range-control__thumb-wrapper,
.templately-wrapper .templately-color-picker-section .templately-typography-panel-wrapper .templately-typography-panel .templately-typography-picker .templately-typography-picker-field .templately-typography-picker-field-inner .components-range-control .components-range-control__thumb-wrapper span {
  background-color: #d0d5dd;
  color: #d0d5dd;
}
.templately-wrapper .templately-dependency-processing-wrapper {
  position: relative;
  background-color: #f9fafb;
  padding: 24px;
  border-radius: 8px;
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 12px;
  box-sizing: border-box;
  margin-top: 0;
  margin-bottom: 0;
}
.templately-wrapper .templately-dependency-processing-wrapper .templately-dependency-processing-item {
  display: flex;
  margin-bottom: 0;
  justify-content: space-between;
  gap: 10px;
  color: #475467;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 1.3;
}
.templately-wrapper .templately-dependency-processing-wrapper .templately-dependency-processing-item .templately-label {
  display: flex;
  align-items: center;
  gap: 12px;
}
.templately-wrapper .templately-dependency-processing-wrapper .templately-dependency-processing-item .templately-label:before {
  content: "";
  height: 16px;
  width: 16px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background-size: 16px;
  background-position: center;
  background-repeat: no-repeat;
}
.templately-wrapper .templately-dependency-processing-wrapper .templately-dependency-processing-item.complete .templately-label:before {
  background-image: url(../images/check_circle.svg);
}
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(-360deg);
  }
}
.templately-wrapper .templately-dependency-processing-wrapper .templately-dependency-processing-item.processing .templately-label:before {
  background-image: url(../images/progress_activity.svg);
  animation-name: spin;
  animation-duration: 2s;
  animation-iteration-count: infinite;
  animation-timing-function: linear;
}
.templately-wrapper .templately-dependency-processing-wrapper .templately-dependency-processing-item.waiting {
  color: #98a2b3;
}
.templately-wrapper .templately-dependency-processing-wrapper .templately-dependency-processing-item.waiting .templately-label:before {
  visibility: hidden;
  opacity: 0;
  pointer-events: none;
}
.templately-wrapper .templately-dependency-processing-wrapper #templately-fsi-retry-button {
  position: absolute;
  top: 10px;
  left: 10px;
  background: none;
  border: none;
  cursor: pointer;
}
@media only screen and (max-width: 1800px) {
  .templately-wrapper .ReactModal__Overlay .ril__image {
    transform: none !important;
    max-width: 85% !important;
  }
}
.templately-wrapper .templately-new-collapser {
  background-color: #fff;
  padding: 16px;
  padding-left: 8px;
  border-radius: 8px;
  border: 1px solid var(--gray-200, #eaecf0);
}
.templately-wrapper .templately-new-collapser .templately-new-collapser-header {
  display: flex;
  align-items: center;
  cursor: pointer;
  gap: 10px;
  padding-left: 8px;
}
.templately-wrapper .templately-new-collapser .templately-new-collapser-header .templately-title {
  color: #1d2939;
  font-size: 14px;
  font-weight: 500;
  line-height: normal;
  margin: 0;
}
.templately-wrapper .templately-new-collapser .templately-new-collapser-header .templately-icon {
  height: 24px;
  width: 24px;
  margin-right: auto;
  flex-shrink: 0;
  border-radius: 24px;
  background-color: #f2f4f7;
  background-image: none !important;
  color: #475467;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}
.templately-wrapper .templately-new-collapser .templately-new-collapser-header .templately-icon svg {
  width: 10px;
  transition: transform 0.1s linear;
}
.templately-wrapper .templately-new-collapser .templately-new-collapser-header.show .templately-icon svg {
  transform: rotate(-180deg);
}
.templately-wrapper .templately-new-collapser .templately-new-collapser-header.collapsed .templately-icon svg {
  transform: rotate(0deg);
}
.templately-wrapper .templately-new-collapser .templately-new-collapser-body {
  display: grid;
  transition: grid-template-rows 0.15s ease-in-out, padding-top 0.15s ease-in-out;
}
.templately-wrapper .templately-new-collapser .templately-new-collapser-body.show {
  grid-template-rows: 1fr;
  padding-top: 18px;
}
.templately-wrapper .templately-new-collapser .templately-new-collapser-body.show .templately-new-collapser-body-inner {
  overflow: auto;
  max-height: 200px;
  padding-left: 4px;
}
.templately-wrapper .templately-new-collapser .templately-new-collapser-body.show .templately-new-collapser-body-inner::-webkit-scrollbar {
  width: 4px;
}
.templately-wrapper .templately-new-collapser .templately-new-collapser-body.show .templately-new-collapser-body-inner::-webkit-scrollbar-track {
  background: #fff;
  border-radius: 5px;
}
.templately-wrapper .templately-new-collapser .templately-new-collapser-body.show .templately-new-collapser-body-inner::-webkit-scrollbar-thumb {
  background: #f2f4f7;
  border-radius: 5px;
}
.templately-wrapper .templately-new-collapser .templately-new-collapser-body.collapsed {
  grid-template-rows: 0fr;
}
.templately-wrapper .templately-new-collapser .templately-new-collapser-body.collapsed .templately-new-collapser-body-inner {
  overflow: hidden;
}
.templately-wrapper .templately-checkbox {
  display: block;
  width: 100%;
}
.templately-wrapper .templately-checkbox:not(:last-child) {
  margin-bottom: 12px;
}
.templately-wrapper .templately-checkbox .templately-checkbox-input {
  display: none;
}
.templately-wrapper .templately-checkbox .templately-checkbox-input + .templately-checkbox-content {
  display: inline-block;
  padding-right: 32px;
  position: relative;
}
.templately-wrapper .templately-checkbox .templately-checkbox-input + .templately-checkbox-content .templately-checkbox-content-inner {
  color: #475467;
  font-size: 16px;
  font-style: normal;
  font-weight: 500;
  line-height: 1.5;
}
.templately-wrapper .templately-checkbox .templately-checkbox-input + .templately-checkbox-content .templately-checkbox-content-inner span.installed {
  color: #039855;
}
.templately-wrapper .templately-checkbox .templately-checkbox-input + .templately-checkbox-content .templately-checkbox-content-inner span.recommended {
  color: #d0d5dd;
}
.templately-wrapper .templately-checkbox .templately-checkbox-input + .templately-checkbox-content:before {
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  content: "";
  height: 20px;
  width: 20px;
  min-width: 20px;
  border-radius: 6px;
  border: 1px solid #d0d5dd;
  background: #ffffff;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
}
.templately-wrapper .templately-checkbox .templately-checkbox-input:checked + .templately-checkbox-content:before {
  border-color: #5453f4;
  background-image: url(../images/check.svg);
  background-size: 14px;
  background-position: center;
  background-repeat: no-repeat;
}
.templately-wrapper .templately-checkbox .templately-checkbox-input:disabled + .templately-checkbox-content:before {
  border-color: #039855;
  background-color: #039855;
  background-image: url(../images/check-white.svg);
  background-size: 14px;
  background-position: center;
  background-repeat: no-repeat;
}
.templately-wrapper .templately-dependency-processing-wrapper {
  background-color: #ffffff;
  border-radius: 8px;
  border: 1px solid var(--gray-200, #eaecf0);
  padding: 24px;
  border-radius: 8px;
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 12px;
  box-sizing: border-box;
  margin-top: 0;
  margin-bottom: 0;
}
.templately-wrapper .templately-dependency-processing-wrapper .templately-dependency-processing-item {
  display: flex;
  margin-bottom: 0;
  justify-content: space-between;
  gap: 10px;
  color: #475467;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 1.3;
}
.templately-wrapper .templately-dependency-processing-wrapper .templately-dependency-processing-item .templately-label {
  display: flex;
  gap: 12px;
}
.templately-wrapper .templately-dependency-processing-wrapper .templately-dependency-processing-item .templately-label:before {
  content: "";
  height: 16px;
  width: 16px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background-size: 16px;
  background-position: center;
  background-repeat: no-repeat;
  flex-shrink: 0;
  margin-top: 1px;
}
.templately-wrapper .templately-dependency-processing-wrapper .templately-dependency-processing-item.no-icon .templately-label:before {
  display: none;
}
.templately-wrapper .templately-dependency-processing-wrapper .templately-dependency-processing-item.warning {
  padding-right: 20px;
  position: relative;
  display: flex;
  flex-direction: column;
  gap: 10px;
}
.templately-wrapper .templately-dependency-processing-wrapper .templately-dependency-processing-item.warning:before {
  content: "";
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  width: 5px;
  border-radius: 5px;
  background-color: #ffb45a;
}
.templately-wrapper .templately-dependency-processing-wrapper .templately-dependency-processing-item.warning .templately-title {
  font-size: 18px;
  line-height: 18px;
  font-weight: normal;
  margin: 0;
  color: #ffb45a;
}
.templately-wrapper .templately-dependency-processing-wrapper .templately-dependency-processing-item.complete .templately-label:before {
  background-image: url(../images/check_circle.svg);
}
.templately-wrapper .templately-dependency-processing-wrapper .templately-dependency-processing-item.error .templately-label:before {
  background-image: url(../images/cross_red_circle.svg);
}
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(-360deg);
  }
}
.templately-wrapper .templately-dependency-processing-wrapper .templately-dependency-processing-item.processing .templately-label:before {
  background-image: url(../images/progress_activity.svg);
  animation-name: spin;
  animation-duration: 2s;
  animation-iteration-count: infinite;
  animation-timing-function: linear;
}
.templately-wrapper .templately-dependency-processing-wrapper .templately-dependency-processing-item.waiting {
  color: #98a2b3;
}
.templately-wrapper .templately-dependency-processing-wrapper .templately-dependency-processing-item.waiting .templately-label:before {
  visibility: hidden;
  opacity: 0;
  pointer-events: none;
}
.templately-wrapper .templately-dependency-processing-wrapper .templately-dependency-processing-item.has-sub-menu {
  flex-direction: column;
  justify-content: center;
  align-items: space-between;
  gap: 0px;
}
.templately-wrapper .templately-dependency-processing-wrapper .templately-dependency-processing-item.has-sub-menu .templately-label .templately-collapse-icon {
  height: 24px;
  width: 24px;
  margin-right: auto;
  flex-shrink: 0;
  border-radius: 24px;
  background-color: #f2f4f7;
  background-image: none !important;
  color: #475467;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}
.templately-wrapper .templately-dependency-processing-wrapper .templately-dependency-processing-item.has-sub-menu .templately-label .templately-collapse-icon svg {
  width: 10px;
  transition: transform 0.1s linear;
}
.templately-wrapper .templately-dependency-processing-wrapper .templately-dependency-processing-item.has-sub-menu .templately-dependency-processing-details-wrapper {
  display: grid;
  padding-right: 28px;
  transition: grid-template-rows 0.15s ease-in-out, padding-top 0.15s ease-in-out;
}
.templately-wrapper .templately-dependency-processing-wrapper .templately-dependency-processing-item.has-sub-menu.collapsed .templately-dependency-processing-details-wrapper {
  grid-template-rows: 0fr;
}
.templately-wrapper .templately-dependency-processing-wrapper .templately-dependency-processing-item.has-sub-menu.collapsed .templately-dependency-processing-details-wrapper .templately-dependency-processing-details {
  overflow: hidden;
}
.templately-wrapper .templately-dependency-processing-wrapper .templately-dependency-processing-item.has-sub-menu.show .templately-label .templately-icon svg {
  transform: rotate(-180deg);
}
.templately-wrapper .templately-dependency-processing-wrapper .templately-dependency-processing-item.has-sub-menu.show .templately-dependency-processing-details-wrapper {
  grid-template-rows: 1fr;
  padding-top: 10px;
}
.templately-wrapper .templately-dependency-processing-wrapper .templately-dependency-processing-item.has-sub-menu.show .templately-dependency-processing-details-wrapper .templately-dependency-processing-details {
  overflow: auto;
}
.templately-wrapper .templately-dependency-processing-wrapper .templately-dependency-processing-item.has-sub-menu.show .templately-dependency-processing-details-wrapper .templately-dependency-processing-details a {
  color: rgb(84, 83, 253) !important;
  text-decoration: none;
}
.templately-wrapper .templately-dependency-processing-wrapper .templately-dependency-processing-item.has-sub-menu.show .templately-dependency-processing-details-wrapper .templately-dependency-processing-details a:hover {
  text-decoration: underline;
}

.templately-display-condition-modal-wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding-right: 20px;
  padding-left: 20px;
}

.templately-display-condition-modal-header {
  background: #ffffff;
  border-block-end: 1px solid #e6e8ea;
  border-start-start-radius: 8px;
  border-start-end-radius: 8px;
  display: flex;
  align-items: center;
  gap: 10px;
  padding-inline: 24px;
  padding-block: 16px;
}
.templately-display-condition-modal-header .templately-logo {
  display: flex;
  align-items: center;
  gap: 12px;
}
.templately-display-condition-modal-header .templately-logo .text-logo {
  height: 28px;
}
.templately-display-condition-modal-header .close-button {
  all: unset;
  margin-inline-start: auto;
  display: inline-flex;
  align-items: center;
}
.templately-display-condition-modal-header .close-button svg {
  width: 24px;
  fill: #8c959f;
}
.templately-display-condition-modal-header .close-button:hover svg {
  fill: #3c434a;
}

.templately-display-condition-modal-body {
  background: #ffffff;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding-block-start: 112px;
  padding-block-end: 56px;
  font-family: "DM Sans", sans-serif;
  overflow-y: auto;
}
.templately-display-condition-modal-body h1 {
  font-size: 30px;
  line-height: 1.1;
  color: #000000;
}
.templately-display-condition-modal-body h1:not(:last-child) {
  margin-block-end: 24px;
}
.templately-display-condition-modal-body p {
  font-size: 18px;
  line-height: 1.43;
  color: #525962;
  margin-block-start: 0;
}
.templately-display-condition-modal-body p:not(:last-child) {
  margin-block-end: 32px;
}
.templately-display-condition-modal-body .templately-display-conditions {
  width: 100%;
  max-width: 700px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 8px;
}
.templately-display-condition-modal-body .templately-display-conditions .templately-display-condition-wrapper {
  width: 100%;
  display: flex;
  align-items: center;
  gap: 8px;
}
.templately-display-condition-modal-body .templately-display-conditions .templately-display-condition-wrapper .templately-display-condition {
  flex-grow: 1;
  display: flex;
  border: 1px solid #e6e8ea;
  border-radius: 0;
}
.templately-display-condition-modal-body .templately-display-conditions .templately-display-condition-wrapper .templately-display-condition .templately-condition.templately-condition-type {
  width: 120px;
}
.templately-display-condition-modal-body .templately-display-conditions .templately-display-condition-wrapper .templately-display-condition .templately-condition.templately-condition-id .templately-condition__input-container {
  width: 120px !important;
}
.templately-display-condition-modal-body .templately-display-conditions .templately-display-condition-wrapper .templately-display-condition .templately-condition.templately-condition-id .templately-condition__input-container .templately-condition__input {
  width: 120px !important;
  text-overflow: ellipsis;
  border: none !important;
  box-shadow: none !important;
  outline: none !important;
}
.templately-display-condition-modal-body .templately-display-conditions .templately-display-condition-wrapper .templately-display-condition .templately-condition:not(.templately-condition-type) {
  flex-grow: 1;
}
.templately-display-condition-modal-body .templately-display-conditions .templately-display-condition-wrapper .templately-display-condition .templately-condition .templately-condition__control {
  border-block-start: none !important;
  border-block-end: none !important;
  border-inline-start: none !important;
  border-radius: 0 !important;
  background-color: transparent !important;
}
.templately-display-condition-modal-body .templately-display-conditions .templately-display-condition-wrapper .templately-display-condition .templately-condition .templately-condition__control .templately-condition__input-container .templately-condition__input {
  min-height: 20px !important;
}
.templately-display-condition-modal-body .templately-display-conditions .templately-display-condition-wrapper .templately-display-condition .templately-condition:last-child .templately-condition__control {
  border-inline-end: 0 !important;
}
.templately-display-condition-modal-body .templately-display-conditions .templately-display-condition-wrapper .templately-display-condition .templately-condition:not(:last-child) .templately-condition__control {
  border-inline-end: 1px solid #e6e8ea !important;
}
.templately-display-condition-modal-body .templately-display-conditions .templately-display-condition-wrapper .delete-button {
  all: unset;
  margin-inline-start: auto;
  display: inline-flex;
  align-items: center;
}
.templately-display-condition-modal-body .templately-display-conditions .templately-display-condition-wrapper .delete-button svg {
  width: 24px;
  fill: #8c959f;
}
.templately-display-condition-modal-body .templately-display-conditions .templately-display-condition-wrapper .delete-button:hover svg {
  fill: #3c434a;
}
.templately-display-condition-modal-body .templately-display-conditions .templately-button {
  margin-block-start: 42px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background-color: #5453fd;
  color: #fff;
  padding: 10px 30px;
  border-radius: 5px;
  letter-spacing: 1px;
  text-transform: uppercase;
  cursor: pointer;
  border: 0;
  box-shadow: none;
  outline: none;
  text-decoration: none;
  box-sizing: border-box;
}

.templately-display-condition-modal-footer {
  background: #ffffff;
  border-block-start: 1px solid #e6e8ea;
  border-end-start-radius: 8px;
  border-end-end-radius: 8px;
  padding-inline: 24px;
  padding-block: 16px;
  display: flex;
  align-items: center;
}
.templately-display-condition-modal-footer .templately-button {
  margin-inline-start: auto;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background-color: #5453fd;
  color: #fff;
  padding: 12px 32px;
  border-radius: 5px;
  letter-spacing: 1px;
  text-transform: uppercase;
  cursor: pointer;
  border: 0;
  box-shadow: none;
  outline: none;
  font-size: 14px;
  font-weight: 500;
  text-decoration: none;
  box-sizing: border-box;
}
.templately-display-condition-modal-footer .templately-button:hover {
  background: #4040e3;
  box-shadow: 0 15px 40px rgba(84, 83, 253, 0.15);
}

.templately-display-condition-loader {
  background: #ffffff;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
}
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/.pnpm/css-loader@6.11.0_webpack@5.98.0_webpack-cli@5.1.4_/node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[4].use[1]!./node_modules/.pnpm/postcss-loader@6.2.1_postcss@8.5.3_webpack@5.98.0_webpack-cli@5.1.4_/node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[4].use[2]!./node_modules/.pnpm/sass-loader@12.6.0_sass@1.86.0_webpack@5.98.0_webpack-cli@5.1.4_/node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[4].use[3]!./react-src/styles/settings.scss ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
.templately_page_templately_settings .templately-admin-preloader {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 100px;
  min-height: 100%;
  box-sizing: border-box;
  text-align: center;
}
.templately_page_templately_settings .templately-admin-preloader > svg {
  margin-left: 10px;
}
.templately_page_templately_settings .templately-builder-nav {
  margin-top: 20px;
  display: flex;
  border-bottom: 1px solid #c3c4c7;
}
.templately_page_templately_settings .templately-builder-nav .nav-tab-wrapper {
  border-bottom: none;
}
.templately_page_templately_settings .templately-builder-nav .nav-tab-wrapper .nav-tab {
  float: right;
  border: none;
  border-bottom: none;
  margin-right: 0.5em;
  margin-bottom: 0;
  padding: 0;
  background: transparent;
  color: #50575e;
  text-decoration: none;
  white-space: nowrap;
}
.templately_page_templately_settings .templately-builder-nav .nav-tab-wrapper .nav-tab.active a {
  background: #f0f0f1;
  color: #000;
  border-bottom: 1px solid #f0f0f1;
  margin-bottom: -1px;
}
.templately_page_templately_settings .templately-builder-nav .nav-tab-wrapper .nav-tab a {
  cursor: pointer;
  float: right;
  border: 1px solid #c3c4c7;
  border-bottom: none;
  margin: 0;
  padding: 5px 10px;
  font-size: 14px;
  line-height: 1.71428571;
  font-weight: 600;
  background: #dcdcde;
  color: #50575e;
  text-decoration: none;
}
.templately_page_templately_settings .templately-builder-nav .nav-tab-wrapper .nav-tab a:focus {
  box-shadow: none;
  outline: 0;
}
.templately_page_templately_settings .templately-builder-nav .nav-tab-wrapper .nav-tab a:hover {
  color: #3c434a;
  background: #fff;
}
.templately_page_templately_settings .templately-builder-nav .templately-switcher {
  width: 150px;
  display: inline-flex;
  margin-right: auto;
}
.templately_page_templately_settings .templately-builder-nav .templately-switcher__control {
  height: 38px;
  width: 150px;
}
.templately_page_templately_settings .templately-builder-nav .templately-switcher__menu {
  left: 20px;
  z-index: 10000;
}
.templately_page_templately_settings .templately-builder-nav .templately-switcher__control .templately-switcher__single-value,
.templately_page_templately_settings .templately-builder-nav .templately-switcher__control .templately-switcher__option,
.templately_page_templately_settings .templately-builder-nav .templately-switcher__menu .templately-switcher__single-value,
.templately_page_templately_settings .templately-builder-nav .templately-switcher__menu .templately-switcher__option {
  display: flex;
  align-items: center;
}
.templately_page_templately_settings .templately-builder-nav .templately-switcher__control .templately-switcher__single-value span.tswitcher-icon,
.templately_page_templately_settings .templately-builder-nav .templately-switcher__control .templately-switcher__option span.tswitcher-icon,
.templately_page_templately_settings .templately-builder-nav .templately-switcher__menu .templately-switcher__single-value span.tswitcher-icon,
.templately_page_templately_settings .templately-builder-nav .templately-switcher__menu .templately-switcher__option span.tswitcher-icon {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: inline-block;
  margin-left: 5px;
  background-position: center center;
  background-repeat: no-repeat;
  background-size: contain;
}
.templately_page_templately_settings .templately-builder-nav .templately-switcher__control .templately-switcher__single-value span.tswitcher-icon.tswitch-elementor,
.templately_page_templately_settings .templately-builder-nav .templately-switcher__control .templately-switcher__option span.tswitcher-icon.tswitch-elementor,
.templately_page_templately_settings .templately-builder-nav .templately-switcher__menu .templately-switcher__single-value span.tswitcher-icon.tswitch-elementor,
.templately_page_templately_settings .templately-builder-nav .templately-switcher__menu .templately-switcher__option span.tswitcher-icon.tswitch-elementor {
  background-image: url(../images/elementor.png);
}
.templately_page_templately_settings .templately-builder-nav .templately-switcher__control .templately-switcher__single-value span.tswitcher-icon.tswitch-gutenberg,
.templately_page_templately_settings .templately-builder-nav .templately-switcher__control .templately-switcher__option span.tswitcher-icon.tswitch-gutenberg,
.templately_page_templately_settings .templately-builder-nav .templately-switcher__menu .templately-switcher__single-value span.tswitcher-icon.tswitch-gutenberg,
.templately_page_templately_settings .templately-builder-nav .templately-switcher__menu .templately-switcher__option span.tswitcher-icon.tswitch-gutenberg {
  background-image: url(../images/gutenberg.png);
}
.templately_page_templately_settings .templately-builder-nav .templately-setting-save-button {
  background-color: #5453fd;
  color: #fff !important;
  outline: none;
  cursor: pointer;
  margin-right: 10px;
  height: 38px;
  border: 0px;
  padding: 4px 24px;
  text-transform: uppercase;
  border-radius: 3px;
  transition: background 200msease 0s;
  box-shadow: 0 0 25px -10px rgba(0, 0, 0, 0.3);
}
.templately_page_templately_settings .templately-setting-tab-panel-wrapper {
  margin-top: 30px;
  padding: 20px;
  background-color: #ffffff;
}
.templately_page_templately_settings .templately-setting-tab-panel-wrapper .templately-setting-tab-panel.templately-setting-tab-panel-general {
  max-width: 400px;
}
.templately_page_templately_settings .templately-setting-tab-panel-wrapper .templately-setting-tab-panel.templately-setting-tab-panel-colors {
  max-width: 500px;
}
.templately_page_templately_settings .templately-setting-tab-panel-wrapper .templately-setting-tab-panel.templately-setting-tab-panel-typography {
  max-width: 500px;
}
.templately_page_templately_settings .templately-setting-input {
  display: flex;
  flex-direction: column;
  margin-bottom: 16px;
}
.templately_page_templately_settings .templately-setting-input .templately-setting-input-label {
  color: #344054;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 6px;
}
.templately_page_templately_settings .templately-setting-input .has-error input:not([type=checkbox]):not([type=radio]) {
  border-color: #f04438;
}
.templately_page_templately_settings .templately-setting-input input:not([type=checkbox]):not([type=radio]) {
  width: 100%;
  padding: 9px 20px;
  background-color: #ffffff;
  color: #20172f;
  border: 1px solid rgba(208, 213, 221, 0.5);
  border-radius: 6px;
  box-shadow: 0px 1px 2px 0px rgba(16, 24, 40, 0.05);
  font-size: 16px;
  line-height: 1.5;
}
.templately_page_templately_settings .templately-setting-input input:not([type=checkbox]):not([type=radio])::-webkit-input-placeholder {
  color: #707c8e;
  font-size: 12px;
}
.templately_page_templately_settings .templately-setting-input input:not([type=checkbox]):not([type=radio])::-moz-placeholder {
  color: #707c8e;
  font-size: 12px;
}
.templately_page_templately_settings .templately-setting-input input:not([type=checkbox]):not([type=radio])::-ms-input-placeholder {
  color: #707c8e;
  font-size: 12px;
}
.templately_page_templately_settings .templately-setting-input input:not([type=checkbox]):not([type=radio])::-moz-placeholder {
  color: #707c8e;
  font-size: 12px;
}
.templately_page_templately_settings .templately-setting-input input:not([type=checkbox]):not([type=radio]):focus {
  box-shadow: none;
  outline: none;
  border-color: #bfc9d7;
}
.templately_page_templately_settings .templately-setting-file-uploader-wrapper {
  margin-bottom: 16px;
  display: block;
}
.templately_page_templately_settings .templately-setting-file-uploader-wrapper .templately-title {
  margin-top: 0;
  margin-bottom: 8px;
  color: #1d2939;
  font-size: 16px;
  font-weight: 500;
  line-height: 1.3;
}
.templately_page_templately_settings .templately-setting-file-uploader-wrapper .templately-info {
  margin-top: 0;
  margin-bottom: 16px;
  color: #667085;
  font-size: 14px;
  font-weight: 400;
  line-height: 1.3;
}
.templately_page_templately_settings .templately-setting-file-uploader-wrapper .templately-setting-file-uploader {
  border-radius: 8px;
  border: 1px solid #eaecf0;
  background: #fff;
  display: flex;
  padding: 16px 24px;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 4px;
  text-align: center;
  cursor: pointer;
}
.templately_page_templately_settings .templately-setting-file-uploader-wrapper .templately-setting-file-uploader:hover {
  border-color: #bfc9d7;
}
.templately_page_templately_settings .templately-setting-file-uploader-wrapper .templately-setting-file-uploader input {
  display: none;
}
.templately_page_templately_settings .templately-setting-file-uploader-wrapper .templately-setting-file-uploader .icon {
  margin-bottom: 8px;
  display: inline-flex;
  position: relative;
}
.templately_page_templately_settings .templately-setting-file-uploader-wrapper .templately-setting-file-uploader .icon .templately-setting-file-remove-button {
  position: absolute;
  left: 3px;
  top: 3px;
  cursor: pointer;
  width: 20px;
  height: 20px;
  border-radius: 3px;
  background: #475467;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}
.templately_page_templately_settings .templately-setting-file-uploader-wrapper .templately-setting-file-uploader .icon .templately-setting-file-remove-button:hover {
  background: #272e3a;
}
.templately_page_templately_settings .templately-setting-file-uploader-wrapper .templately-setting-file-uploader .icon img {
  max-width: 100%;
  max-height: 80px;
  padding: 10px;
  box-sizing: border-box;
  background-color: rgba(0, 0, 0, 0.1);
  box-shadow: 0 0 1px rgba(0, 0, 0, 0.25);
}
.templately_page_templately_settings .templately-setting-file-uploader-wrapper .templately-setting-file-uploader .title {
  display: block;
  color: #667085;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 1.43;
  margin: 0;
  text-transform: none;
}
.templately_page_templately_settings .templately-setting-file-uploader-wrapper .templately-setting-file-uploader .title .highlighted {
  color: #5453f4;
  font-weight: 500;
}
.templately_page_templately_settings .templately-setting-file-uploader-wrapper .templately-setting-file-uploader .info {
  color: #667085;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 1.5;
}
.templately_page_templately_settings .templately-setting-file-uploader-wrapper .components-range-control label {
  text-transform: unset;
  font-size: 14px;
}
.templately_page_templately_settings .templately-setting-file-uploader-wrapper .components-range-control .components-range-control__tooltip {
  min-width: 39px;
}
.templately_page_templately_settings .templately-setting-color-picker-section {
  display: flex;
  flex-direction: column;
  margin-bottom: 40px;
}
.templately_page_templately_settings .templately-setting-color-picker-section .templately-setting-color-picker-section-label-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
}
.templately_page_templately_settings .templately-setting-color-picker-section .templately-setting-color-picker-section-label {
  color: #344054;
  font-size: 18px;
  font-weight: 500;
}
.templately_page_templately_settings .templately-setting-color-picker-section .templately-setting-color-picker-reset-button {
  background-color: transparent !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-size: 10px !important;
  padding: 0 !important;
  border-radius: 0 !important;
  color: #b7b9bd !important;
  border: none;
  cursor: pointer;
}
.templately_page_templately_settings .templately-setting-color-picker-section .templately-setting-device-tab {
  display: flex;
  align-items: center;
}
.templately_page_templately_settings .templately-setting-color-picker-section .templately-setting-device-tab .templately-setting-device-tab-button {
  background: transparent !important;
  color: #d0d3da !important;
  height: 20px !important;
  width: 20px !important;
  margin: 0 !important;
  padding: 0 !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  border-radius: 0 !important;
}
.templately_page_templately_settings .templately-setting-color-picker-section .templately-setting-device-tab .templately-setting-device-tab-button .dashicons {
  height: 14px !important;
  width: 14px !important;
  margin: 0 !important;
  padding: 0 !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-size: 14px !important;
}
.templately_page_templately_settings .templately-setting-color-picker-section .templately-setting-device-tab .templately-setting-device-tab-button:not(:last-child) {
  border-left: 1px solid #d3d7de !important;
}
.templately_page_templately_settings .templately-setting-color-picker-section .templately-setting-device-tab .templately-setting-device-tab-button:first-child {
  border-top-right-radius: 4px !important;
  border-bottom-right-radius: 4px !important;
}
.templately_page_templately_settings .templately-setting-color-picker-section .templately-setting-device-tab .templately-setting-device-tab-button:last-child {
  border-top-left-radius: 4px !important;
  border-bottom-left-radius: 4px !important;
}
.templately_page_templately_settings .templately-setting-color-picker-section .templately-setting-device-tab .templately-setting-device-tab-button.active {
  color: #344054 !important;
}
.templately_page_templately_settings .templately-setting-color-picker-section .templately-setting-device-tab .templately-setting-device-tab-button:hover {
  background: #f0f0f0 !important;
}
.templately_page_templately_settings .templately-setting-color-picker-section .templately-setting-color-picker-panel {
  padding-top: 20px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}
.templately_page_templately_settings .templately-setting-color-picker-section .templately-setting-color-picker-panel .templately-setting-colorpicker-wrap {
  position: relative;
}
.templately_page_templately_settings .templately-setting-color-picker-section .templately-setting-color-picker-panel .templately-setting-colorpicker-wrap .templately-colorpicker-screen {
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 10px;
}
.templately_page_templately_settings .templately-setting-color-picker-section .templately-setting-color-picker-panel .templately-setting-colorpicker-wrap .templately-colorpicker-screen .templately-picker-title {
  font-size: 16px;
  flex-grow: 1;
}
.templately_page_templately_settings .templately-setting-color-picker-section .templately-setting-color-picker-panel .templately-setting-colorpicker-wrap .templately-colorpicker-screen .templately-picker-sub-title {
  font-size: 16px;
}
.templately_page_templately_settings .templately-setting-color-picker-section .templately-setting-color-picker-panel .templately-setting-colorpicker-wrap .templately-colorpicker-screen .templately-picker-display-wrapper {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  height: 40px;
  width: 40px;
  border: 1px solid #ccc;
  border-radius: 100%;
}
.templately_page_templately_settings .templately-setting-color-picker-section .templately-setting-color-picker-panel .templately-setting-colorpicker-wrap .templately-colorpicker-screen .templately-picker-display-wrapper .templately-picker-display {
  width: 100%;
  height: 100%;
  display: block;
  border-radius: 100%;
}
.templately_page_templately_settings .templately-setting-color-picker-section .templately-setting-color-picker-panel .templately-setting-colorpicker-wrap .templately-colorpicker {
  padding: 12px 16px 16px;
  border-radius: 4px;
  position: absolute;
  top: -24px;
  right: calc(100% + 24px);
  z-index: 2;
  background-color: #fff;
  box-shadow: 15px 15px 50px 4px rgba(129, 129, 129, 0.2);
  width: 320px;
}
.templately_page_templately_settings .templately-setting-color-picker-section .templately-setting-color-picker-panel .templately-setting-colorpicker-wrap .templately-colorpicker .templately-title {
  margin-bottom: 8px;
  color: #1d2939;
  font-size: 16px;
  font-weight: 500;
  display: block;
}
.templately_page_templately_settings .templately-setting-color-picker-section .templately-setting-color-picker-panel .templately-setting-colorpicker-wrap .templately-colorpicker .components-color-picker {
  width: 100%;
}
.templately_page_templately_settings .templately-setting-color-picker-section .templately-setting-color-picker-panel .templately-setting-colorpicker-wrap .templately-colorpicker .components-color-picker .react-colorful {
  width: 100%;
}
.templately_page_templately_settings .templately-setting-color-picker-section .templately-setting-color-picker-panel .templately-setting-colorpicker-wrap .templately-colorpicker .components-color-picker .react-colorful .react-colorful__saturation {
  height: 290px;
  margin-bottom: 12px;
}
.templately_page_templately_settings .templately-setting-color-picker-section .templately-setting-color-picker-panel .templately-setting-colorpicker-wrap .templately-colorpicker .components-color-picker .react-colorful .react-colorful__hue,
.templately_page_templately_settings .templately-setting-color-picker-section .templately-setting-color-picker-panel .templately-setting-colorpicker-wrap .templately-colorpicker .components-color-picker .react-colorful .react-colorful__alpha {
  width: calc(100% - 12px);
  margin-bottom: 12px;
}
.templately_page_templately_settings .templately-setting-color-picker-section .templately-setting-color-picker-panel .templately-setting-colorpicker-wrap .templately-colorpicker .components-color-picker > div:not(.react-colorful) {
  display: flex;
  position: relative;
  padding: 0;
}
.templately_page_templately_settings .templately-setting-color-picker-section .templately-setting-color-picker-panel .templately-setting-colorpicker-wrap .templately-colorpicker .components-color-picker > div:not(.react-colorful) > .components-h-stack {
  width: 100%;
  position: relative;
}
.templately_page_templately_settings .templately-setting-color-picker-section .templately-setting-color-picker-panel .templately-setting-colorpicker-wrap .templately-colorpicker .components-color-picker > div:not(.react-colorful) > .components-h-stack .components-input-control__container {
  background: transparent;
}
.templately_page_templately_settings .templately-setting-color-picker-section .templately-setting-color-picker-panel .templately-setting-colorpicker-wrap .templately-colorpicker .components-color-picker > div:not(.react-colorful) > .components-h-stack .components-select-control__input {
  height: 44px;
  padding: 2px 14px;
  border-radius: 8px;
  border: 1px solid #d0d5dd;
  background: #ffffff;
  color: #101828;
  box-shadow: 0px 1px 2px 0px rgba(16, 24, 40, 0.05);
}
.templately_page_templately_settings .templately-setting-color-picker-section .templately-setting-color-picker-panel .templately-setting-colorpicker-wrap .templately-colorpicker .components-color-picker > div:not(.react-colorful) > .components-h-stack .components-input-control__suffix svg {
  fill: #101828;
}
.templately_page_templately_settings .templately-setting-color-picker-section .templately-setting-color-picker-panel .templately-setting-colorpicker-wrap .templately-colorpicker .components-color-picker > div:not(.react-colorful) > .components-h-stack .components-input-control__backdrop {
  display: none;
}
.templately_page_templately_settings .templately-setting-color-picker-section .templately-setting-color-picker-panel .templately-setting-colorpicker-wrap .templately-colorpicker .components-color-picker > div:not(.react-colorful) > .components-h-stack button.components-button {
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  background: transparent;
  border: none;
  padding: 0;
  z-index: 2;
  display: inline-flex;
}
.templately_page_templately_settings .templately-setting-color-picker-section .templately-setting-color-picker-panel .templately-setting-colorpicker-wrap .templately-colorpicker .components-color-picker > div:not(.react-colorful) > .components-h-stack button.components-button i,
.templately_page_templately_settings .templately-setting-color-picker-section .templately-setting-color-picker-panel .templately-setting-colorpicker-wrap .templately-colorpicker .components-color-picker > div:not(.react-colorful) > .components-h-stack button.components-button svg,
.templately_page_templately_settings .templately-setting-color-picker-section .templately-setting-color-picker-panel .templately-setting-colorpicker-wrap .templately-colorpicker .components-color-picker > div:not(.react-colorful) > .components-h-stack button.components-button img {
  width: 16px;
  font-size: 16px;
  fill: #101828;
}
.templately_page_templately_settings .templately-setting-color-picker-section .templately-setting-color-picker-panel .templately-setting-colorpicker-wrap .templately-colorpicker .components-color-picker > div:not(.react-colorful) > .components-flex:last-child {
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  z-index: 1;
  padding: 0;
  display: inline-flex;
  flex-direction: row;
  gap: 4px;
}
.templately_page_templately_settings .templately-setting-color-picker-section .templately-setting-color-picker-panel .templately-setting-colorpicker-wrap .templately-colorpicker .components-color-picker > div:not(.react-colorful) > .components-flex:last-child .components-input-control {
  height: 44px;
  display: flex;
  align-items: center;
  padding: 2px 14px;
  border-radius: 8px;
  border: 1px solid #d0d5dd;
  background: #ffffff;
  box-shadow: 0px 1px 2px 0px rgba(16, 24, 40, 0.05);
}
.templately_page_templately_settings .templately-setting-color-picker-section .templately-setting-color-picker-panel .templately-setting-colorpicker-wrap .templately-colorpicker .components-color-picker > div:not(.react-colorful) > .components-flex:last-child .components-input-control .components-input-control__container {
  background: transparent;
}
.templately_page_templately_settings .templately-setting-color-picker-section .templately-setting-color-picker-panel .templately-setting-colorpicker-wrap .templately-colorpicker .components-color-picker > div:not(.react-colorful) > .components-flex:last-child .components-input-control .components-input-control__container .components-input-control__prefix {
  margin: 0;
}
.templately_page_templately_settings .templately-setting-color-picker-section .templately-setting-color-picker-panel .templately-setting-colorpicker-wrap .templately-colorpicker .components-color-picker > div:not(.react-colorful) > .components-flex:last-child .components-input-control .components-input-control__container .components-input-control__prefix, .templately_page_templately_settings .templately-setting-color-picker-section .templately-setting-color-picker-panel .templately-setting-colorpicker-wrap .templately-colorpicker .components-color-picker > div:not(.react-colorful) > .components-flex:last-child .components-input-control .components-input-control__container .components-input-control__prefix span {
  color: #101828;
  margin: 0;
}
.templately_page_templately_settings .templately-setting-color-picker-section .templately-setting-color-picker-panel .templately-setting-colorpicker-wrap .templately-colorpicker .components-color-picker > div:not(.react-colorful) > .components-flex:last-child .components-input-control .components-input-control__container .components-input-control__input {
  padding: 0;
  min-width: auto;
  height: auto;
  min-height: auto;
  color: #101828;
}
.templately_page_templately_settings .templately-setting-color-picker-section .templately-setting-color-picker-panel .templately-setting-colorpicker-wrap .templately-colorpicker .components-color-picker > div:not(.react-colorful) > .components-flex:last-child .components-input-control .components-input-control__container .components-input-control__backdrop {
  display: none;
}
.templately_page_templately_settings .templately-setting-color-picker-section .templately-setting-color-picker-panel .templately-setting-colorpicker-wrap .templately-colorpicker .components-color-picker > div:not(.react-colorful) > .components-flex:last-child .components-h-stack .components-input-control {
  padding-right: 8px;
  padding-left: 8px;
  width: 57px;
}
.templately_page_templately_settings .templately-setting-color-picker-section .templately-setting-color-picker-panel .templately-setting-colorpicker-wrap .templately-colorpicker .components-color-picker > div:not(.react-colorful) > .components-flex:last-child .components-h-stack .components-input-control .components-input-control__prefix {
  display: none;
}
.templately_page_templately_settings .templately-setting-color-picker-section .templately-setting-color-picker-panel .templately-setting-colorpicker-wrap .templately-colorpicker .components-color-picker > div:not(.react-colorful) > .components-flex:last-child .components-h-stack:last-child .components-input-control {
  padding-left: 22px;
}
.templately_page_templately_settings .templately-setting-color-picker-section .templately-setting-color-picker-panel .templately-setting-colorpicker-wrap .templately-colorpicker .components-color-picker > div:not(.react-colorful) > .components-flex:last-child .components-range-control {
  display: none;
}
.templately_page_templately_settings .templately-setting-color-picker-section .templately-setting-color-picker-panel-2 {
  padding-top: 20px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}
.templately_page_templately_settings .templately-setting-color-picker-section .templately-setting-color-picker-panel-2 .templately-setting-colorpicker-wrap {
  position: relative;
}
.templately_page_templately_settings .templately-setting-color-picker-section .templately-setting-color-picker-panel-2 .templately-setting-colorpicker-wrap .templately-colorpicker-screen {
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 10px;
}
.templately_page_templately_settings .templately-setting-color-picker-section .templately-setting-color-picker-panel-2 .templately-setting-colorpicker-wrap .templately-colorpicker-screen .templately-picker-title {
  font-size: 16px;
  flex-grow: 1;
}
.templately_page_templately_settings .templately-setting-color-picker-section .templately-setting-color-picker-panel-2 .templately-setting-colorpicker-wrap .templately-colorpicker-screen .templately-picker-sub-title {
  font-size: 16px;
}
.templately_page_templately_settings .templately-setting-color-picker-section .templately-setting-color-picker-panel-2 .templately-setting-colorpicker-wrap .templately-colorpicker-screen .templately-picker-display-wrapper {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  height: 40px;
  width: 40px;
  border: 1px solid #ccc;
  border-radius: 100%;
}
.templately_page_templately_settings .templately-setting-color-picker-section .templately-setting-color-picker-panel-2 .templately-setting-colorpicker-wrap .templately-colorpicker-screen .templately-picker-display-wrapper .templately-picker-display {
  width: 100%;
  height: 100%;
  display: block;
  border-radius: 100%;
}
.templately_page_templately_settings .templately-setting-color-picker-section .templately-setting-color-picker-panel-2 .templately-setting-colorpicker-wrap .templately-colorpicker {
  padding: 12px 16px 16px;
  border-radius: 4px;
  position: absolute;
  top: -24px;
  right: calc(100% + 24px);
  z-index: 2;
  background-color: #fff;
  box-shadow: 15px 15px 50px 4px rgba(129, 129, 129, 0.2);
  width: 320px;
}
.templately_page_templately_settings .templately-setting-color-picker-section .templately-setting-color-picker-panel-2 .templately-setting-colorpicker-wrap .templately-colorpicker .templately-title {
  margin-bottom: 8px;
  color: #1d2939;
  font-size: 16px;
  font-weight: 500;
  display: block;
}
.templately_page_templately_settings .templately-setting-color-picker-section .templately-setting-color-picker-panel-2 .templately-setting-colorpicker-wrap .templately-colorpicker .components-color-picker {
  width: 100%;
}
.templately_page_templately_settings .templately-setting-color-picker-section .templately-setting-color-picker-panel-2 .templately-setting-colorpicker-wrap .templately-colorpicker .components-color-picker .react-colorful {
  width: 100%;
}
.templately_page_templately_settings .templately-setting-color-picker-section .templately-setting-color-picker-panel-2 .templately-setting-colorpicker-wrap .templately-colorpicker .components-color-picker .react-colorful .react-colorful__saturation {
  height: 290px;
  margin-bottom: 12px;
}
.templately_page_templately_settings .templately-setting-color-picker-section .templately-setting-color-picker-panel-2 .templately-setting-colorpicker-wrap .templately-colorpicker .components-color-picker .react-colorful .react-colorful__hue,
.templately_page_templately_settings .templately-setting-color-picker-section .templately-setting-color-picker-panel-2 .templately-setting-colorpicker-wrap .templately-colorpicker .components-color-picker .react-colorful .react-colorful__alpha {
  width: calc(100% - 12px);
  margin-bottom: 12px;
}
.templately_page_templately_settings .templately-setting-color-picker-section .templately-setting-color-picker-panel-2 .templately-setting-colorpicker-wrap .templately-colorpicker .components-color-picker > div:not(.react-colorful) {
  display: flex;
  position: relative;
  padding: 0;
}
.templately_page_templately_settings .templately-setting-color-picker-section .templately-setting-color-picker-panel-2 .templately-setting-colorpicker-wrap .templately-colorpicker .components-color-picker > div:not(.react-colorful) > .components-h-stack {
  width: 100%;
  position: relative;
}
.templately_page_templately_settings .templately-setting-color-picker-section .templately-setting-color-picker-panel-2 .templately-setting-colorpicker-wrap .templately-colorpicker .components-color-picker > div:not(.react-colorful) > .components-h-stack .components-input-control__container {
  background: transparent;
}
.templately_page_templately_settings .templately-setting-color-picker-section .templately-setting-color-picker-panel-2 .templately-setting-colorpicker-wrap .templately-colorpicker .components-color-picker > div:not(.react-colorful) > .components-h-stack .components-select-control__input {
  height: 44px;
  padding: 2px 14px;
  border-radius: 8px;
  border: 1px solid #d0d5dd;
  background: #ffffff;
  color: #101828;
  box-shadow: 0px 1px 2px 0px rgba(16, 24, 40, 0.05);
}
.templately_page_templately_settings .templately-setting-color-picker-section .templately-setting-color-picker-panel-2 .templately-setting-colorpicker-wrap .templately-colorpicker .components-color-picker > div:not(.react-colorful) > .components-h-stack .components-input-control__suffix svg {
  fill: #101828;
}
.templately_page_templately_settings .templately-setting-color-picker-section .templately-setting-color-picker-panel-2 .templately-setting-colorpicker-wrap .templately-colorpicker .components-color-picker > div:not(.react-colorful) > .components-h-stack .components-input-control__backdrop {
  display: none;
}
.templately_page_templately_settings .templately-setting-color-picker-section .templately-setting-color-picker-panel-2 .templately-setting-colorpicker-wrap .templately-colorpicker .components-color-picker > div:not(.react-colorful) > .components-h-stack button.components-button {
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  background: transparent;
  border: none;
  padding: 0;
  z-index: 2;
  display: inline-flex;
}
.templately_page_templately_settings .templately-setting-color-picker-section .templately-setting-color-picker-panel-2 .templately-setting-colorpicker-wrap .templately-colorpicker .components-color-picker > div:not(.react-colorful) > .components-h-stack button.components-button i,
.templately_page_templately_settings .templately-setting-color-picker-section .templately-setting-color-picker-panel-2 .templately-setting-colorpicker-wrap .templately-colorpicker .components-color-picker > div:not(.react-colorful) > .components-h-stack button.components-button svg,
.templately_page_templately_settings .templately-setting-color-picker-section .templately-setting-color-picker-panel-2 .templately-setting-colorpicker-wrap .templately-colorpicker .components-color-picker > div:not(.react-colorful) > .components-h-stack button.components-button img {
  width: 16px;
  font-size: 16px;
  fill: #101828;
}
.templately_page_templately_settings .templately-setting-color-picker-section .templately-setting-color-picker-panel-2 .templately-setting-colorpicker-wrap .templately-colorpicker .components-color-picker > div:not(.react-colorful) > .components-flex:last-child {
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  z-index: 1;
  padding: 0;
  display: inline-flex;
  flex-direction: row;
  gap: 4px;
}
.templately_page_templately_settings .templately-setting-color-picker-section .templately-setting-color-picker-panel-2 .templately-setting-colorpicker-wrap .templately-colorpicker .components-color-picker > div:not(.react-colorful) > .components-flex:last-child .components-input-control {
  height: 44px;
  display: flex;
  align-items: center;
  padding: 2px 14px;
  border-radius: 8px;
  border: 1px solid #d0d5dd;
  background: #ffffff;
  box-shadow: 0px 1px 2px 0px rgba(16, 24, 40, 0.05);
}
.templately_page_templately_settings .templately-setting-color-picker-section .templately-setting-color-picker-panel-2 .templately-setting-colorpicker-wrap .templately-colorpicker .components-color-picker > div:not(.react-colorful) > .components-flex:last-child .components-input-control .components-input-control__container {
  background: transparent;
}
.templately_page_templately_settings .templately-setting-color-picker-section .templately-setting-color-picker-panel-2 .templately-setting-colorpicker-wrap .templately-colorpicker .components-color-picker > div:not(.react-colorful) > .components-flex:last-child .components-input-control .components-input-control__container .components-input-control__prefix {
  margin: 0;
}
.templately_page_templately_settings .templately-setting-color-picker-section .templately-setting-color-picker-panel-2 .templately-setting-colorpicker-wrap .templately-colorpicker .components-color-picker > div:not(.react-colorful) > .components-flex:last-child .components-input-control .components-input-control__container .components-input-control__prefix, .templately_page_templately_settings .templately-setting-color-picker-section .templately-setting-color-picker-panel-2 .templately-setting-colorpicker-wrap .templately-colorpicker .components-color-picker > div:not(.react-colorful) > .components-flex:last-child .components-input-control .components-input-control__container .components-input-control__prefix span {
  color: #101828;
  margin: 0;
}
.templately_page_templately_settings .templately-setting-color-picker-section .templately-setting-color-picker-panel-2 .templately-setting-colorpicker-wrap .templately-colorpicker .components-color-picker > div:not(.react-colorful) > .components-flex:last-child .components-input-control .components-input-control__container .components-input-control__input {
  padding: 0;
  min-width: auto;
  height: auto;
  min-height: auto;
  color: #101828;
}
.templately_page_templately_settings .templately-setting-color-picker-section .templately-setting-color-picker-panel-2 .templately-setting-colorpicker-wrap .templately-colorpicker .components-color-picker > div:not(.react-colorful) > .components-flex:last-child .components-input-control .components-input-control__container .components-input-control__backdrop {
  display: none;
}
.templately_page_templately_settings .templately-setting-color-picker-section .templately-setting-color-picker-panel-2 .templately-setting-colorpicker-wrap .templately-colorpicker .components-color-picker > div:not(.react-colorful) > .components-flex:last-child .components-h-stack .components-input-control {
  padding-right: 8px;
  padding-left: 8px;
  width: 57px;
}
.templately_page_templately_settings .templately-setting-color-picker-section .templately-setting-color-picker-panel-2 .templately-setting-colorpicker-wrap .templately-colorpicker .components-color-picker > div:not(.react-colorful) > .components-flex:last-child .components-h-stack .components-input-control .components-input-control__prefix {
  display: none;
}
.templately_page_templately_settings .templately-setting-color-picker-section .templately-setting-color-picker-panel-2 .templately-setting-colorpicker-wrap .templately-colorpicker .components-color-picker > div:not(.react-colorful) > .components-flex:last-child .components-h-stack:last-child .components-input-control {
  padding-left: 22px;
}
.templately_page_templately_settings .templately-setting-color-picker-section .templately-setting-color-picker-panel-2 .templately-setting-colorpicker-wrap .templately-colorpicker .components-color-picker > div:not(.react-colorful) > .components-flex:last-child .components-range-control {
  display: none;
}
.templately_page_templately_settings .templately-setting-typography-picker-section {
  display: flex;
  flex-direction: column;
  margin-bottom: 40px;
}
.templately_page_templately_settings .templately-setting-typography-picker-section .templately-setting-typography-picker-section-label-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
}
.templately_page_templately_settings .templately-setting-typography-picker-section .templately-setting-typography-picker-section-label {
  color: #344054;
  font-size: 18px;
  font-weight: 500;
}
.templately_page_templately_settings .templately-setting-typography-picker-section .templately-setting-typography-picker-reset-button {
  background-color: transparent !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-size: 10px !important;
  padding: 0 !important;
  border-radius: 0 !important;
  color: #b7b9bd !important;
  border: none;
  cursor: pointer;
}
.templately_page_templately_settings .templately-setting-typography-picker-section .templately-setting-device-tab {
  display: flex;
  align-items: center;
}
.templately_page_templately_settings .templately-setting-typography-picker-section .templately-setting-device-tab .templately-setting-device-tab-button {
  background: transparent !important;
  color: #d0d3da !important;
  height: 20px !important;
  width: 20px !important;
  margin: 0 !important;
  padding: 0 !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  border-radius: 0 !important;
  border: none;
  cursor: pointer;
}
.templately_page_templately_settings .templately-setting-typography-picker-section .templately-setting-device-tab .templately-setting-device-tab-button .dashicons {
  height: 14px !important;
  width: 14px !important;
  margin: 0 !important;
  padding: 0 !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-size: 14px !important;
}
.templately_page_templately_settings .templately-setting-typography-picker-section .templately-setting-device-tab .templately-setting-device-tab-button:not(:last-child) {
  border-left: 1px solid #d3d7de !important;
}
.templately_page_templately_settings .templately-setting-typography-picker-section .templately-setting-device-tab .templately-setting-device-tab-button:first-child {
  border-top-right-radius: 4px !important;
  border-bottom-right-radius: 4px !important;
}
.templately_page_templately_settings .templately-setting-typography-picker-section .templately-setting-device-tab .templately-setting-device-tab-button:last-child {
  border-top-left-radius: 4px !important;
  border-bottom-left-radius: 4px !important;
}
.templately_page_templately_settings .templately-setting-typography-picker-section .templately-setting-device-tab .templately-setting-device-tab-button.active {
  color: #344054 !important;
}
.templately_page_templately_settings .templately-setting-typography-picker-section .templately-setting-device-tab .templately-setting-device-tab-button:hover {
  background: #f0f0f0 !important;
}
.templately_page_templately_settings .templately-setting-miscellaneous-panel .templately-title {
  font-size: 16px;
  font-weight: 600;
  line-height: 20px;
  margin-bottom: 14px;
  margin-top: 0;
}
.templately_page_templately_settings .templately-setting-miscellaneous-panel .templately-sub-title {
  font-size: 16px;
  line-height: 20px;
  color: #475467;
  margin: 0;
  margin-bottom: 30px;
  max-width: 490px;
}
.templately_page_templately_settings .templately-setting-miscellaneous-panel .templately-button {
  display: inline-block;
  text-decoration: none;
  font-size: 16px;
  line-height: 20px;
  font-weight: 500;
  color: #5453fd;
  min-height: 30px;
  margin: 0;
  padding: 12px 36px;
  border-width: 1px;
  border-style: solid;
  border-color: #5453fd;
  border-radius: 60px;
  background: transparent;
  cursor: pointer;
  white-space: nowrap;
  box-sizing: border-box;
}
.templately_page_templately_settings .templately-setting-miscellaneous-panel .templately-button:disabled {
  color: #667085 !important;
  border-color: #d0d5dd !important;
  background: transparent !important;
  box-shadow: none !important;
  cursor: default !important;
  transform: none !important;
}
.templately_page_templately_settings .templately-setting-overlay {
  position: fixed;
  inset: 0;
  z-index: 1;
  background-color: rgba(0, 0, 0, 0.5);
  pointer-events: none;
}
