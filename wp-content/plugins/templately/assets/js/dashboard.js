(()=>{"use strict";var e={2799:(e,t)=>{var r="function"==typeof Symbol&&Symbol.for,a=r?Symbol.for("react.element"):60103,n=r?Symbol.for("react.portal"):60106,l=r?Symbol.for("react.fragment"):60107,c=r?Symbol.for("react.strict_mode"):60108,o=r?Symbol.for("react.profiler"):60114,i=r?Symbol.for("react.provider"):60109,s=r?Symbol.for("react.context"):60110,m=r?Symbol.for("react.async_mode"):60111,u=r?Symbol.for("react.concurrent_mode"):60111,f=r?Symbol.for("react.forward_ref"):60112,p=r?Symbol.for("react.suspense"):60113,d=r?Symbol.for("react.suspense_list"):60120,y=r?Symbol.for("react.memo"):60115,v=r?Symbol.for("react.lazy"):60116,w=r?Symbol.for("react.block"):60121,h=r?Symbol.for("react.fundamental"):60117,E=r?Symbol.for("react.responder"):60118,b=r?Symbol.for("react.scope"):60119;function g(e){if("object"==typeof e&&null!==e){var t=e.$$typeof;switch(t){case a:switch(e=e.type){case m:case u:case l:case o:case c:case p:return e;default:switch(e=e&&e.$$typeof){case s:case f:case v:case y:case i:return e;default:return t}}case n:return t}}}function _(e){return g(e)===u}t.AsyncMode=m,t.ConcurrentMode=u,t.ContextConsumer=s,t.ContextProvider=i,t.Element=a,t.ForwardRef=f,t.Fragment=l,t.Lazy=v,t.Memo=y,t.Portal=n,t.Profiler=o,t.StrictMode=c,t.Suspense=p,t.isAsyncMode=function(e){return _(e)||g(e)===m},t.isConcurrentMode=_,t.isContextConsumer=function(e){return g(e)===s},t.isContextProvider=function(e){return g(e)===i},t.isElement=function(e){return"object"==typeof e&&null!==e&&e.$$typeof===a},t.isForwardRef=function(e){return g(e)===f},t.isFragment=function(e){return g(e)===l},t.isLazy=function(e){return g(e)===v},t.isMemo=function(e){return g(e)===y},t.isPortal=function(e){return g(e)===n},t.isProfiler=function(e){return g(e)===o},t.isStrictMode=function(e){return g(e)===c},t.isSuspense=function(e){return g(e)===p},t.isValidElementType=function(e){return"string"==typeof e||"function"==typeof e||e===l||e===u||e===o||e===c||e===p||e===d||"object"==typeof e&&null!==e&&(e.$$typeof===v||e.$$typeof===y||e.$$typeof===i||e.$$typeof===s||e.$$typeof===f||e.$$typeof===h||e.$$typeof===E||e.$$typeof===b||e.$$typeof===w)},t.typeOf=g},4146:(e,t,r)=>{var a=r(4363),n={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},l={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},c={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},o={};function i(e){return a.isMemo(e)?c:o[e.$$typeof]||n}o[a.ForwardRef]={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},o[a.Memo]=c;var s=Object.defineProperty,m=Object.getOwnPropertyNames,u=Object.getOwnPropertySymbols,f=Object.getOwnPropertyDescriptor,p=Object.getPrototypeOf,d=Object.prototype;e.exports=function e(t,r,a){if("string"!=typeof r){if(d){var n=p(r);n&&n!==d&&e(t,n,a)}var c=m(r);u&&(c=c.concat(u(r)));for(var o=i(t),y=i(r),v=0;v<c.length;++v){var w=c[v];if(!(l[w]||a&&a[w]||y&&y[w]||o&&o[w])){var h=f(r,w);try{s(t,w,h)}catch(e){}}}}return t}},4363:(e,t,r)=>{e.exports=r(2799)},4737:(e,t,r)=>{r(8989)},8989:(e,t)=>{if("function"==typeof Symbol&&Symbol.for){var r=Symbol.for;r("react.element"),r("react.portal"),r("react.fragment"),r("react.strict_mode"),r("react.profiler"),r("react.provider"),r("react.context"),r("react.forward_ref"),r("react.suspense"),r("react.suspense_list"),r("react.memo"),r("react.lazy"),r("react.block"),r("react.server.block"),r("react.fundamental"),r("react.debug_trace_mode"),r("react.legacy_hidden")}}},t={};function r(a){var n=t[a];if(void 0!==n)return n.exports;var l=t[a]={exports:{}};return e[a](l,l.exports,r),l.exports}r.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return r.d(t,{a:t}),t},r.d=(e,t)=>{for(var a in t)r.o(t,a)&&!r.o(e,a)&&Object.defineProperty(e,a,{enumerable:!0,get:t[a]})},r.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t);const a=window.React;var n=r.n(a);const l=window.ReactDOM;var c=r.n(l);function o(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,a=Array(t);r<t;r++)a[r]=e[r];return a}function i(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var a,n,l,c,o=[],i=!0,s=!1;try{if(l=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;i=!1}else for(;!(i=(a=l.call(r)).done)&&(o.push(a.value),o.length!==t);i=!0);}catch(e){s=!0,n=e}finally{try{if(!i&&null!=r.return&&(c=r.return(),Object(c)!==c))return}finally{if(s)throw n}}return o}}(e,t)||function(e,t){if(e){if("string"==typeof e)return o(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?o(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function s(){return s=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var a in r)({}).hasOwnProperty.call(r,a)&&(e[a]=r[a])}return e},s.apply(null,arguments)}"undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement?a.useLayoutEffect:a.useEffect,r(4146),r(4737),l.unstable_batchedUpdates;const m=window.wp.i18n,u=function(){return n().createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"11",height:"11",fill:"none",viewBox:"0 0 11 11"},n().createElement("path",{fill:"#475467",d:"M1.658 10.465l-1.4-1.4 3.6-3.6-3.6-3.575 1.4-1.4 3.6 3.6L8.833.49l1.4 1.4-3.6 3.575 3.6 3.6-1.4 1.4-3.575-3.6-3.6 3.6z"}))};var f=function(e){return n().createElement("svg",s({viewBox:"0 0 512 512",xmlns:"http://www.w3.org/2000/svg"},e),n().createElement("circle",{cx:90.5,cy:454.8,fill:"#ffb45a",r:57.2}),n().createElement("circle",{cx:256,cy:454.8,fill:"#ff7b8e",r:57.2}),n().createElement("circle",{cx:421.5,cy:454.8,fill:"#5ac0ff",r:57.2}),n().createElement("path",{d:"M356.5 94.5c-3.1 0-6.1.2-9.1.4C335 40.6 286.4 0 228.3 0c-15.3 0-29.9 2.8-43.4 8 0 15.3.1 32.9.2 40.4 15.6.1 45.8.2 59.1.2v57.8h-58.8v97.7h60.5c-1.7 4.3-4.3 10.7-5.4 13.2-11 26.5-38.3 41.3-66.6 36.2-26.9-4.9-46.9-27.8-48.4-55.9-.4-8.2-.2-16.5-.2-24.7V106h-20.7c-41.8 19.4-70.9 61.7-70.9 110.8 0 67.5 54.7 122.2 122.2 122.2h202c66.9-.7 120.8-55.1 120.8-122.2 0-67.5-54.7-122.3-122.2-122.3z",fill:"#5633d1"}))},p=function(e){return n().createElement("svg",s({viewBox:"0 0 143 28",xmlns:"http://www.w3.org/2000/svg"},e),n().createElement("g",{fill:"#424c5e"},n().createElement("path",{d:"M6.6 18.5c-1.2 0-1.9-.7-1.9-2.1v-7h3.4V6.3H4.3l-.6-3H.8v14.2c0 2.8 1.3 4.1 3.8 4.1H8v-3.1zM17.3 6.3c-4.8 0-7.2 2.5-7.2 7.5 0 5.2 2.8 7.8 8.3 7.8 1.9 0 3.6-.1 4.9-.4v-3.1c-1.5.3-3.1.4-4.6.4-3.2 0-4.8-1.1-4.8-3.2h10.2c.1-.6.1-1.3.1-1.9.1-4.7-2.3-7.1-6.9-7.1zM14 12.6c.2-2.2 1.3-3.3 3.3-3.3 2.1 0 3.2 1.1 3.2 3.2v.1zM44.4 6.3c-1.7 0-3.3.7-4.9 2.1-.7-1.4-2-2.1-4-2.1-1.9 0-3.6.7-4.9 2.2l-.5-2.2h-3v15.3H31V11c1-1 2.1-1.5 3.2-1.5 1.4 0 2.1.9 2.1 2.6v9.6h3.9V11.1c1.1-1 2.2-1.5 3.3-1.5 1.5 0 2.3.8 2.3 2.6v9.6h3.9v-9.5c0-4.1-1.8-6-5.3-6zM59.8 6.3c-2.3 0-4.4.2-6.4.6V28h3.9v-7.1c1 .5 2.1.7 3.1.7 4.8 0 7.3-2.7 7.3-8-.1-4.9-2.7-7.3-7.9-7.3zm.5 12.1c-1.1 0-2.1-.3-3.1-.8v-8c.7-.1 1.6-.2 2.7-.2 2.5 0 3.8 1.4 3.8 4.1.1 3.3-1.1 4.9-3.4 4.9zM70.5.7h3.9v20.8h-3.9zM84.1 6.3c-1.7 0-3.5.2-5.6.7v3.1c2-.5 3.9-.7 5.6-.7 2 0 3 .7 3 2.1v1.2c-1-.2-2.1-.3-3.1-.3-4.4 0-6.6 1.5-6.6 4.6 0 3.2 1.9 4.8 5.6 4.8 1.6 0 3.1-.5 4.4-1.4l1.4 1.4H91V11.5c-.1-3.6-2.4-5.2-6.9-5.2zm-.6 12.5c-1.6 0-2.3-.7-2.3-2s.9-1.9 2.8-1.9c1.1 0 2.1.1 3.1.3v2.4c-1.2.8-2.4 1.2-3.6 1.2zM100 18.5c-1.2 0-1.9-.7-1.9-2.1v-7h3.4V6.3h-3.8l-.6-3h-2.9v14.2c0 2.8 1.3 4.1 3.8 4.1h3.4v-3.1zM110.7 6.3c-4.8 0-7.2 2.5-7.2 7.5 0 5.2 2.8 7.8 8.3 7.8 1.9 0 3.6-.1 4.9-.4v-3.1c-1.5.3-3.1.4-4.6.4-3.2 0-4.8-1.1-4.8-3.2h10.2c.1-.6.1-1.3.1-1.9.1-4.8-2.3-7.1-6.9-7.1zm-3.3 6.3c.2-2.2 1.3-3.3 3.3-3.3 2.1 0 3.2 1.1 3.2 3.2v.1zM120.6.7h3.9v20.8h-3.9zM138 6.2l-3.8 11-3.9-11h-4.1l5.9 15.4c-.8 1.7-2 2.9-3.7 3.7l1.9 2.6c2.5-1.2 4.3-3.2 5.4-5.9L142 6.2z"})))};const d=function(e){var t=e.title,r=e.subTitle,a=e.setIsClosed;return n().createElement("div",{className:"review-header"},!t&&n().createElement("div",{className:"templately-logo"},n().createElement(f,{width:"37.66px",className:"vector-logo"}),n().createElement(p,{width:"143px",className:"text-logo"})),t&&n().createElement("h1",{className:"templately-review-header-title"},t),r&&n().createElement("h3",{className:"templately-review-header-sub-title"},r),n().createElement("button",{className:"templately-review-header-close-button",onClick:function(e){a("close_button")}},n().createElement(u,null)))},y=window.wp.apiFetch;var v=r.n(y);const w=function(e){e.setCurrentScreen;var t=e.setIsClosed,r=e.rating,l=e.email,c=i((0,a.useState)(null!=l?l:""),2),o=c[0],s=c[1],u=i((0,a.useState)(""),2),f=u[0],p=u[1];return n().createElement(n().Fragment,null,n().createElement(d,{title:(0,m.__)("Thank you for sharing your experience.","templately"),subTitle:(0,m.__)("We truly value your feedback. Please let us know how we can improve and enhance your experience.","templately"),setIsClosed:t}),n().createElement("div",{className:"review-body"},n().createElement("div",{className:"review-wrapper"},n().createElement("form",{action:"",className:"review-form",onSubmit:function(e){e.preventDefault();var a=new FormData(e.target);a.set("rating",r),v()({url:ajaxurl+"?action=templately_pack_feedback_form&nonce="+window.templately.nonce,method:"POST",body:a}).then(function(){t(!0)}).catch(function(e){console.error("Error:",e)})}},n().createElement("div",{className:"form-control form-control-email"},n().createElement("label",{htmlFor:"review-email",className:"input-email-label"},(0,m.__)("Email","templately")),n().createElement("input",{name:"review-email",id:"review-email",type:"email",className:"input-email",placeholder:"<EMAIL>",value:o,onChange:function(e){return s(e.target.value)},required:!0})),n().createElement("div",{className:"form-control form-control-description"},n().createElement("label",{htmlFor:"review-description",className:"input-description-label"},(0,m.__)("Description","templately")),n().createElement("textarea",{name:"review-description",id:"review-description",className:"input-description",placeholder:"Describe your issue in details",value:f,onChange:function(e){return p(e.target.value)},required:!0})),n().createElement("button",{type:"submit",className:"review-submit"},(0,m.__)("Send","templately"))))))},h=function(e){var t=e.setIsClosed,r=i((0,a.useState)(),2),l=r[0],c=r[1];return(0,a.useEffect)(function(){var e=setTimeout(function(){t(!0)},5e3);return c(e),function(){l&&clearTimeout(l)}},[]),n().createElement(n().Fragment,null,n().createElement(d,{title:(0,m.__)("Thanks a lot for your Feedback!","templately"),setIsClosed:t}),n().createElement("div",{className:"review-body"},n().createElement("div",{className:"review-wrapper"},n().createElement("p",{className:"description"},(0,m.__)("We really appreciate you taking the time to share your thoughts with us. Your feedback helps us improve and create a better experience for everyone.","templately")))))},E=function(e){var t=e.setCurrentScreen,r=e.setIsClosed,a=e.setRating,l=e.email,c=function(e){a(e),t(e>=5?"review":"feedback")};return n().createElement(n().Fragment,null,n().createElement(d,{setIsClosed:r,email:l}),n().createElement("div",{className:"review-body"},n().createElement("div",{className:"review-wrapper"},n().createElement("p",{className:"description"},(0,m.__)("I see that you've already started using Templately. How has your experience been so far, and how would you rate us?","templately")),n().createElement("div",{className:"ratting-wrapper"},n().createElement("div",{className:"review-ratings"},n().createElement("div",{className:"rating rating-1",onClick:function(){return c(1)}},n().createElement("span",{className:"icon"}),n().createElement("span",null,(0,m.__)("1 Star","templately"))),n().createElement("div",{className:"rating rating-2",onClick:function(){return c(2)}},n().createElement("span",{className:"icon"}),n().createElement("span",null,(0,m.__)("2 Stars","templately"))),n().createElement("div",{className:"rating rating-3",onClick:function(){return c(3)}},n().createElement("span",{className:"icon"}),n().createElement("span",null,(0,m.__)("3 Stars","templately"))),n().createElement("div",{className:"rating rating-4",onClick:function(){return c(4)}},n().createElement("span",{className:"icon"}),n().createElement("span",null,(0,m.__)("4 Stars","templately"))),n().createElement("div",{className:"rating rating-5",onClick:function(){return c(5)}},n().createElement("span",{className:"icon"}),n().createElement("span",null,(0,m.__)("5 Stars","templately")))),n().createElement("div",{className:"review-scale"},n().createElement("div",{className:"not-satisfied"},(0,m.__)("Not Satisfied","templately")),n().createElement("div",{className:"very-satisfied"},(0,m.__)("Very Satisfied","templately")))))))},b=function(){return n().createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",fill:"none",viewBox:"0 0 25 24"},n().createElement("mask",{id:"mask0_1187_1613",style:{maskType:"alpha"},width:"25",height:"24",x:"0",y:"0",maskUnits:"userSpaceOnUse"},n().createElement("path",{fill:"#D9D9D9",d:"M0.191 0H24.191V24H0.191z"})),n().createElement("g",{mask:"url(#mask0_1187_1613)"},n().createElement("path",{fill:"#fff",d:"M15.191 19l-1.425-1.4 4.6-4.6H2.191v-2h16.175l-4.575-4.6 1.4-1.4 7 7-7 7z"})))},g=function(e){var t=e.setIsClosed,r=e.email;return n().createElement(n().Fragment,null,n().createElement(d,{title:(0,m.__)("We're glad that you liked us! 😍","templately"),setIsClosed:function(e){t("close_button_5_star")}}),n().createElement("div",{className:"review-body"},n().createElement("div",{className:"review-wrapper"},n().createElement("p",{className:"description"},(0,m.__)("If you don't mind, could you take 30 seconds to review us on WordPress? Your feedback will help us improve and grow. Thank you in advance! 🙏","templately")))),n().createElement("div",{className:"review-footer"},n().createElement("a",{href:"https://wordpress.org/support/plugin/templately/reviews/#new-post",className:"button",target:"_blank",onClick:function(e){var a=new FormData;a.set("review-description","N/A"),a.set("review-email",r),a.set("rating",5),v()({url:ajaxurl+"?action=templately_pack_feedback_form&nonce="+window.templately.nonce,method:"POST",body:a}).then(function(){t(!0)}).catch(function(e){console.error("Error:",e)}),t(!0)}},n().createElement("span",null,(0,m.__)("Rate Templately","templately")),n().createElement(b,null)),n().createElement("a",{href:"#",className:"button button-secondary",onClick:function(e){e.preventDefault(),t("already_did")}},n().createElement("span",null,(0,m.__)("I already did","templately")))))},_=window.wp.url,S=function(e){var t=i((0,a.useState)(""),2),r=t[0],l=t[1],c=i((0,a.useState)(!1),2),o=c[0],s=c[1],m=i((0,a.useState)(0),2),u=m[0],f=m[1],p=i((0,a.useState)(!1),2),d=p[0],y=p[1];if((0,a.useEffect)(function(){if(o){var t=(0,_.addQueryArgs)(ajaxurl,{action:"templately_pack_import_close_feedback_modal",nonce:window.templately.nonce});o&&"string"==typeof o&&(t=(0,_.addQueryArgs)(t,{closeAction:o,"review-email":e.email})),v()({url:t,method:"GET"}).then(function(e){console.log("result:",e)}).catch(function(e){console.error("Error:",e)})}},[o]),(0,a.useEffect)(function(){setTimeout(function(){y(!0)},1500)},[]),!o&&d)return n().createElement(n().Fragment,null,n().createElement("div",{className:"templately-feedback-form "+r},function(t){switch(t){case"review":return n().createElement(g,{setCurrentScreen:l,setIsClosed:s,email:e.email});case"feedback":return n().createElement(w,{setCurrentScreen:l,setIsClosed:s,rating:u,email:e.email});case"feedbackcomplete":return n().createElement(h,{setCurrentScreen:l,setIsClosed:s});default:return n().createElement(E,{setCurrentScreen:l,setIsClosed:s,setRating:f,email:e.email})}}(r)))},N=window.wp.domReady;r.n(N)()(function(){c().render(n().createElement(S,window.templately),document.getElementById("templately-fsi-feedback"))})})();