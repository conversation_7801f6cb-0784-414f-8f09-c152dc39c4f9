(()=>{"use strict";var e={};function t(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function n(e,n){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,i,o,c,l=[],a=!0,u=!1;try{if(o=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;a=!1}else for(;!(a=(r=o.call(n)).done)&&(l.push(r.value),l.length!==t);a=!0);}catch(e){u=!0,i=e}finally{try{if(!a&&null!=n.return&&(c=n.return(),Object(c)!==c))return}finally{if(u)throw i}}return l}}(e,n)||function(e,n){if(e){if("string"==typeof e)return t(e,n);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?t(e,n):void 0}}(e,n)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}e.n=t=>{var n=t&&t.__esModule?()=>t.default:()=>t;return e.d(n,{a:n}),n},e.d=(t,n)=>{for(var r in n)e.o(n,r)&&!e.o(t,r)&&Object.defineProperty(t,r,{enumerable:!0,get:n[r]})},e.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t);const r=window.React;var i=e.n(r);const o=window.wp.hooks,c=(window.wp.data,window.wp.editor),l=window.wp.plugins,a=window.wp.i18n,u=window.wp.editPost,s=window.wp.apiFetch;var d=e.n(s);const b=window.wp.url;function p(){return p=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},p.apply(null,arguments)}var h,f,m=function(e){return i().createElement("svg",p({viewBox:"0 0 512 512",xmlns:"http://www.w3.org/2000/svg"},e),i().createElement("circle",{cx:90.5,cy:454.8,fill:"#ffb45a",r:57.2}),i().createElement("circle",{cx:256,cy:454.8,fill:"#ff7b8e",r:57.2}),i().createElement("circle",{cx:421.5,cy:454.8,fill:"#5ac0ff",r:57.2}),i().createElement("path",{d:"M356.5 94.5c-3.1 0-6.1.2-9.1.4C335 40.6 286.4 0 228.3 0c-15.3 0-29.9 2.8-43.4 8 0 15.3.1 32.9.2 40.4 15.6.1 45.8.2 59.1.2v57.8h-58.8v97.7h60.5c-1.7 4.3-4.3 10.7-5.4 13.2-11 26.5-38.3 41.3-66.6 36.2-26.9-4.9-46.9-27.8-48.4-55.9-.4-8.2-.2-16.5-.2-24.7V106h-20.7c-41.8 19.4-70.9 61.7-70.9 110.8 0 67.5 54.7 122.2 122.2 122.2h202c66.9-.7 120.8-55.1 120.8-122.2 0-67.5-54.7-122.3-122.2-122.3z",fill:"#5633d1"}))},y=c.PluginMoreMenuItem||u.PluginMoreMenuItem;h=jQuery,f={subscription:null,cacheElements:function(){var e=this;e.cache={},e.cache.$gutenberg=h("#editor"),e.cache.$switchMode=h(h("#templately-gutenberg-button-switch-mode").html()),e.cache.$switchGutenbergButton=e.cache.$switchMode.find("#templately-gutenberg-button"),e.cache.$templateSaveButton=e.cache.$switchMode.find("#templately-cloud-push"),e.bindEvents(),e.subscribe()},subscribe:function(){var e=this;e.subscription||(e.subscription=wp.data.subscribe(function(){setTimeout(function(){e.buildPanel()},1)}))},unsubscribe:function(){this.subscription&&(this.subscription(),this.subscription=null)},buildPanel:function(){var e=this;if("yes"!==templately.hide_buttons){if(!e.cache.$gutenberg.find("#templately-gutenberg-buttons").length){var t=e.cache.$gutenberg.find(".edit-post-header-toolbar").children().eq(0);e.cache.$switchMode.insertAfter(t)}}else e.removePanel()},removePanel:function(){this.cache.$gutenberg.find("#templately-gutenberg-buttons").remove()},bindEvents:function(){this.cache.$switchGutenbergButton.on("click",function(e){window.TemplatelyAppManager.open(null,"templately-gutenberg","gutenberg")}),this.cache.$templateSaveButton.on("click",function(e){window.TemplatelyAppManager.open({route:"clouds/save-template"},"templately-gutenberg","gutenberg")})},init:function(){if(this.cacheElements(),"templately_library"===templately.post_type){var e=new Set(["core/post-content","core/template-part"]);(0,o.addFilter)("blockEditor.__unstableCanInsertBlockType","removePostContentFromInserter",function(t,n){return e.has(n.name)||t},100)}}},h(document).ready(function(){f.init()}),window.TemplatelyGutenbergApp=f,(0,l.registerPlugin)("templately-gutenberg-hide-buttons",{render:function(){var e=n((0,r.useState)(!1),2),t=e[0],i=e[1],o=n((0,r.useState)("yes"===templately.hide_buttons?"yes":"no"),2),c=o[0],l=o[1];return React.createElement(y,{icon:React.createElement(m,{width:"20",height:"20"}),onClick:function(){i(!0);var e="yes"===c?"no":"yes";templately.hide_buttons=e,"yes"===e?(f.unsubscribe(),f.removePanel()):(f.subscribe(),f.buildPanel(),setTimeout(function(){f.buildPanel()},1));var t=(0,b.addQueryArgs)(ajaxurl,{action:"update_gutenberg_hide_buttons",hide_buttons:e,nonce:templately.nonce});d()({url:t,method:"GET"}).then(function(t){l(e)}).catch(function(e){}).finally(function(){i(!1)})},disabled:t},"yes"===c?(0,a.__)("Show Templately Buttons"):(0,a.__)("Hide Templately Buttons"))}})})();