/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/.pnpm/css-loader@6.11.0_webpack@5.98.0_webpack-cli@5.1.4_/node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[4].use[1]!./node_modules/.pnpm/postcss-loader@6.2.1_postcss@8.5.3_webpack@5.98.0_webpack-cli@5.1.4_/node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[4].use[2]!./node_modules/.pnpm/sass-loader@12.6.0_sass@1.86.0_webpack@5.98.0_webpack-cli@5.1.4_/node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[4].use[3]!./react-src/styles/platforms/gutenberg.scss ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
.bg-warning {
  background-color: #f39c12 !important;
}

.bg-danger {
  background-color: #c0392b !important;
}

.text-danger {
  color: #c0392b !important;
}

#templately-gutenberg .templately-gutenberg {
  position: relative;
}
#templately-gutenberg .templately-gutenberg:before {
  position: fixed;
  content: "";
  right: 0;
  left: 0;
  top: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
}
#templately-gutenberg .templately-gutenberg #templately-wrapper {
  position: fixed;
  right: 10%;
  left: 10%;
  top: 10%;
  bottom: 10%;
  box-shadow: 0 0 100px -30px rgba(0, 0, 0, 0.5);
  height: 80%;
}

.templately-icon {
  background-image: url(../images/logo-icon.svg);
  width: 20px;
  height: 20px;
  display: inline-block;
  margin-left: 7px;
}

#templately-gutenberg-buttons {
  display: flex;
}

#templately-cloud-push, #templately-gutenberg-button {
  text-transform: capitalize;
  background-color: rgb(86, 51, 209);
  color: #fff;
  border-color: rgb(86, 51, 209);
  display: inline-flex;
  align-items: center;
  min-height: 33px;
  margin: 2px;
}
#templately-cloud-push:focus, #templately-gutenberg-button:focus {
  outline: none;
  box-shadow: none;
}
#templately-cloud-push:hover, #templately-gutenberg-button:hover {
  background-color: rgb(67.716, 38.456, 170.544);
  border-color: rgb(67.716, 38.456, 170.544);
}
