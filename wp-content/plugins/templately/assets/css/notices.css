/* review notice */

.notice-templately-black_friday {
	padding: 16px 0;
}
/*
#wpnotice-templately-black_friday.notice-info {
	border-left-color: #5633D1;
}

#wpnotice-templately-black_friday .wpnotice-thumbnail-wrapper {
	padding-left: 20px;
	padding-right: 10px;
	grid-column-start: 2;
}

#wpnotice-templately-black_friday {
	grid-template-columns: 15px 180px 1fr !important;
}

#wpnotice-templately-black_friday .wpnotice-content-wrapper {
	padding: 0;
}

#wpnotice-templately-black_friday .wpnotice-content-wrapper a {
	text-transform: capitalize;
	margin-right: 16px;
}

#wpnotice-templately-black_friday .wpnotice-content-wrapper button {
	text-transform: capitalize;
}

#wpnotice-templately-black_friday .wpnotice-content-wrapper button:hover {
	background-color: transparent;
} */

#wpnotice-templately-holiday.notice-info,
#wpnotice-templately-black_friday.notice-info {
	padding: 10px 38px 10px 12px;
	border-left-color: #5626E7;
}
#wpnotice-templately-holiday .wpnotice-content-wrapper,
#wpnotice-templately-black_friday .wpnotice-content-wrapper {
	flex: 1;
	display: flex;
	align-items: center;
	justify-content: space-between;
	gap: 20px;
}
#wpnotice-templately-holiday .wpnotice-content-wrapper {
	gap: 10px;
	flex-flow: column;
	align-items: flex-start;
}
#wpnotice-templately-holiday .wpnotice-content-wrapper p,
#wpnotice-templately-black_friday .wpnotice-content-wrapper p {
	font-size: 14px;
}
#wpnotice-templately-holiday .button.button-primary,
#wpnotice-templately-black_friday .button.button-primary {
	display: inline-flex;
	align-items: center;
	gap: 8px;
	padding: 5px 15px;
	background: #5626E7;
	border: none;
	border-radius: 4px;
}
#wpnotice-templately-holiday .wpnotice-button-wrapper {
	display: flex;
	gap: 10px;
}
#wpnotice-templately-holiday .button.button-link {
	display: inline-flex;
	align-items: center;
	gap: 8px;
	padding: 5px 15px;
	border: none;
	border-radius: 4px;
	position: initial;
}
#wpnotice-templately-holiday .button.button-link:before {
	display: none;
}

.toplevel_page_templately .notice {
	display: none !important;
}

.wpnotice-thumbnail-wrapper img {
	display: block;
}

.button.button-primary.btn-nx-cta {
	background-color: #6A4BFF;
}

.button.button-primary.btn-nx-cta:hover {
	background-color: #5414d0;
}

.btn-nx-cta:focus {
	outline: none;
	box-shadow: none;
}

.wpnotice-content-wrapper > p {
	margin-top: 0px;
}

.notice-templately-opt_in {
	display: block !important;
}

.notice-templately-review {
	padding: 10px;
	background-color: #fff;
	border-radius: 3px;
	margin: 15px;
	grid-template-columns: 80px 1fr !important;
	display: grid !important;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	grid-template-columns: 60px 1fr !important;
}

.notice-templately-review:after {
	content: "";
	display: table;
	clear: both;
}

.wpdeveloper-notice-thumbnail {
	width: 90px;
	float: left;
	padding: 5px;
	text-align: center;
	border-right: 4px solid transparent;
}

.wpdeveloper-notice-thumbnail img {
	width: 72px;
	opacity: 0.85;
	-webkit-transition: all 0.3s;
	-o-transition: all 0.3s;
	transition: all 0.3s;
}

.wpdeveloper-notice-thumbnail img:hover {
	opacity: 1;
}

.wpdeveloper-notice-thumbnail img,
.wpdeveloper-update-notice .wpdeveloper-notice-thumbnail img,
.wpdeveloper-update_400k-notice .wpdeveloper-notice-thumbnail img {
	width: 32px;
}

.wpdeveloper-notice-thumbnail,
.wpdeveloper-update-notice .wpdeveloper-notice-thumbnail,
.wpdeveloper-update_400k-notice .wpdeveloper-notice-thumbnail {
	width: auto;
	padding: 7px;
}

.wpdeveloper-update-notice .wpdeveloper-notice-message,
.wpdeveloper-update_400k-notice .wpdeveloper-notice-message {
	padding: 5px 0;
}

.wpdeveloper-update-notice,
.wpdeveloper-update_400k-notice {
	border-color: #6648fe;
	padding: 0
}

a.ea-notice-cta {
	background-color: #4d18ff;
	background: linear-gradient(-30deg, #4d18ff, #9a7cff);
	margin-top: 30px;
	color: #fff;
	padding: 8px 20px;
	outline: none;
	text-decoration: none;
	border-radius: 3px;
	margin-left: 10px;
	transition: all 0.3s ease;
}

a.ea-notice-cta:hover {
	opacity: 0.85;
}

span.coupon-code {
	background: #ebebeb;
	padding: 5px 10px;
	letter-spacing: 0.035em;
}

.eael-review-text {
	overflow: hidden;
}

.eael-review-text h3 {
	font-size: 24px;
	margin: 0 0 5px;
	font-weight: 400;
	line-height: 1.3;
}

.eael-review-text p {
	font-size: 13px;
	margin: 0 0 5px;
}

.templately-notice-links {
	margin: 8px 0 0 0;
	padding: 0;
}

.templately-notice-links li {
	display: inline-flex;
	margin-right: 15px;
	align-items: center;
}

.templately-notice-links li a {
	display: inline-block;
	color: #10738b;
	text-decoration: none;
	position: relative;
}

.wpdeveloper-notice-message {
	padding: 10px 0;
}

.wpdeveloper-upsale-notice .wpdeveloper-notice-message {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	padding: 10px 0;
}

.wpdeveloper-upsale-notice .wpdeveloper-notice-message + .notice-dismiss {
	top: 10px;
}

.wpdeveloper-upsale-notice #plugin-install-core {
	margin-left: 10px;
}

.notice.notice-has-thumbnail {
	padding-left: 0;
}

.wpdeveloper-upsale-notice {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
}

.wpdeveloper-upsale-notice .wpdeveloper-notice-thumbnail {
	padding: 10px;
	width: 40px;
}

.wpdeveloper-upsale-notice .wpdeveloper-notice-thumbnail img {
	width: 32px;
}

.toplevel_page_eael-settings .wp-menu-image img {
	max-width: 20px;
	padding-top: 8px !important;
}

.wpdeveloper-upsale-notice .wpdeveloper-notice-message .button {
	margin-left: 15px;
}

.notice-has-thumbnail.wpdeveloper-freedom30-notice {
	display: grid;
	grid-template-columns: 70px 1fr;
	align-items: center;
	position: relative;
	/* background-image: linear-gradient(180deg, #8C94FF 0%, #2564FF 100%);
	color: #fff;
	border-left: 0px; */
}

.notice-has-thumbnail.wpdeveloper-freedom30-notice .wpdeveloper-notice-thumbnail {
	width: 70px;
}

.notice-has-thumbnail.wpdeveloper-freedom30-notice .wpdeveloper-notice-thumbnail img {
	width: 50px;
}

#wpnotice-templately-upsale {
	grid-template-columns: 1fr 50px !important;
}

#wpnotice-templately-upsale p {
	margin: 0;
	padding: 10px 0px;
}