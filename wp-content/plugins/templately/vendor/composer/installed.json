{"packages": [{"name": "priyomukul/wp-notice", "version": "v2.x-dev", "version_normalized": "2.9999999.9999999.9999999-dev", "source": {"type": "git", "url": "**************:priyomukul/wp-notice.git", "reference": "5471f3262e583ebcba56062d84faecc910bd04d6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/priyomukul/wp-notice/zipball/5471f3262e583ebcba56062d84faecc910bd04d6", "reference": "5471f3262e583ebcba56062d84faecc910bd04d6", "shasum": ""}, "time": "2023-12-27T06:49:41+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"PriyoMukul\\WPNotice\\": "src/"}}, "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "support": {"source": "https://github.com/priyomukul/wp-notice/tree/v2", "issues": "https://github.com/priyomukul/wp-notice/issues"}, "install-path": "../priyomukul/wp-notice"}], "dev": true, "dev-package-names": []}