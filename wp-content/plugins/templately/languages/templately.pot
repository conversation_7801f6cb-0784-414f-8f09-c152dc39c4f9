# Copyright (C) 2025 Templately
# This file is distributed under the same license as the Templately plugin.
msgid ""
msgstr ""
"Project-Id-Version: Templately 3.2.8\n"
"Report-Msgid-Bugs-To: https://wordpress.org/support/plugin/templately\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"POT-Creation-Date: 2025-07-17T11:48:17+00:00\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"X-Generator: WP-CLI 2.11.0\n"
"X-Domain: templately\n"

#. Plugin Name of the plugin
#. Author of the plugin
#: templately.php
#: includes/Core/Admin.php:494
#: includes/Core/Platform/Gutenberg.php:83
#: views/header.php:7
#: assets/js/elementor.js:1
#: react-src/elementor.js:93
#: assets/js/elementor.js:3015
msgid "Templately"
msgstr ""

#. Plugin URI of the plugin
#: templately.php
msgid "https://templately.com"
msgstr ""

#. Description of the plugin
#: templately.php
msgid "The Best Templates Cloud for Elementor & Gutenberg. Get access to stunning templates, WorkSpace, Cloud Library & many more."
msgstr ""

#. Author URI of the plugin
#: templately.php
msgid "https://templately.com/"
msgstr ""

#: includes/API/API.php:162
msgid "Sorry, you need to login/re-login again."
msgstr ""

#: includes/API/Conditions.php:92
msgid "Successfully saved."
msgstr ""

#: includes/API/Dependencies.php:92
#: includes/Core/Admin.php:496
#: includes/Core/Importer/Runners/ElementorContent.php:16
#: assets/js/settings.js:105787
#: assets/js/settings.js:110063
#: assets/js/templately.js:1
#: react-src/app/header/actionParts/Switcher.js:44
#: react-src/app/settings/components/Switcher.js:35
#: assets/js/templately.js:133881
#: assets/js/templately.js:137436
msgid "Elementor"
msgstr ""

#: includes/API/Dependencies.php:122
msgid "You have the plugin installed."
msgstr ""

#: includes/API/Import.php:30
#: includes/API/Import.php:163
#: includes/API/Import.php:184
msgid "Invalid ID is provided."
msgstr ""

#: includes/API/Import.php:36
#: includes/API/Import.php:169
#: includes/API/Import.php:190
msgid "Please try again we enabled container."
msgstr ""

#: includes/API/Import.php:69
msgid "Template data is invalid. Please kindly contact Templately Support."
msgstr ""

#: includes/API/Items.php:131
#: includes/API/Items.php:156
msgid "Invalid Type Call"
msgstr ""

#: includes/API/Items.php:149
msgid "Items slug cannot be empty."
msgstr ""

#: includes/API/Items.php:175
msgid "Item data not found"
msgstr ""

#: includes/API/Items.php:192
msgid "Search keyword cannot be empty."
msgstr ""

#: includes/API/Items.php:287
msgid "Unfavourite Action Failed: Something went wrong."
msgstr ""

#: includes/API/Items.php:321
#: includes/Utils/Base.php:31
#: includes/Utils/Base.php:42
#: assets/js/settings.js:93614
#: assets/js/settings.js:94134
#: assets/js/settings.js:109273
#: assets/js/settings.js:117766
#: assets/js/settings.js:119031
#: assets/js/settings.js:120255
#: assets/js/settings.js:120532
#: assets/js/settings.js:120558
#: assets/js/templately.js:1
#: react-src/app/components/clouds/components/partials/AddNewWorkSpace.js:18
#: react-src/app/components/clouds/components/partials/WorkSpaceFilter.js:94
#: react-src/app/revert/RevertWrapper.js:15
#: react-src/app/settings/tabs/Miscellaneous.js:13
#: react-src/redux/sagas/savedTemplatesSaga.js:82
#: react-src/utils/cloudFilesContextMenu.js:54
#: react-src/utils/savedTemplatesContextMenu.js:35
#: react-src/utils/workSpaceContextMenu.js:15
#: react-src/utils/workSpaceContextMenu.js:44
#: assets/js/templately.js:124084
#: assets/js/templately.js:124554
#: assets/js/templately.js:136456
#: assets/js/templately.js:138541
#: assets/js/templately.js:143052
#: assets/js/templately.js:143701
#: assets/js/templately.js:144729
#: assets/js/templately.js:144980
#: assets/js/templately.js:145009
msgid "Something went wrong."
msgstr ""

#: includes/API/Login.php:69
msgid "API Key field cannot be empty."
msgstr ""

#: includes/API/Login.php:76
#: includes/API/SignUp.php:40
msgid "Make sure you have given a valid email address."
msgstr ""

#: includes/API/Login.php:80
msgid "Password field cannot be empty."
msgstr ""

#: includes/API/Login.php:166
msgid "Logged out."
msgstr ""

#: includes/API/MyClouds.php:112
msgid "Missing required dependency Elementor, please install before trying again."
msgstr ""

#: includes/API/MyClouds.php:179
msgid "Invalid Item ID for download cloud item."
msgstr ""

#: includes/API/MyClouds.php:237
msgid "Invalid Item ID for delete cloud item."
msgstr ""

#: includes/API/SavedTemplates.php:29
msgid "Sorry, you do not have permission to delete this template."
msgstr ""

#: includes/API/SignUp.php:31
#: assets/js/settings.js:120062
#: assets/js/templately.js:1
#: react-src/utils/messages.js:33
#: assets/js/templately.js:144586
msgid "First name cannot be empty."
msgstr ""

#: includes/API/SignUp.php:34
#: assets/js/settings.js:120071
#: assets/js/templately.js:1
#: react-src/utils/messages.js:43
#: assets/js/templately.js:144596
msgid "Last name cannot be empty."
msgstr ""

#: includes/API/SignUp.php:37
msgid "Email cannot be empty."
msgstr ""

#: includes/API/SignUp.php:43
#: assets/js/settings.js:120078
#: assets/js/templately.js:1
#: react-src/utils/messages.js:51
#: assets/js/templately.js:144604
msgid "Password cannot be empty."
msgstr ""

#: includes/API/SignUp.php:46
msgid "Confirm password cannot be empty."
msgstr ""

#: includes/API/SignUp.php:50
msgid "Password and confirm password should be matched."
msgstr ""

#: includes/API/ThemeBuilderApi.php:62
msgid "Something went wrong. %s"
msgstr ""

#: includes/API/WorkSpaces.php:32
msgid "Sorry, you need to login/re-login to get your workspace list."
msgstr ""

#: includes/API/WorkSpaces.php:76
msgid "Invalid workspace slug for get workspace."
msgstr ""

#: includes/API/WorkSpaces.php:113
msgid "The workspace you looking for is not exists anymore."
msgstr ""

#: includes/API/WorkSpaces.php:133
#: includes/API/WorkSpaces.php:181
msgid "Workspace name cannot be empty."
msgstr ""

#: includes/API/WorkSpaces.php:221
msgid "You should provide a valid WorkSpace ID."
msgstr ""

#: includes/API/WorkSpaces.php:249
msgid "Invalid Workspace ID."
msgstr ""

#: includes/API/WorkSpaces.php:253
msgid "Invalid Files ID."
msgstr ""

#: includes/API/WorkSpaces.php:273
msgid "Invalid item id for get cloud item."
msgstr ""

#: includes/API/WorkSpaces.php:301
msgid "Invalid item id for delete cloud item."
msgstr ""

#: includes/Builder/Conditions/Archive.php:27
msgid "All Archives"
msgstr ""

#: includes/Builder/Conditions/Error.php:11
#: includes/Builder/Types/Error.php:11
msgid "404 Error"
msgstr ""

#: includes/Builder/Conditions/Front.php:11
msgid "Front Page"
msgstr ""

#: includes/Builder/Conditions/General.php:16
msgid "General"
msgstr ""

#: includes/Builder/Conditions/General.php:24
msgid "Entire Site"
msgstr ""

#: includes/Builder/Conditions/PostByAuthor.php:19
msgid "%s By Author"
msgstr ""

#: includes/Builder/Conditions/PostTypeArchive.php:31
#: includes/Builder/Conditions/PostTypeArchive.php:35
msgid "%s Archive"
msgstr ""

#: includes/Builder/Conditions/ProductSearch.php:11
msgid "Product Search"
msgstr ""

#: includes/Builder/Conditions/Search.php:15
msgid "Search"
msgstr ""

#: includes/Builder/Conditions/Singular.php:21
msgid "Singular"
msgstr ""

#: includes/Builder/Conditions/Singular.php:25
msgid "All Singular"
msgstr ""

#: includes/Builder/Conditions/WooCommerce.php:14
msgid "WooCommerce"
msgstr ""

#: includes/Builder/Managers/LocationManager.php:57
#: includes/Builder/Types/Header.php:12
msgid "Header"
msgstr ""

#: includes/Builder/Managers/LocationManager.php:61
#: includes/Builder/Types/Footer.php:12
msgid "Footer"
msgstr ""

#: includes/Builder/Managers/LocationManager.php:65
#: includes/Builder/Types/Archive.php:12
msgid "Archive"
msgstr ""

#: includes/Builder/Managers/LocationManager.php:69
#: includes/Builder/Types/Single.php:11
msgid "Single"
msgstr ""

#: includes/Builder/PageTemplates.php:29
msgid "Templately Gutenberg Template"
msgstr ""

#: includes/Builder/Source.php:97
msgid "Type"
msgstr ""

#: includes/Builder/Source.php:98
msgid "Platform"
msgstr ""

#: includes/Builder/Source.php:142
msgid "Edit Conditions"
msgstr ""

#: includes/Builder/Source.php:166
#: includes/Builder/Source.php:181
msgctxt "Template Library"
msgid "Templates"
msgstr ""

#: includes/Builder/Source.php:170
msgctxt "Template Library"
msgid "Template"
msgstr ""

#: includes/Builder/Source.php:171
#: includes/Builder/Source.php:172
msgctxt "Template Library"
msgid "Add New Template"
msgstr ""

#: includes/Builder/Source.php:173
msgctxt "Template Library"
msgid "Edit Template"
msgstr ""

#: includes/Builder/Source.php:174
msgctxt "Template Library"
msgid "New Template"
msgstr ""

#: includes/Builder/Source.php:175
msgctxt "Template Library"
msgid "All Templates"
msgstr ""

#: includes/Builder/Source.php:176
msgctxt "Template Library"
msgid "View Template"
msgstr ""

#: includes/Builder/Source.php:177
msgctxt "Template Library"
msgid "Search Template"
msgstr ""

#: includes/Builder/Source.php:178
msgctxt "Template Library"
msgid "No Templates found"
msgstr ""

#: includes/Builder/Source.php:179
msgctxt "Template Library"
msgid "No Templates found in Trash"
msgstr ""

#: includes/Builder/Source.php:223
#: assets/js/settings.js:96278
#: assets/js/settings.js:101671
#: assets/js/settings.js:101744
#: assets/js/settings.js:102182
#: assets/js/settings.js:102204
#: assets/js/settings.js:102988
#: assets/js/settings.js:103501
#: assets/js/templately.js:1
#: react-src/app/components/core/itemDetails/Packs/PacksHeader.js:115
#: react-src/app/components/header/ContentHeader.js:16
#: react-src/app/components/header/PlanSwitcher.js:39
#: react-src/app/components/profile/FavouritesFilter.js:37
#: react-src/app/components/profile/FavouritesFilter.js:59
#: react-src/app/components/sidebar/components/partials/Categories.js:42
#: react-src/app/components/sidebar/components/partials/TemplateTypes.js:47
#: assets/js/templately.js:126232
#: assets/js/templately.js:130837
#: assets/js/templately.js:130887
#: assets/js/templately.js:131199
#: assets/js/templately.js:131221
#: assets/js/templately.js:131954
#: assets/js/templately.js:132384
msgid "All"
msgstr ""

#: includes/Builder/Types/Archive.php:16
msgid "Archives"
msgstr ""

#: includes/Builder/Types/CourseArchive.php:11
msgid "Course Archive"
msgstr ""

#: includes/Builder/Types/CourseArchive.php:15
msgid "Course Archives"
msgstr ""

#: includes/Builder/Types/CourseSingle.php:11
msgid "Course Single"
msgstr ""

#: includes/Builder/Types/CourseSingle.php:15
msgid "Courses Single"
msgstr ""

#: includes/Builder/Types/Error.php:15
msgid "404 Error Pages"
msgstr ""

#: includes/Builder/Types/Footer.php:16
msgid "Footers"
msgstr ""

#: includes/Builder/Types/Header.php:16
msgid "Headers"
msgstr ""

#: includes/Builder/Types/Page.php:14
#: includes/Builder/Widgets/Post_Content.php:134
msgid "Page"
msgstr ""

#: includes/Builder/Types/Page.php:18
#: assets/js/settings.js:96296
#: assets/js/settings.js:102192
#: assets/js/settings.js:109399
#: assets/js/templately.js:1
#: react-src/app/components/core/itemDetails/Packs/PacksHeader.js:135
#: react-src/app/components/profile/FavouritesFilter.js:47
#: react-src/app/routesTemplate.js:40
#: assets/js/templately.js:126252
#: assets/js/templately.js:131209
#: assets/js/templately.js:136574
msgid "Pages"
msgstr ""

#: includes/Builder/Types/PageSingle.php:11
#: includes/Builder/Types/PageSingle.php:15
msgid "Page Single"
msgstr ""

#: includes/Builder/Types/Post.php:11
msgid "Post"
msgstr ""

#: includes/Builder/Types/Post.php:15
msgid "Posts"
msgstr ""

#: includes/Builder/Types/ProductArchive.php:11
msgid "Product Archive"
msgstr ""

#: includes/Builder/Types/ProductArchive.php:15
msgid "Product Archives"
msgstr ""

#: includes/Builder/Types/ProductSingle.php:11
msgid "Product Single"
msgstr ""

#: includes/Builder/Types/ProductSingle.php:15
msgid "Products Single"
msgstr ""

#: includes/Builder/Types/Search.php:12
msgid "Search Result"
msgstr ""

#: includes/Builder/Types/Search.php:16
msgid "Search Results"
msgstr ""

#: includes/Builder/Types/Single.php:15
msgid "Singles"
msgstr ""

#: includes/Builder/Widgets/Featured_Image.php:22
#: includes/Builder/Widgets/Featured_Image.php:41
msgid "Featured Image"
msgstr ""

#: includes/Builder/Widgets/Featured_Image.php:59
#: includes/Builder/Widgets/Site_Logo.php:85
msgid "Image"
msgstr ""

#: includes/Builder/Widgets/Featured_Image.php:67
#: includes/Builder/Widgets/Site_Logo.php:93
msgid "Alignment"
msgstr ""

#: includes/Builder/Widgets/Featured_Image.php:71
#: includes/Builder/Widgets/Site_Logo.php:97
msgid "Left"
msgstr ""

#: includes/Builder/Widgets/Featured_Image.php:75
#: includes/Builder/Widgets/Site_Logo.php:101
msgid "Center"
msgstr ""

#: includes/Builder/Widgets/Featured_Image.php:79
#: includes/Builder/Widgets/Site_Logo.php:105
msgid "Right"
msgstr ""

#: includes/Builder/Widgets/Featured_Image.php:92
#: includes/Builder/Widgets/Site_Logo.php:118
msgid "Width"
msgstr ""

#: includes/Builder/Widgets/Featured_Image.php:127
#: includes/Builder/Widgets/Site_Logo.php:153
msgid "Max Width"
msgstr ""

#: includes/Builder/Widgets/Featured_Image.php:162
#: includes/Builder/Widgets/Site_Logo.php:188
msgid "Height"
msgstr ""

#: includes/Builder/Widgets/Featured_Image.php:184
#: includes/Builder/Widgets/Site_Logo.php:210
msgid "Object Fit"
msgstr ""

#: includes/Builder/Widgets/Featured_Image.php:190
#: includes/Builder/Widgets/Site_Logo.php:216
#: assets/js/settings.js:98460
#: assets/js/settings.js:98598
#: assets/js/settings.js:98610
#: assets/js/settings.js:110709
#: assets/js/settings.js:111280
#: assets/js/templately.js:1
#: react-src/app/components/core/itemDetails/Packs/import-parts/Typography/index.js:41
#: react-src/app/components/core/itemDetails/Packs/import-parts/Typography/index.js:50
#: react-src/app/components/core/itemDetails/Packs/import-parts/Typography/TypographyRow.js:23
#: react-src/app/settings/components/TypographyRow.js:24
#: react-src/app/settings/tabs/Typography.js:45
#: assets/js/templately.js:128270
#: assets/js/templately.js:128384
#: assets/js/templately.js:128393
#: assets/js/templately.js:138080
#: assets/js/templately.js:138696
msgid "Default"
msgstr ""

#: includes/Builder/Widgets/Featured_Image.php:191
#: includes/Builder/Widgets/Site_Logo.php:217
msgid "Fill"
msgstr ""

#: includes/Builder/Widgets/Featured_Image.php:192
#: includes/Builder/Widgets/Site_Logo.php:218
msgid "Cover"
msgstr ""

#: includes/Builder/Widgets/Featured_Image.php:193
#: includes/Builder/Widgets/Site_Logo.php:219
msgid "Contain"
msgstr ""

#: includes/Builder/Widgets/Featured_Image.php:205
#: includes/Builder/Widgets/Site_Logo.php:231
msgid "Object Position"
msgstr ""

#: includes/Builder/Widgets/Featured_Image.php:208
#: includes/Builder/Widgets/Site_Logo.php:234
msgid "Center Center"
msgstr ""

#: includes/Builder/Widgets/Featured_Image.php:209
#: includes/Builder/Widgets/Site_Logo.php:235
msgid "Center Left"
msgstr ""

#: includes/Builder/Widgets/Featured_Image.php:210
#: includes/Builder/Widgets/Site_Logo.php:236
msgid "Center Right"
msgstr ""

#: includes/Builder/Widgets/Featured_Image.php:211
#: includes/Builder/Widgets/Site_Logo.php:237
msgid "Top Center"
msgstr ""

#: includes/Builder/Widgets/Featured_Image.php:212
#: includes/Builder/Widgets/Site_Logo.php:238
msgid "Top Left"
msgstr ""

#: includes/Builder/Widgets/Featured_Image.php:213
#: includes/Builder/Widgets/Site_Logo.php:239
msgid "Top Right"
msgstr ""

#: includes/Builder/Widgets/Featured_Image.php:214
#: includes/Builder/Widgets/Site_Logo.php:240
msgid "Bottom Center"
msgstr ""

#: includes/Builder/Widgets/Featured_Image.php:215
#: includes/Builder/Widgets/Site_Logo.php:241
msgid "Bottom Left"
msgstr ""

#: includes/Builder/Widgets/Featured_Image.php:216
#: includes/Builder/Widgets/Site_Logo.php:242
msgid "Bottom Right"
msgstr ""

#: includes/Builder/Widgets/Featured_Image.php:241
#: includes/Builder/Widgets/Site_Logo.php:267
msgid "Border Radius"
msgstr ""

#: includes/Builder/Widgets/Post_Content.php:21
msgid "Post Content"
msgstr ""

#: includes/Builder/Widgets/Post_Content.php:40
msgid "Content"
msgstr ""

#: includes/Builder/Widgets/Post_Content.php:48
#: includes/Builder/Widgets/Post_Title.php:79
msgid "Text Color"
msgstr ""

#: includes/Builder/Widgets/Post_Content.php:130
msgid "Pages:"
msgstr ""

#: includes/Builder/Widgets/Post_Title.php:23
msgid "Post Title"
msgstr ""

#: includes/Builder/Widgets/Post_Title.php:42
#: includes/Builder/Widgets/Post_Title.php:71
msgid "Heading"
msgstr ""

#: includes/Builder/Widgets/Post_Title.php:49
msgid "HTML Tag"
msgstr ""

#: includes/Builder/Widgets/Site_Logo.php:22
#: includes/Builder/Widgets/Site_Logo.php:41
#: includes/Builder/Widgets/Site_Logo.php:48
msgid "Site Logo"
msgstr ""

#: includes/Builder/Widgets/Site_Logo.php:66
msgid "Change Site Logo"
msgstr ""

#: includes/Core/Admin.php:50
msgctxt "Post type general name"
msgid "Theme Builders"
msgstr ""

#: includes/Core/Admin.php:51
msgctxt "Post type singular name"
msgid "Theme Builder"
msgstr ""

#: includes/Core/Admin.php:52
msgctxt "Admin Menu text"
msgid "Theme Builder"
msgstr ""

#: includes/Core/Admin.php:53
msgctxt "Add New on Toolbar"
msgid "Book"
msgstr ""

#: includes/Core/Admin.php:54
msgid "Add New"
msgstr ""

#: includes/Core/Admin.php:55
#: assets/js/settings.js:89732
#: assets/js/templately.js:1
#: react-src/app/add-new/AdminHeader.js:52
#: assets/js/templately.js:120605
msgid "Add New Template"
msgstr ""

#: includes/Core/Admin.php:56
msgid "New Template"
msgstr ""

#: includes/Core/Admin.php:57
#: assets/js/settings.js:100265
#: assets/js/templately.js:1
#: react-src/app/components/core/modals/ImportSuccess.js:20
#: assets/js/templately.js:129786
msgid "Edit Template"
msgstr ""

#: includes/Core/Admin.php:58
#: assets/js/settings.js:100271
#: assets/js/templately.js:1
#: react-src/app/components/core/modals/ImportSuccess.js:33
#: assets/js/templately.js:129799
msgid "View Template"
msgstr ""

#: includes/Core/Admin.php:59
msgid "All Templates"
msgstr ""

#: includes/Core/Admin.php:60
msgid "Search Templates"
msgstr ""

#: includes/Core/Admin.php:61
msgid "Parent Templates:"
msgstr ""

#: includes/Core/Admin.php:62
msgid "No templates found."
msgstr ""

#: includes/Core/Admin.php:63
msgid "No templates found in Trash."
msgstr ""

#: includes/Core/Admin.php:64
msgctxt "Overrides the “Featured Image” phrase for this post type. Added in 4.3"
msgid "Template Cover Image"
msgstr ""

#: includes/Core/Admin.php:65
msgctxt "Overrides the “Set featured image” phrase for this post type. Added in 4.3"
msgid "Set cover image"
msgstr ""

#: includes/Core/Admin.php:66
msgctxt "Overrides the “Remove featured image” phrase for this post type. Added in 4.3"
msgid "Remove cover image"
msgstr ""

#: includes/Core/Admin.php:67
msgctxt "Overrides the “Use as featured image” phrase for this post type. Added in 4.3"
msgid "Use as cover image"
msgstr ""

#: includes/Core/Admin.php:68
msgctxt "The post type archive label used in nav menus. Default “Post Archives”. Added in 4.4"
msgid "Template archives"
msgstr ""

#: includes/Core/Admin.php:70
msgctxt "Overrides the “Uploaded to this post”/”Uploaded to this page” phrase (used when viewing media attached to a post). Added in 4.4"
msgid "Uploaded to this template"
msgstr ""

#: includes/Core/Admin.php:71
msgctxt "Screen reader text for the filter links heading on the post type listing screen. Default “Filter posts list”/”Filter pages list”. Added in 4.4"
msgid "Filter templates list"
msgstr ""

#: includes/Core/Admin.php:72
msgctxt "Screen reader text for the pagination heading on the post type listing screen. Default “Posts list navigation”/”Pages list navigation”. Added in 4.4"
msgid "Templates list navigation"
msgstr ""

#: includes/Core/Admin.php:73
msgctxt "Screen reader text for the items list heading on the post type listing screen. Default “Posts list”/”Pages list”. Added in 4.4"
msgid "Templates list"
msgstr ""

#: includes/Core/Admin.php:302
msgid "We hope you're enjoying %s! Could you please do us a favor and give us a review on %s to help us spread the word and boost our motivation?"
msgstr ""

#: includes/Core/Admin.php:311
msgid "Sure, you deserve it!"
msgstr ""

#: includes/Core/Admin.php:315
#: assets/js/dashboard.js:1
#: react-src/dashboard/Feedback/Review.js:65
#: assets/js/dashboard.js:3465
msgid "I already did"
msgstr ""

#: includes/Core/Admin.php:322
msgid "Maybe Later"
msgstr ""

#: includes/Core/Admin.php:334
msgid "I need help"
msgstr ""

#: includes/Core/Admin.php:338
msgid "Never show again"
msgstr ""

#: includes/Core/Admin.php:369
msgid "🔥 Get access to 5,000+ Ready Templates & save up to 65% OFF now"
msgstr ""

#: includes/Core/Admin.php:369
#: includes/Core/Admin.php:389
#: assets/js/settings.js:100224
#: assets/js/templately.js:1
#: react-src/app/components/core/modals/ImportFailed.js:25
#: assets/js/templately.js:129750
msgid "Upgrade to Pro"
msgstr ""

#: includes/Core/Admin.php:412
msgid "Get"
msgstr ""

#: includes/Core/Admin.php:413
msgid "Templately PRO"
msgstr ""

#: includes/Core/Admin.php:414
msgid "with up to 60% OFF & unlock 5500+ ready WordPress templates to power up web design in 2025."
msgstr ""

#: includes/Core/Admin.php:420
msgid "GET PRO Lifetime Access"
msgstr ""

#: includes/Core/Admin.php:425
msgid "No, I’ll Pay Full Price Later"
msgstr ""

#: includes/Core/Admin.php:495
msgid "requires"
msgstr ""

#: includes/Core/Admin.php:497
msgid "plugin to be installed and activated. Please install Elementor to continue."
msgstr ""

#: includes/Core/Admin.php:519
msgid "Open settings page"
msgstr ""

#: includes/Core/Admin.php:520
msgid "Templates Library"
msgstr ""

#: includes/Core/Importer/Form.php:25
msgid "Form JSON does not exists."
msgstr ""

#: includes/Core/Importer/Form.php:31
msgid "Cannot be imported."
msgstr ""

#: includes/Core/Importer/FullSiteImport.php:91
msgid "Invalid nonce"
msgstr ""

#: includes/Core/Importer/FullSiteImport.php:97
msgid "Insufficient permissions"
msgstr ""

#: includes/Core/Importer/FullSiteImport.php:344
#: includes/Core/Importer/Utils/Utils.php:296
#: includes/Core/Importer/Utils/Utils.php:304
msgid "Invalid Session ID."
msgstr ""

#: includes/Core/Importer/FullSiteImport.php:372
msgid "Invalid Pack ID."
msgstr ""

#: includes/Core/Importer/FullSiteImport.php:420
msgid "Please update the templately plugin."
msgstr ""

#: includes/Core/Importer/FullSiteImport.php:452
#: includes/Core/Importer/FullSiteImport.php:840
#: assets/js/settings.js:96074
#: assets/js/settings.js:96090
#: assets/js/settings.js:106516
#: assets/js/templately.js:1
#: react-src/app/components/core/itemDetails/Packs/FullSiteImport.js:64
#: react-src/app/components/core/itemDetails/Packs/FullSiteImport.js:88
#: react-src/app/hooks/useFullSiteImport.js:310
#: assets/js/templately.js:126022
#: assets/js/templately.js:126046
#: assets/js/templately.js:134462
msgid "Oops!"
msgstr ""

#: includes/Core/Importer/FullSiteImport.php:537
msgid "Upload directory is not writable."
msgstr ""

#: includes/Core/Importer/FullSiteImport.php:546
msgid "Permission Passed"
msgstr ""

#: includes/Core/Importer/FullSiteImport.php:561
#: includes/Core/Importer/FullSiteImport.php:596
#: includes/Core/Importer/FullSiteImport.php:603
#: includes/Core/Importer/Runners/DownloadZip.php:61
#: includes/Core/Importer/Runners/DownloadZip.php:96
#: includes/Core/Importer/Runners/DownloadZip.php:110
msgid "Downloading Template Pack"
msgstr ""

#: includes/Core/Importer/FullSiteImport.php:578
#: includes/Core/Importer/Runners/DownloadZip.php:78
msgid "Template pack download failed"
msgstr ""

#: includes/Core/Importer/FullSiteImport.php:588
#: includes/Core/Importer/Runners/DownloadZip.php:88
msgid " Please try again or contact <a href='%s' target='_blank'>support</a>."
msgstr ""

#: includes/Core/Importer/FullSiteImport.php:593
#: includes/Core/Importer/Runners/DownloadZip.php:93
msgid "Template pack download failed with response code: "
msgstr ""

#: includes/Core/Importer/FullSiteImport.php:607
#: includes/Core/Importer/Runners/DownloadZip.php:114
msgid "Downloading Failed. Please try again"
msgstr ""

#: includes/Core/Importer/FullSiteImport.php:616
msgid "WP_Filesystem cannot be initialized"
msgstr ""

#: includes/Core/Importer/FullSiteImport.php:629
#: includes/Core/Importer/FullSiteImport.php:829
#: assets/js/settings.js:106517
#: assets/js/templately.js:1
#: react-src/app/hooks/useFullSiteImport.js:311
#: assets/js/templately.js:134463
msgid "It seems we're experiencing technical difficulties. Please try again or contact <a href='%s' target='_blank'>support</a>."
msgstr ""

#: includes/Core/Importer/FullSiteImport.php:676
msgid "Cannot be imported, as the manifest file is corrupted"
msgstr ""

#: includes/Core/Importer/Runners/Attachments.php:19
msgid "Attachments"
msgstr ""

#: includes/Core/Importer/Runners/Attachments.php:32
msgid "Importing Attachments: ("
msgstr ""

#: includes/Core/Importer/Runners/Attachments.php:34
msgid "Importing Attachment."
msgstr ""

#: includes/Core/Importer/Runners/BaseRunner.php:55
msgid "Platform is not specified. Please try again after specifying the platform."
msgstr ""

#: includes/Core/Importer/Runners/Customizer.php:17
msgid "Customizer"
msgstr ""

#: includes/Core/Importer/Runners/Customizer.php:29
#: includes/Core/Importer/Runners/Dependencies.php:31
#: includes/Core/Importer/Runners/DownloadZip.php:29
msgid "Updating customizer settings."
msgstr ""

#: includes/Core/Importer/Runners/Dependencies.php:19
msgid "Download Dependencies"
msgstr ""

#: includes/Core/Importer/Runners/DownloadZip.php:17
msgid "Download Zip"
msgstr ""

#: includes/Core/Importer/Runners/ElementorContent.php:32
msgid "Importing Elementor Page and Post Templates"
msgstr ""

#: includes/Core/Importer/Runners/ExtraContent.php:18
msgid "Extra Contents"
msgstr ""

#: includes/Core/Importer/Runners/ExtraContent.php:30
msgid "Importing Forms and Others Extra Contents"
msgstr ""

#: includes/Core/Importer/Runners/Finalizer.php:35
#: includes/Core/Importer/Runners/Finalizer.php:39
msgid "Finalizing Your Imports"
msgstr ""

#: includes/Core/Importer/Runners/GutenbergContent.php:20
msgid "Block Editor Content"
msgstr ""

#: includes/Core/Importer/Runners/GutenbergContent.php:36
msgid "Importing Gutenberg Page and Post Templates"
msgstr ""

#: includes/Core/Importer/Runners/Taxonomies.php:13
msgid "Taxonomies"
msgstr ""

#: includes/Core/Importer/Runners/Taxonomies.php:21
msgid "Importing Taxonomies"
msgstr ""

#: includes/Core/Importer/Runners/Templates.php:19
#: assets/js/settings.js:109415
#: assets/js/templately.js:1
#: react-src/app/routesTemplate.js:58
#: assets/js/templately.js:136592
msgid "Templates"
msgstr ""

#: includes/Core/Importer/Runners/Templates.php:23
msgid "Importing Templates (i.e: Header, Footer, etc)"
msgstr ""

#: includes/Core/Importer/Runners/WPContent.php:24
msgid "WordPress Contents"
msgstr ""

#: includes/Core/Importer/Runners/WPContent.php:28
msgid "Importing Content (Pages, Posts, Products, Navigation)"
msgstr ""

#: includes/Core/Importer/Utils/ElementorHelper.php:101
msgid "Templately Error code: T001"
msgstr ""

#: includes/Core/Importer/Utils/Utils.php:19
msgid "JSON file not exists. "
msgstr ""

#: includes/Core/Importer/Utils/Utils.php:178
msgid "URL is empty"
msgstr ""

#: includes/Core/Importer/Utils/Utils.php:183
msgid "Invalid URL"
msgstr ""

#: includes/Core/Platform/Elementor.php:84
msgid "You have to install/activate %s to import %s Template."
msgstr ""

#: includes/Core/Platform/Elementor.php:191
msgid "Successfully Deleted."
msgstr ""

#: includes/Core/Platform/Elementor.php:197
msgid "Something must went wrong."
msgstr ""

#: includes/Core/Platform/Gutenberg.php:87
msgid "Save %s in Templately"
msgstr ""

#: includes/Plugin.php:90
msgid "Cloning is forbidden."
msgstr ""

#: includes/Plugin.php:99
msgid "Un-serializing instances of this class is forbidden."
msgstr ""

#: includes/Utils/Installer.php:60
msgid "Sorry, you do not have permission to install a plugin."
msgstr ""

#: includes/Utils/Installer.php:117
msgid "Failed to install plugin"
msgstr ""

#: includes/Utils/Installer.php:127
msgid "Sorry, you do not have permission to activate a plugin."
msgstr ""

#: includes/Utils/Installer.php:156
msgid "Theme is already active"
msgstr ""

#: includes/Utils/Installer.php:161
msgid "Function themes_api does not exist"
msgstr ""

#: includes/Utils/Installer.php:191
msgid "Failed to install theme"
msgstr ""

#: includes/Utils/Installer.php:206
#: includes/Utils/Installer.php:264
msgid "Failed to activate theme"
msgstr ""

#: includes/Utils/Installer.php:217
msgid "The plugin requires PHP version %s or higher. You are running version %s."
msgstr ""

#: includes/Utils/Installer.php:226
msgid "The plugin requires WordPress version %s or higher. You are running version %s."
msgstr ""

#: views/builder/conditions.php:1
msgid "Display Condtions by Templately"
msgstr ""

#: views/builder/conditions.php:3
#: assets/js/elementor.js:1
#: react-src/elementor.js:210
#: assets/js/elementor.js:3132
msgid "Condition (by Templately)"
msgstr ""

#: views/header.php:10
msgid "Version"
msgstr ""

#: assets/js/dashboard.js:1
#: react-src/dashboard/Feedback/Feedback.js:33
#: assets/js/dashboard.js:3197
msgid "Thank you for sharing your experience."
msgstr ""

#: assets/js/dashboard.js:1
#: react-src/dashboard/Feedback/Feedback.js:34
#: assets/js/dashboard.js:3198
msgid "We truly value your feedback. Please let us know how we can improve and enhance your experience."
msgstr ""

#: assets/js/dashboard.js:1
#: assets/js/settings.js:102306
#: assets/js/templately.js:1
#: react-src/app/components/profile/Profile.js:55
#: react-src/dashboard/Feedback/Feedback.js:44
#: assets/js/dashboard.js:3208
#: assets/js/templately.js:131302
msgid "Email"
msgstr ""

#: assets/js/dashboard.js:1
#: react-src/dashboard/Feedback/Feedback.js:61
#: assets/js/dashboard.js:3225
msgid "Description"
msgstr ""

#: assets/js/dashboard.js:1
#: react-src/dashboard/Feedback/Feedback.js:74
#: assets/js/dashboard.js:3238
msgid "Send"
msgstr ""

#: assets/js/dashboard.js:1
#: react-src/dashboard/Feedback/FeedbackComplete.js:25
#: assets/js/dashboard.js:3273
msgid "Thanks a lot for your Feedback!"
msgstr ""

#: assets/js/dashboard.js:1
#: react-src/dashboard/Feedback/FeedbackComplete.js:31
#: assets/js/dashboard.js:3279
msgid "We really appreciate you taking the time to share your thoughts with us. Your feedback helps us improve and create a better experience for everyone."
msgstr ""

#: assets/js/dashboard.js:1
#: react-src/dashboard/Feedback/Rating.js:24
#: assets/js/dashboard.js:3346
msgid "I see that you've already started using Templately. How has your experience been so far, and how would you rate us?"
msgstr ""

#: assets/js/dashboard.js:1
#: react-src/dashboard/Feedback/Rating.js:35
#: assets/js/dashboard.js:3357
msgid "1 Star"
msgstr ""

#: assets/js/dashboard.js:1
#: react-src/dashboard/Feedback/Rating.js:41
#: assets/js/dashboard.js:3363
msgid "2 Stars"
msgstr ""

#: assets/js/dashboard.js:1
#: react-src/dashboard/Feedback/Rating.js:47
#: assets/js/dashboard.js:3369
msgid "3 Stars"
msgstr ""

#: assets/js/dashboard.js:1
#: react-src/dashboard/Feedback/Rating.js:53
#: assets/js/dashboard.js:3375
msgid "4 Stars"
msgstr ""

#: assets/js/dashboard.js:1
#: react-src/dashboard/Feedback/Rating.js:59
#: assets/js/dashboard.js:3381
msgid "5 Stars"
msgstr ""

#: assets/js/dashboard.js:1
#: react-src/dashboard/Feedback/Rating.js:64
#: assets/js/dashboard.js:3386
msgid "Not Satisfied"
msgstr ""

#: assets/js/dashboard.js:1
#: react-src/dashboard/Feedback/Rating.js:67
#: assets/js/dashboard.js:3389
msgid "Very Satisfied"
msgstr ""

#: assets/js/dashboard.js:1
#: react-src/dashboard/Feedback/Review.js:37
#: assets/js/dashboard.js:3437
msgid "We're glad that you liked us! 😍"
msgstr ""

#: assets/js/dashboard.js:1
#: react-src/dashboard/Feedback/Review.js:43
#: assets/js/dashboard.js:3443
msgid "If you don't mind, could you take 30 seconds to review us on WordPress? Your feedback will help us improve and grow. Thank you in advance! 🙏"
msgstr ""

#: assets/js/dashboard.js:1
#: react-src/dashboard/Feedback/Review.js:57
#: assets/js/dashboard.js:3457
msgid "Rate Templately"
msgstr ""

#: assets/js/elementor.js:1
#: react-src/elementor.js:112
#: react-src/elementor.js:132
#: assets/js/elementor.js:3034
#: assets/js/elementor.js:3054
msgid "Save Page in Templately"
msgstr ""

#: assets/js/elementor.js:1
#: react-src/elementor.js:158
#: assets/js/elementor.js:3080
msgid "Save Block in Templately"
msgstr ""

#: assets/js/settings.js:89721
#: assets/js/templately.js:1
#: react-src/app/add-new/AdminHeader.js:26
#: assets/js/templately.js:120579
msgid "Templates Make your Work More Efficient"
msgstr ""

#: assets/js/settings.js:89723
#: assets/js/templately.js:1
#: react-src/app/add-new/AdminHeader.js:32
#: assets/js/templately.js:120585
msgid "Templates help you create different sections of your site, and with just a single click, you can reuse them whenever needed"
msgstr ""

#: assets/js/settings.js:89847
#: assets/js/templately.js:1
#: react-src/app/add-new/ChooseTemplateType.js:57
#: assets/js/templately.js:120668
msgid "Select template type"
msgstr ""

#: assets/js/settings.js:89850
#: assets/js/templately.js:1
#: react-src/app/add-new/ChooseTemplateType.js:59
#: assets/js/templately.js:120670
msgid "No type Found"
msgstr ""

#: assets/js/settings.js:89872
#: assets/js/templately.js:1
#: react-src/app/add-new/ChooseTemplateType.js:85
#: assets/js/templately.js:120696
msgid "Create Template"
msgstr ""

#: assets/js/settings.js:90350
#: assets/js/settings.js:109005
#: assets/js/templately.js:1
#: react-src/app/components/TemplateContent.js:24
#: react-src/app/layouts/TemplatesTypeLayout.js:35
#: assets/js/templately.js:121128
#: assets/js/templately.js:136218
msgid "You are looking for: %s"
msgstr ""

#: assets/js/settings.js:90450
#: assets/js/templately.js:1
#: react-src/app/components/admin/FrontPage.js:56
#: assets/js/templately.js:121211
msgid "Ultimate Templates "
msgstr ""

#: assets/js/settings.js:90450
#: assets/js/templately.js:1
#: react-src/app/components/admin/FrontPage.js:57
#: assets/js/templately.js:121212
msgid "Cloud for WordPress"
msgstr ""

#: assets/js/settings.js:90450
#: assets/js/templately.js:1
#: react-src/app/components/admin/FrontPage.js:60
#: assets/js/templately.js:121215
msgid "Take Your Favourite Page Builders To A Whole New Level & Manage All Your Designs In One Place With Templately"
msgstr ""

#: assets/js/settings.js:90465
#: assets/js/settings.js:90978
#: assets/js/templately.js:1
#: react-src/app/components/admin/FrontPage.js:78
#: react-src/app/components/admin/SignIn.js:312
#: assets/js/templately.js:121233
#: assets/js/templately.js:121694
msgid "Sign In"
msgstr ""

#: assets/js/settings.js:90470
#: assets/js/templately.js:1
#: react-src/app/components/admin/FrontPage.js:85
#: assets/js/templately.js:121240
msgid "Free Sign Up"
msgstr ""

#: assets/js/settings.js:90839
#: assets/js/settings.js:91128
#: assets/js/settings.js:91192
#: assets/js/settings.js:120092
#: assets/js/templately.js:1
#: react-src/app/components/admin/SignIn.js:163
#: react-src/app/components/admin/SignUp.js:55
#: react-src/app/components/admin/SignUp.js:123
#: react-src/utils/messages.js:77
#: assets/js/templately.js:121545
#: assets/js/templately.js:121798
#: assets/js/templately.js:121866
#: assets/js/templately.js:144630
msgid "Opps...!"
msgstr ""

#: assets/js/settings.js:90866
#: assets/js/templately.js:1
#: react-src/app/components/admin/SignIn.js:192
#: assets/js/templately.js:121574
msgid "API Key"
msgstr ""

#: assets/js/settings.js:90867
#: assets/js/templately.js:1
#: react-src/app/components/admin/SignIn.js:193
#: assets/js/templately.js:121575
msgid "Your API Key"
msgstr ""

#: assets/js/settings.js:90897
#: assets/js/settings.js:91278
#: assets/js/templately.js:1
#: react-src/app/components/admin/SignIn.js:222
#: react-src/app/components/admin/SignUp.js:204
#: assets/js/templately.js:121604
#: assets/js/templately.js:121947
msgid "Email Address"
msgstr ""

#: assets/js/settings.js:90898
#: assets/js/settings.js:91279
#: assets/js/templately.js:1
#: react-src/app/components/admin/SignIn.js:223
#: react-src/app/components/admin/SignUp.js:205
#: assets/js/templately.js:121605
#: assets/js/templately.js:121948
msgid "Enter Your Email Address"
msgstr ""

#: assets/js/settings.js:90916
#: assets/js/settings.js:90917
#: assets/js/settings.js:91300
#: assets/js/settings.js:91301
#: assets/js/templately.js:1
#: react-src/app/components/admin/SignIn.js:242
#: react-src/app/components/admin/SignIn.js:243
#: react-src/app/components/admin/SignUp.js:223
#: react-src/app/components/admin/SignUp.js:224
#: assets/js/templately.js:121624
#: assets/js/templately.js:121625
#: assets/js/templately.js:121966
#: assets/js/templately.js:121967
msgid "Password"
msgstr ""

#: assets/js/settings.js:90950
#: assets/js/templately.js:1
#: react-src/app/components/admin/SignIn.js:270
#: assets/js/templately.js:121652
msgid "Forgot Password?"
msgstr ""

#: assets/js/settings.js:90957
#: assets/js/templately.js:1
#: react-src/app/components/admin/SignIn.js:288
#: assets/js/templately.js:121670
msgid "Global Sign In"
msgstr ""

#: assets/js/settings.js:90970
#: assets/js/templately.js:1
#: react-src/app/components/admin/SignIn.js:300
#: assets/js/templately.js:121682
msgid "All the sitewide users will be logged in with this Templately account"
msgstr ""

#: assets/js/settings.js:90978
#: assets/js/templately.js:1
#: react-src/app/components/admin/SignIn.js:313
#: assets/js/templately.js:121695
msgid "Signing In..."
msgstr ""

#: assets/js/settings.js:90983
#: assets/js/templately.js:1
#: react-src/app/components/admin/SignIn.js:319
#: assets/js/templately.js:121701
msgid "Connect via API"
msgstr ""

#: assets/js/settings.js:90983
#: assets/js/templately.js:1
#: react-src/app/components/admin/SignIn.js:321
#: assets/js/templately.js:121703
msgid "Connect via Login Credentials"
msgstr ""

#: assets/js/settings.js:91116
#: assets/js/templately.js:1
#: react-src/app/components/admin/SignUp.js:44
#: assets/js/templately.js:121787
msgid "First name and Last name cannot be empty."
msgstr ""

#: assets/js/settings.js:91237
#: assets/js/settings.js:91238
#: assets/js/templately.js:1
#: react-src/app/components/admin/SignUp.js:161
#: react-src/app/components/admin/SignUp.js:162
#: assets/js/templately.js:121904
#: assets/js/templately.js:121905
msgid "First Name"
msgstr ""

#: assets/js/settings.js:91255
#: assets/js/settings.js:91256
#: assets/js/templately.js:1
#: react-src/app/components/admin/SignUp.js:177
#: react-src/app/components/admin/SignUp.js:178
#: assets/js/templately.js:121920
#: assets/js/templately.js:121921
msgid "Last Name"
msgstr ""

#: assets/js/settings.js:91320
#: assets/js/settings.js:91321
#: assets/js/templately.js:1
#: react-src/app/components/admin/SignUp.js:243
#: react-src/app/components/admin/SignUp.js:244
#: assets/js/templately.js:121986
#: assets/js/templately.js:121987
msgid "Confirm Password"
msgstr ""

#: assets/js/settings.js:91352
#: assets/js/settings.js:91370
#: assets/js/templately.js:1
#: react-src/app/components/admin/SignUp.js:281
#: react-src/app/components/admin/SignUp.js:307
#: assets/js/templately.js:122024
#: assets/js/templately.js:122050
msgid "I agree to the Templately "
msgstr ""

#: assets/js/settings.js:91355
#: assets/js/templately.js:1
#: react-src/app/components/admin/SignUp.js:285
#: assets/js/templately.js:122028
msgid "Privacy Policy"
msgstr ""

#: assets/js/settings.js:91373
#: assets/js/templately.js:1
#: react-src/app/components/admin/SignUp.js:311
#: assets/js/templately.js:122054
msgid "Terms of Services"
msgstr ""

#: assets/js/settings.js:91391
#: assets/js/templately.js:1
#: react-src/app/components/admin/SignUp.js:339
#: assets/js/templately.js:122082
msgid "Sign Up"
msgstr ""

#: assets/js/settings.js:91583
#: assets/js/settings.js:95407
#: assets/js/settings.js:95412
#: assets/js/settings.js:95734
#: assets/js/settings.js:102463
#: assets/js/settings.js:119102
#: assets/js/settings.js:120301
#: assets/js/templately.js:1
#: react-src/app/components/clouds/MyClouds.js:83
#: react-src/app/components/core/item/Meta.js:84
#: react-src/app/components/core/item/Meta.js:90
#: react-src/app/components/core/itemDetails/Header.js:63
#: react-src/app/components/profile/PurchasedItems.js:100
#: react-src/utils/cloudFilesContextMenu.js:140
#: react-src/utils/savedTemplatesContextMenu.js:93
#: assets/js/templately.js:122222
#: assets/js/templately.js:125519
#: assets/js/templately.js:125525
#: assets/js/templately.js:125749
#: assets/js/templately.js:131479
#: assets/js/templately.js:143787
#: assets/js/templately.js:144787
msgid "Insert"
msgstr ""

#: assets/js/settings.js:91729
#: assets/js/settings.js:92533
#: assets/js/settings.js:92541
#: assets/js/settings.js:108587
#: assets/js/settings.js:108647
#: assets/js/settings.js:108668
#: assets/js/templately.js:1
#: react-src/app/components/clouds/components/modals/AddTemplatesModal.js:21
#: react-src/app/components/clouds/components/modals/AddTemplatesModal.js:28
#: react-src/app/components/clouds/SaveTemplates.js:48
#: react-src/app/layouts/CloudsLayout.js:31
#: react-src/app/layouts/CloudsLayout.js:86
#: react-src/app/layouts/CloudsLayout.js:107
#: assets/js/templately.js:122363
#: assets/js/templately.js:123105
#: assets/js/templately.js:123112
#: assets/js/templately.js:135847
#: assets/js/templately.js:135902
#: assets/js/templately.js:135923
msgid "My Clouds"
msgstr ""

#: assets/js/settings.js:91734
#: assets/js/settings.js:92806
#: assets/js/settings.js:94089
#: assets/js/settings.js:102640
#: assets/js/settings.js:108651
#: assets/js/templately.js:1
#: react-src/app/components/clouds/components/modals/CopyMoveModal.js:50
#: react-src/app/components/clouds/components/partials/WorkSpaceFilter.js:47
#: react-src/app/components/clouds/SaveTemplates.js:55
#: react-src/app/components/sidebar/components/CloudSidebar.js:21
#: react-src/app/layouts/CloudsLayout.js:90
#: assets/js/templately.js:122370
#: assets/js/templately.js:123399
#: assets/js/templately.js:124507
#: assets/js/templately.js:131614
#: assets/js/templately.js:135906
msgid "My WorkSpace"
msgstr ""

#: assets/js/settings.js:91740
#: assets/js/settings.js:92812
#: assets/js/settings.js:94095
#: assets/js/templately.js:1
#: react-src/app/components/clouds/components/modals/CopyMoveModal.js:56
#: react-src/app/components/clouds/components/partials/WorkSpaceFilter.js:53
#: react-src/app/components/clouds/SaveTemplates.js:61
#: assets/js/templately.js:122376
#: assets/js/templately.js:123405
#: assets/js/templately.js:124513
msgid "Shared WorkSpace"
msgstr ""

#: assets/js/settings.js:91804
#: assets/js/settings.js:91835
#: assets/js/templately.js:1
#: react-src/app/components/clouds/SaveTemplates.js:145
#: react-src/app/components/clouds/SaveTemplates.js:199
#: assets/js/templately.js:122460
#: assets/js/templately.js:122514
msgid "Something must went wrong!"
msgstr ""

#: assets/js/settings.js:91814
#: assets/js/templately.js:1
#: react-src/app/components/clouds/SaveTemplates.js:162
#: assets/js/templately.js:122477
msgid "Uploading..."
msgstr ""

#: assets/js/settings.js:91815
#: assets/js/templately.js:1
#: react-src/app/components/clouds/SaveTemplates.js:163
#: assets/js/templately.js:122478
msgid "Your template content is being pushed into Templately Cloud. Please do not refresh or close this tab."
msgstr ""

#: assets/js/settings.js:91825
#: assets/js/templately.js:1
#: react-src/app/components/clouds/SaveTemplates.js:180
#: assets/js/templately.js:122495
msgid "Successfully Pushed!"
msgstr ""

#: assets/js/settings.js:91826
#: assets/js/templately.js:1
#: react-src/app/components/clouds/SaveTemplates.js:181
#: assets/js/templately.js:122496
msgid "Your template is successfully pushed to your desire cloud folder. You will be redirected there."
msgstr ""

#: assets/js/settings.js:91852
#: assets/js/templately.js:1
#: react-src/app/components/clouds/SaveTemplates.js:224
#: assets/js/templately.js:122539
msgid "Save your templates and push to cloud"
msgstr ""

#: assets/js/settings.js:91857
#: assets/js/templately.js:1
#: react-src/app/components/clouds/SaveTemplates.js:229
#: assets/js/templately.js:122544
msgid "Type a name for the template..."
msgstr ""

#: assets/js/settings.js:91864
#: assets/js/templately.js:1
#: react-src/app/components/clouds/SaveTemplates.js:238
#: assets/js/templately.js:122553
msgid "Select "
msgstr ""

#: assets/js/settings.js:91871
#: assets/js/settings.js:94189
#: assets/js/templately.js:1
#: react-src/app/components/clouds/components/partials/WorkSpaceFilter.js:143
#: react-src/app/components/clouds/SaveTemplates.js:245
#: assets/js/templately.js:122560
#: assets/js/templately.js:124603
msgid "Loading workspace and shared workspaces list."
msgstr ""

#: assets/js/settings.js:91881
#: assets/js/templately.js:1
#: react-src/app/components/clouds/SaveTemplates.js:256
#: assets/js/templately.js:122571
msgid "Save & Push"
msgstr ""

#: assets/js/settings.js:91965
#: assets/js/templately.js:1
#: react-src/app/components/clouds/WorkSpaceWrapper.js:30
#: assets/js/templately.js:122627
msgid "Add New WorkSpace"
msgstr ""

#: assets/js/settings.js:91973
#: assets/js/templately.js:1
#: react-src/app/components/clouds/WorkSpaceWrapper.js:41
#: assets/js/templately.js:122638
msgid "No shared workspaces yet."
msgstr ""

#: assets/js/settings.js:91973
#: assets/js/templately.js:1
#: react-src/app/components/clouds/WorkSpaceWrapper.js:42
#: assets/js/templately.js:122639
msgid "You do not have any workspaces."
msgstr ""

#: assets/js/settings.js:92214
#: assets/js/templately.js:1
#: react-src/app/components/clouds/components/WorkSpace.js:116
#: assets/js/templately.js:122835
msgid "Shared with:"
msgstr ""

#: assets/js/settings.js:92544
#: assets/js/templately.js:1
#: react-src/app/components/clouds/components/modals/AddTemplatesModal.js:32
#: assets/js/templately.js:123116
msgid "My Workspaces"
msgstr ""

#: assets/js/settings.js:92547
#: assets/js/templately.js:1
#: react-src/app/components/clouds/components/modals/AddTemplatesModal.js:36
#: assets/js/templately.js:123120
msgid "Shared Workspaces"
msgstr ""

#: assets/js/settings.js:92620
#: assets/js/templately.js:1
#: react-src/app/components/clouds/components/modals/AddTemplatesModal.js:138
#: assets/js/templately.js:123222
msgid "Add Template to WorkSpace"
msgstr ""

#: assets/js/settings.js:92626
#: assets/js/templately.js:1
#: react-src/app/components/clouds/components/modals/AddTemplatesModal.js:141
#: assets/js/templately.js:123225
msgid "Select Source"
msgstr ""

#: assets/js/settings.js:92632
#: assets/js/templately.js:1
#: react-src/app/components/clouds/components/modals/AddTemplatesModal.js:149
#: assets/js/templately.js:123233
msgid "Select a source"
msgstr ""

#: assets/js/settings.js:92634
#: assets/js/templately.js:1
#: react-src/app/components/clouds/components/modals/AddTemplatesModal.js:151
#: assets/js/templately.js:123235
msgid "No Sources Found"
msgstr ""

#: assets/js/settings.js:92646
#: assets/js/templately.js:1
#: react-src/app/components/clouds/components/modals/AddTemplatesModal.js:163
#: assets/js/templately.js:123247
msgid "Select Workspace"
msgstr ""

#: assets/js/settings.js:92652
#: assets/js/templately.js:1
#: react-src/app/components/clouds/components/modals/AddTemplatesModal.js:171
#: assets/js/templately.js:123255
msgid "Select a workspace"
msgstr ""

#: assets/js/settings.js:92654
#: assets/js/templately.js:1
#: react-src/app/components/clouds/components/modals/AddTemplatesModal.js:174
#: assets/js/templately.js:123258
msgid "No %s Found"
msgstr ""

#: assets/js/settings.js:92654
#: assets/js/templately.js:1
#: react-src/app/components/clouds/components/modals/AddTemplatesModal.js:176
#: assets/js/templately.js:123260
msgid "Workspace"
msgstr ""

#: assets/js/settings.js:92654
#: assets/js/templately.js:1
#: react-src/app/components/clouds/components/modals/AddTemplatesModal.js:177
#: assets/js/templately.js:123261
msgid "Shared Workspace"
msgstr ""

#: assets/js/settings.js:92666
#: assets/js/templately.js:1
#: react-src/app/components/clouds/components/modals/AddTemplatesModal.js:192
#: assets/js/templately.js:123276
msgid "Select Templates"
msgstr ""

#: assets/js/settings.js:92674
#: assets/js/templately.js:1
#: react-src/app/components/clouds/components/modals/AddTemplatesModal.js:202
#: assets/js/templately.js:123286
msgid "Search by typing..."
msgstr ""

#: assets/js/settings.js:92676
#: assets/js/templately.js:1
#: react-src/app/components/clouds/components/modals/AddTemplatesModal.js:207
#: assets/js/templately.js:123291
msgid "No Templates Found"
msgstr ""

#: assets/js/settings.js:92679
#: assets/js/templately.js:1
#: react-src/app/components/clouds/components/modals/AddTemplatesModal.js:210
#: assets/js/templately.js:123294
msgid "Loading templates..."
msgstr ""

#: assets/js/settings.js:92689
#: assets/js/settings.js:93175
#: assets/js/settings.js:100707
#: assets/js/templately.js:1
#: react-src/app/components/clouds/components/modals/AddTemplatesModal.js:219
#: react-src/app/components/clouds/components/modals/DeleteWorkSpaceModal.js:14
#: react-src/app/components/core/modals/partials/DependencyShowcase.js:147
#: assets/js/templately.js:123303
#: assets/js/templately.js:123714
#: assets/js/templately.js:130107
msgid "Note"
msgstr ""

#: assets/js/settings.js:92689
#: assets/js/templately.js:1
#: react-src/app/components/clouds/components/modals/AddTemplatesModal.js:220
#: assets/js/templately.js:123304
msgid "You are seeing fewer items after initial load. You can search more to find your desire templates from the selected source."
msgstr ""

#: assets/js/settings.js:92697
#: assets/js/templately.js:1
#: react-src/app/components/clouds/components/modals/AddTemplatesModal.js:236
#: assets/js/templately.js:123320
msgid "Add to WorkSpace"
msgstr ""

#: assets/js/settings.js:92852
#: assets/js/templately.js:1
#: react-src/app/components/clouds/components/modals/CopyMoveModal.js:104
#: assets/js/templately.js:123453
msgid "%s to WorkSpace"
msgstr ""

#: assets/js/settings.js:92852
#: assets/js/settings.js:92888
#: assets/js/templately.js:1
#: react-src/app/components/clouds/components/modals/CopyMoveModal.js:104
#: react-src/app/components/clouds/components/modals/CopyMoveModal.js:136
#: assets/js/templately.js:123453
#: assets/js/templately.js:123485
msgid "Copy"
msgstr ""

#: assets/js/settings.js:92852
#: assets/js/settings.js:92888
#: assets/js/templately.js:1
#: react-src/app/components/clouds/components/modals/CopyMoveModal.js:104
#: react-src/app/components/clouds/components/modals/CopyMoveModal.js:136
#: assets/js/templately.js:123453
#: assets/js/templately.js:123485
msgid "Move"
msgstr ""

#: assets/js/settings.js:92858
#: assets/js/templately.js:1
#: react-src/app/components/clouds/components/modals/CopyMoveModal.js:107
#: assets/js/templately.js:123456
msgid "Item Name"
msgstr ""

#: assets/js/settings.js:92865
#: assets/js/templately.js:1
#: react-src/app/components/clouds/components/modals/CopyMoveModal.js:116
#: assets/js/templately.js:123465
msgid "Copy to"
msgstr ""

#: assets/js/settings.js:92865
#: assets/js/templately.js:1
#: react-src/app/components/clouds/components/modals/CopyMoveModal.js:116
#: assets/js/templately.js:123465
msgid "Move to"
msgstr ""

#: assets/js/settings.js:92872
#: assets/js/templately.js:1
#: react-src/app/components/clouds/components/modals/CopyMoveModal.js:122
#: assets/js/templately.js:123471
msgid "Select WorkSpace"
msgstr ""

#: assets/js/settings.js:92874
#: assets/js/templately.js:1
#: react-src/app/components/clouds/components/modals/CopyMoveModal.js:123
#: assets/js/templately.js:123472
msgid "No WorkSpace Found"
msgstr ""

#: assets/js/settings.js:93037
#: assets/js/templately.js:1
#: react-src/app/components/clouds/components/modals/CreateWorkspaceModal.js:97
#: assets/js/templately.js:123598
msgid "Type workspace title here..."
msgstr ""

#: assets/js/settings.js:93047
#: assets/js/settings.js:93505
#: assets/js/templately.js:1
#: react-src/app/components/clouds/components/modals/CreateWorkspaceModal.js:113
#: react-src/app/components/clouds/components/modals/EditWorkSpaceModal.js:252
#: assets/js/templately.js:123614
#: assets/js/templately.js:124007
msgid "Add People"
msgstr ""

#: assets/js/settings.js:93059
#: assets/js/settings.js:93517
#: assets/js/templately.js:1
#: react-src/app/components/clouds/components/modals/CreateWorkspaceModal.js:128
#: react-src/app/components/clouds/components/modals/EditWorkSpaceModal.js:267
#: assets/js/templately.js:123629
#: assets/js/templately.js:124022
msgid "Type email to add"
msgstr ""

#: assets/js/settings.js:93065
#: assets/js/settings.js:93523
#: assets/js/templately.js:1
#: react-src/app/components/clouds/components/modals/CreateWorkspaceModal.js:139
#: react-src/app/components/clouds/components/modals/EditWorkSpaceModal.js:278
#: assets/js/templately.js:123640
#: assets/js/templately.js:124033
msgid "Add"
msgstr ""

#: assets/js/settings.js:93075
#: assets/js/templately.js:1
#: react-src/app/components/clouds/components/modals/CreateWorkspaceModal.js:150
#: assets/js/templately.js:123651
msgid "Create Workspace"
msgstr ""

#: assets/js/settings.js:93109
#: assets/js/settings.js:93175
#: assets/js/templately.js:1
#: react-src/app/components/clouds/components/modals/DeleteFileModal.js:12
#: react-src/app/components/clouds/components/modals/DeleteWorkSpaceModal.js:12
#: assets/js/templately.js:123671
#: assets/js/templately.js:123712
msgid "Are You Sure?"
msgstr ""

#: assets/js/settings.js:93109
#: assets/js/templately.js:1
#: react-src/app/components/clouds/components/modals/DeleteFileModal.js:14
#: assets/js/templately.js:123673
msgid "You want to delete the file from your cloud."
msgstr ""

#: assets/js/settings.js:93118
#: assets/js/settings.js:93196
#: assets/js/settings.js:100710
#: assets/js/settings.js:109231
#: assets/js/templately.js:1
#: react-src/app/components/clouds/components/modals/DeleteFileModal.js:25
#: react-src/app/components/clouds/components/modals/DeleteWorkSpaceModal.js:40
#: react-src/app/components/core/modals/partials/DependencyShowcase.js:165
#: react-src/app/revert/Modal.js:81
#: react-src/app/settings/components/Modal.js:80
#: assets/js/templately.js:123684
#: assets/js/templately.js:123740
#: assets/js/templately.js:130125
#: assets/js/templately.js:136434
#: assets/js/templately.js:137394
msgid "Cancel"
msgstr ""

#: assets/js/settings.js:93128
#: assets/js/settings.js:93209
#: assets/js/settings.js:119128
#: assets/js/settings.js:120335
#: assets/js/settings.js:120590
#: assets/js/templately.js:1
#: react-src/app/components/clouds/components/modals/DeleteFileModal.js:33
#: react-src/app/components/clouds/components/modals/DeleteWorkSpaceModal.js:48
#: react-src/utils/cloudFilesContextMenu.js:166
#: react-src/utils/savedTemplatesContextMenu.js:131
#: react-src/utils/workSpaceContextMenu.js:83
#: assets/js/templately.js:123692
#: assets/js/templately.js:123748
#: assets/js/templately.js:143813
#: assets/js/templately.js:144825
#: assets/js/templately.js:145048
msgid "Delete"
msgstr ""

#: assets/js/settings.js:93175
#: assets/js/templately.js:1
#: react-src/app/components/clouds/components/modals/DeleteWorkSpaceModal.js:15
#: assets/js/templately.js:123715
msgid "The WorkSpace will be permanently deleted."
msgstr ""

#: assets/js/settings.js:93184
#: assets/js/templately.js:1
#: react-src/app/components/clouds/components/modals/DeleteWorkSpaceModal.js:29
#: assets/js/templately.js:123729
msgid "Want To Delete Files Too?"
msgstr ""

#: assets/js/settings.js:93352
#: assets/js/templately.js:1
#: react-src/app/components/clouds/components/modals/EditWorkSpaceModal.js:80
#: assets/js/templately.js:123835
msgid "invited"
msgstr ""

#: assets/js/settings.js:93497
#: assets/js/templately.js:1
#: react-src/app/components/clouds/components/modals/EditWorkSpaceModal.js:240
#: assets/js/templately.js:123995
msgid "Shared with"
msgstr ""

#: assets/js/settings.js:93533
#: assets/js/templately.js:1
#: react-src/app/components/clouds/components/modals/EditWorkSpaceModal.js:289
#: assets/js/templately.js:124044
msgid "Save Changes"
msgstr ""

#: assets/js/settings.js:93607
#: assets/js/templately.js:1
#: react-src/app/components/clouds/components/partials/AddNewWorkSpace.js:10
#: assets/js/templately.js:124076
msgid "Add New Workspace"
msgstr ""

#: assets/js/settings.js:93615
#: assets/js/settings.js:109274
#: assets/js/templately.js:1
#: react-src/app/components/clouds/components/partials/AddNewWorkSpace.js:19
#: react-src/app/revert/RevertWrapper.js:16
#: react-src/app/settings/tabs/Miscellaneous.js:14
#: assets/js/templately.js:124085
#: assets/js/templately.js:136457
#: assets/js/templately.js:138542
msgid "Something went wrong while creating workspace for you. Try Later or Contact support."
msgstr ""

#: assets/js/settings.js:93618
#: assets/js/templately.js:1
#: react-src/app/components/clouds/components/partials/AddNewWorkSpace.js:25
#: assets/js/templately.js:124091
msgid "Successfully Created"
msgstr ""

#: assets/js/settings.js:93621
#: assets/js/templately.js:1
#: react-src/app/components/clouds/components/partials/AddNewWorkSpace.js:30
#: assets/js/templately.js:124096
msgid "You have created a WorkSpace called"
msgstr ""

#: assets/js/settings.js:93654
#: assets/js/settings.js:94581
#: assets/js/templately.js:1
#: react-src/app/components/clouds/components/partials/AddNewWorkSpace.js:70
#: react-src/app/components/core/NoItemFound.js:11
#: assets/js/templately.js:124136
#: assets/js/templately.js:124867
msgid "Your account is not verified. Please check your email inbox and verify your Templately account."
msgstr ""

#: assets/js/settings.js:94138
#: assets/js/templately.js:1
#: react-src/app/components/clouds/components/partials/WorkSpaceFilter.js:98
#: assets/js/templately.js:124558
msgid "Successfully Added!"
msgstr ""

#: assets/js/settings.js:94139
#: assets/js/templately.js:1
#: react-src/app/components/clouds/components/partials/WorkSpaceFilter.js:99
#: assets/js/templately.js:124559
msgid "Your file has been added to this workspace."
msgstr ""

#: assets/js/settings.js:94177
#: assets/js/templately.js:1
#: react-src/app/components/clouds/components/partials/WorkSpaceFilter.js:132
#: assets/js/templately.js:124592
msgid "My Files"
msgstr ""

#: assets/js/settings.js:94182
#: assets/js/templately.js:1
#: react-src/app/components/clouds/components/partials/WorkSpaceFilter.js:137
#: assets/js/templately.js:124597
msgid "Current Workspace"
msgstr ""

#: assets/js/settings.js:94719
#: assets/js/templately.js:1
#: react-src/app/components/core/Pagination.js:106
#: assets/js/templately.js:124989
msgid "1"
msgstr ""

#: assets/js/settings.js:94724
#: assets/js/settings.js:94746
#: assets/js/templately.js:1
#: react-src/app/components/core/Pagination.js:113
#: react-src/app/components/core/Pagination.js:138
#: assets/js/templately.js:124996
#: assets/js/templately.js:125021
msgid "..."
msgstr ""

#: assets/js/settings.js:95394
#: assets/js/settings.js:99454
#: assets/js/templately.js:1
#: react-src/app/components/core/item/Meta.js:41
#: react-src/app/components/core/itemDetails/Sidebar.js:200
#: assets/js/templately.js:125476
#: assets/js/templately.js:129106
msgid "Average Rating"
msgstr ""

#: assets/js/settings.js:95396
#: assets/js/settings.js:99456
#: assets/js/templately.js:1
#: react-src/app/components/core/item/Meta.js:47
#: react-src/app/components/core/itemDetails/Sidebar.js:206
#: assets/js/templately.js:125482
#: assets/js/templately.js:129112
msgid "Not Rated"
msgstr ""

#: assets/js/settings.js:95401
#: assets/js/settings.js:99437
#: assets/js/templately.js:1
#: react-src/app/components/core/item/Meta.js:67
#: react-src/app/components/core/itemDetails/Sidebar.js:175
#: assets/js/templately.js:125502
#: assets/js/templately.js:129081
msgid "Added to Favourite"
msgstr ""

#: assets/js/settings.js:95401
#: assets/js/settings.js:99437
#: assets/js/templately.js:1
#: react-src/app/components/core/item/Meta.js:68
#: react-src/app/components/core/itemDetails/Sidebar.js:176
#: assets/js/templately.js:125503
#: assets/js/templately.js:129082
msgid "Add to Favourite"
msgstr ""

#: assets/js/settings.js:95407
#: assets/js/templately.js:1
#: react-src/app/components/core/item/Meta.js:83
#: assets/js/templately.js:125518
msgid "Total Downloads"
msgstr ""

#: assets/js/settings.js:95495
#: assets/js/templately.js:1
#: react-src/app/components/core/itemDetails/Banner.js:37
#: assets/js/templately.js:125572
msgid "Screenshots"
msgstr ""

#: assets/js/settings.js:95499
#: assets/js/settings.js:96303
#: assets/js/templately.js:1
#: react-src/app/components/core/itemDetails/Banner.js:44
#: react-src/app/components/core/itemDetails/Packs/PacksHeader.js:144
#: assets/js/templately.js:125579
#: assets/js/templately.js:126261
msgid "Live Demo"
msgstr ""

#: assets/js/settings.js:95621
#: assets/js/templately.js:1
#: react-src/app/components/core/itemDetails/FeaturedItems.js:24
#: assets/js/templately.js:125667
msgid "Featured Items"
msgstr ""

#: assets/js/settings.js:95714
#: assets/js/settings.js:100228
#: assets/js/templately.js:1
#: react-src/app/components/core/itemDetails/Header.js:33
#: react-src/app/components/core/modals/ImportFailed.js:32
#: assets/js/templately.js:125719
#: assets/js/templately.js:129757
msgid "Purchase This Item"
msgstr ""

#: assets/js/settings.js:95833
#: assets/js/templately.js:1
#: react-src/app/components/core/itemDetails/LayoutsInPack.js:14
#: assets/js/templately.js:125817
msgid "Layouts In This Pack"
msgstr ""

#: assets/js/settings.js:96075
#: assets/js/templately.js:1
#: react-src/app/components/core/itemDetails/Packs/FullSiteImport.js:65
#: assets/js/templately.js:126023
msgid "Unfortunately, you don't have permission to import templates. Please check your permissions or contact the website admin for help."
msgstr ""

#: assets/js/settings.js:96140
#: assets/js/templately.js:1
#: react-src/app/components/core/itemDetails/Packs/FullSiteImport.js:148
#: assets/js/templately.js:126106
msgid "Insert Full Site"
msgstr ""

#: assets/js/settings.js:96286
#: assets/js/settings.js:102187
#: assets/js/settings.js:109392
#: assets/js/settings.js:109407
#: assets/js/templately.js:1
#: react-src/app/components/core/itemDetails/Packs/PacksHeader.js:124
#: react-src/app/components/profile/FavouritesFilter.js:42
#: react-src/app/routesTemplate.js:32
#: react-src/app/routesTemplate.js:49
#: assets/js/templately.js:126241
#: assets/js/templately.js:131204
#: assets/js/templately.js:136566
#: assets/js/templately.js:136583
msgid "Blocks"
msgstr ""

#: assets/js/settings.js:96831
#: assets/js/settings.js:111062
#: assets/js/templately.js:1
#: react-src/app/components/core/itemDetails/Packs/import-parts/Customizer.js:317
#: react-src/app/settings/tabs/General.js:36
#: assets/js/templately.js:126669
#: assets/js/templately.js:138404
msgid "Site Title"
msgstr ""

#: assets/js/settings.js:96840
#: assets/js/settings.js:111067
#: assets/js/templately.js:1
#: react-src/app/components/core/itemDetails/Packs/import-parts/Customizer.js:329
#: react-src/app/settings/tabs/General.js:42
#: assets/js/templately.js:126681
#: assets/js/templately.js:138410
msgid "Site Tagline"
msgstr ""

#: assets/js/settings.js:97329
#: assets/js/templately.js:1
#: react-src/app/components/core/itemDetails/Packs/import-parts/Dependencies.js:278
#: assets/js/templately.js:127231
msgid "Back"
msgstr ""

#: assets/js/settings.js:98600
#: assets/js/settings.js:111282
#: assets/js/templately.js:1
#: react-src/app/components/core/itemDetails/Packs/import-parts/Typography/index.js:42
#: react-src/app/settings/tabs/Typography.js:46
#: assets/js/templately.js:128385
#: assets/js/templately.js:138697
msgid "System"
msgstr ""

#: assets/js/settings.js:98603
#: assets/js/settings.js:111285
#: assets/js/templately.js:1
#: react-src/app/components/core/itemDetails/Packs/import-parts/Typography/index.js:44
#: react-src/app/settings/tabs/Typography.js:48
#: assets/js/templately.js:128387
#: assets/js/templately.js:138699
msgid "Google"
msgstr ""

#: assets/js/settings.js:99058
#: assets/js/settings.js:99078
#: assets/js/templately.js:1
#: react-src/app/components/core/itemDetails/Packs/import-parts/index.js:60
#: react-src/app/components/core/itemDetails/Packs/import-parts/index.js:82
#: assets/js/templately.js:128754
#: assets/js/templately.js:128776
msgid "Please wait until the import process is complete before navigating back."
msgstr ""

#: assets/js/settings.js:99406
#: assets/js/templately.js:1
#: react-src/app/components/core/itemDetails/Sidebar.js:112
#: assets/js/templately.js:129018
msgid "Template Type"
msgstr ""

#: assets/js/settings.js:99414
#: assets/js/templately.js:1
#: react-src/app/components/core/itemDetails/Sidebar.js:127
#: assets/js/templately.js:129033
msgid "Category"
msgstr ""

#: assets/js/settings.js:99416
#: assets/js/templately.js:1
#: react-src/app/components/core/itemDetails/Sidebar.js:135
#: assets/js/templately.js:129041
msgid "Item Parent"
msgstr ""

#: assets/js/settings.js:99423
#: assets/js/templately.js:1
#: react-src/app/components/core/itemDetails/Sidebar.js:147
#: assets/js/templately.js:129053
msgid "Dependency"
msgstr ""

#: assets/js/settings.js:99431
#: assets/js/templately.js:1
#: react-src/app/components/core/itemDetails/Sidebar.js:159
#: assets/js/templately.js:129065
msgid "Price"
msgstr ""

#: assets/js/settings.js:99473
#: assets/js/templately.js:1
#: react-src/app/components/core/itemDetails/Sidebar.js:236
#: assets/js/templately.js:129142
msgid "Purchase This %s"
msgstr ""

#: assets/js/settings.js:99511
#: assets/js/templately.js:1
#: react-src/app/components/core/itemDetails/Tags.js:8
#: assets/js/templately.js:129172
msgid "Tags"
msgstr ""

#: assets/js/settings.js:99811
#: assets/js/settings.js:101760
#: assets/js/settings.js:102214
#: assets/js/templately.js:1
#: react-src/app/components/core/itemPartials/Badge.js:17
#: react-src/app/components/header/PlanSwitcher.js:53
#: react-src/app/components/profile/FavouritesFilter.js:69
#: assets/js/templately.js:129421
#: assets/js/templately.js:130901
#: assets/js/templately.js:131231
msgid "Pro"
msgstr ""

#: assets/js/settings.js:99811
#: assets/js/settings.js:101752
#: assets/js/settings.js:102209
#: assets/js/templately.js:1
#: react-src/app/components/core/itemPartials/Badge.js:17
#: react-src/app/components/header/PlanSwitcher.js:46
#: react-src/app/components/profile/FavouritesFilter.js:64
#: assets/js/templately.js:129421
#: assets/js/templately.js:130894
#: assets/js/templately.js:131226
msgid "Starter"
msgstr ""

#: assets/js/settings.js:100044
#: assets/js/templately.js:1
#: react-src/app/components/core/modals/DependencyInstaller.js:62
#: assets/js/templately.js:129555
msgid "Oops! Seems you don't have %s to import the %s Template"
msgstr ""

#: assets/js/settings.js:100045
#: assets/js/templately.js:1
#: react-src/app/components/core/modals/DependencyInstaller.js:80
#: assets/js/templately.js:129573
msgid "%6$s %1$s is a part of a %2$s which is available in %3$s. You need to install Elementor Pro to import %1$s template from here. %4$s, you can insert this template from %5$s on your single page."
msgstr ""

#: assets/js/settings.js:100045
#: assets/js/templately.js:1
#: react-src/app/components/core/modals/DependencyInstaller.js:103
#: assets/js/templately.js:129596
msgid "NOTE"
msgstr ""

#: assets/js/settings.js:100127
#: assets/js/settings.js:100839
#: assets/js/templately.js:1
#: react-src/app/components/core/modals/DependencyInstaller.js:175
#: react-src/app/components/core/modals/partials/ImportModal.js:82
#: assets/js/templately.js:129668
#: assets/js/templately.js:130224
msgid "Importing..."
msgstr ""

#: assets/js/settings.js:100128
#: assets/js/settings.js:100840
#: assets/js/templately.js:1
#: react-src/app/components/core/modals/DependencyInstaller.js:176
#: react-src/app/components/core/modals/partials/ImportModal.js:83
#: assets/js/templately.js:129669
#: assets/js/templately.js:130225
msgid "Your chosen template is being imported right now, please do not refresh or close this tab."
msgstr ""

#: assets/js/settings.js:100133
#: assets/js/templately.js:1
#: react-src/app/components/core/modals/DependencyInstaller.js:187
#: assets/js/templately.js:129680
msgid "Checking..."
msgstr ""

#: assets/js/settings.js:100134
#: assets/js/templately.js:1
#: react-src/app/components/core/modals/DependencyInstaller.js:188
#: assets/js/templately.js:129681
msgid "Checking template dependencies are installed or not..."
msgstr ""

#: assets/js/settings.js:100212
#: assets/js/templately.js:1
#: react-src/app/components/core/modals/ImportFailed.js:11
#: assets/js/templately.js:129736
msgid "Importing Failed"
msgstr ""

#: assets/js/settings.js:100260
#: assets/js/templately.js:1
#: react-src/app/components/core/modals/ImportSuccess.js:7
#: assets/js/templately.js:129773
msgid "Imported successfully!"
msgstr ""

#: assets/js/settings.js:100260
#: assets/js/templately.js:1
#: react-src/app/components/core/modals/ImportSuccess.js:9
#: assets/js/templately.js:129775
msgid "You can now edit or preview the template or, you can push it to Templately Cloud to share with your team."
msgstr ""

#: assets/js/settings.js:100268
#: assets/js/templately.js:1
#: react-src/app/components/core/modals/ImportSuccess.js:27
#: assets/js/templately.js:129793
msgid "Edit Template with Elementor"
msgstr ""

#: assets/js/settings.js:100317
#: assets/js/templately.js:1
#: react-src/app/components/core/modals/KitWarningModal.js:19
#: assets/js/templately.js:129826
msgid "Force Delete"
msgstr ""

#: assets/js/settings.js:100319
#: assets/js/templately.js:1
#: react-src/app/components/core/modals/KitWarningModal.js:21
#: assets/js/templately.js:129828
msgid "Keep my settings"
msgstr ""

#: assets/js/settings.js:100661
#: assets/js/templately.js:1
#: react-src/app/components/core/modals/partials/DependencyShowcase.js:70
#: assets/js/templately.js:130030
msgid "Required Dependencies"
msgstr ""

#: assets/js/settings.js:100661
#: assets/js/templately.js:1
#: react-src/app/components/core/modals/partials/DependencyShowcase.js:72
#: assets/js/templately.js:130032
msgid "To import this item you need to install all the Plugin(s) listed below:"
msgstr ""

#: assets/js/settings.js:100707
#: assets/js/templately.js:1
#: react-src/app/components/core/modals/partials/DependencyShowcase.js:150
#: assets/js/templately.js:130110
msgid "Make sure you have manually installed & activated the <em><strong>%s</strong></em> listed above"
msgstr ""

#: assets/js/settings.js:100707
#: assets/js/templately.js:1
#: react-src/app/components/core/modals/partials/DependencyShowcase.js:154
#: assets/js/templately.js:130114
msgid "Pro Plugin"
msgid_plural "Pro Plugin's"
msgstr[0] ""
msgstr[1] ""

#: assets/js/settings.js:100717
#: assets/js/templately.js:1
#: react-src/app/components/core/modals/partials/DependencyShowcase.js:171
#: assets/js/templately.js:130131
msgid "Install & Proceed to Import"
msgstr ""

#: assets/js/settings.js:100925
#: assets/js/templately.js:1
#: react-src/app/components/core/modals/partials/InstallerInBackground.js:22
#: assets/js/templately.js:130286
msgid "Ignored"
msgstr ""

#: assets/js/settings.js:100979
#: assets/js/templately.js:1
#: react-src/app/components/core/modals/partials/InstallerInBackground.js:93
#: assets/js/templately.js:130357
msgid "Installing..."
msgstr ""

#: assets/js/settings.js:100991
#: assets/js/templately.js:1
#: react-src/app/components/core/modals/partials/InstallerInBackground.js:108
#: assets/js/templately.js:130372
msgid "Retry & Proceed to Import"
msgstr ""

#: assets/js/settings.js:100994
#: assets/js/templately.js:1
#: react-src/app/components/core/modals/partials/InstallerInBackground.js:116
#: assets/js/templately.js:130380
msgid "Success"
msgstr ""

#: assets/js/settings.js:101066
#: assets/js/templately.js:1
#: react-src/app/components/core/modals/partials/importPartials/CreatePage.js:22
#: assets/js/templately.js:130437
msgid "Create a new page from this template to make it available as a draft page in your pages list."
msgstr ""

#: assets/js/settings.js:101070
#: assets/js/templately.js:1
#: react-src/app/components/core/modals/partials/importPartials/CreatePage.js:30
#: assets/js/templately.js:130445
msgid "Type a title here..."
msgstr ""

#: assets/js/settings.js:101077
#: assets/js/templately.js:1
#: react-src/app/components/core/modals/partials/importPartials/CreatePage.js:36
#: assets/js/templately.js:130451
msgid "Create a Page"
msgstr ""

#: assets/js/settings.js:101125
#: assets/js/templately.js:1
#: react-src/app/components/core/modals/partials/importPartials/Import.js:17
#: assets/js/templately.js:130478
msgid "Import this template to your library to make it available in your Elementor Saved Templates(opens in a new tab) list for future use."
msgstr ""

#: assets/js/settings.js:101131
#: assets/js/templately.js:1
#: react-src/app/components/core/modals/partials/importPartials/Import.js:27
#: assets/js/templately.js:130488
msgid "Import to Library"
msgstr ""

#: assets/js/settings.js:102197
#: assets/js/settings.js:109385
#: assets/js/templately.js:1
#: react-src/app/components/profile/FavouritesFilter.js:52
#: react-src/app/routesTemplate.js:24
#: assets/js/templately.js:131214
#: assets/js/templately.js:136558
msgid "Packs"
msgstr ""

#: assets/js/settings.js:102306
#: assets/js/templately.js:1
#: react-src/app/components/profile/Profile.js:58
#: assets/js/templately.js:131305
msgid "Joining Date"
msgstr ""

#: assets/js/settings.js:102308
#: assets/js/settings.js:102718
#: assets/js/templately.js:1
#: react-src/app/components/profile/Profile.js:64
#: react-src/app/components/sidebar/components/CloudSidebar.js:123
#: assets/js/templately.js:131311
#: assets/js/templately.js:131716
msgid "You haven't verify your account yet. Please check your email inbox."
msgstr ""

#: assets/js/settings.js:102314
#: assets/js/settings.js:105702
#: assets/js/templately.js:1
#: react-src/app/components/profile/Profile.js:78
#: react-src/app/header/actionParts/ProfileDrops.js:80
#: assets/js/templately.js:131325
#: assets/js/templately.js:133794
msgid "Manage Account"
msgstr ""

#: assets/js/settings.js:102320
#: assets/js/settings.js:105557
#: assets/js/templately.js:1
#: react-src/app/components/profile/Profile.js:86
#: react-src/app/header/actionParts/LogoutLink.js:19
#: assets/js/templately.js:131333
#: assets/js/templately.js:133661
msgid "Link My Account"
msgstr ""

#: assets/js/settings.js:102327
#: assets/js/settings.js:105578
#: assets/js/templately.js:1
#: react-src/app/components/profile/Profile.js:104
#: react-src/app/header/actionParts/LogoutLink.js:50
#: assets/js/templately.js:131351
#: assets/js/templately.js:133692
msgid "Unlink My Account"
msgstr ""

#: assets/js/settings.js:102327
#: assets/js/settings.js:105578
#: assets/js/templately.js:1
#: react-src/app/components/profile/Profile.js:105
#: react-src/app/header/actionParts/LogoutLink.js:51
#: assets/js/templately.js:131352
#: assets/js/templately.js:133693
msgid "Logout"
msgstr ""

#: assets/js/settings.js:102467
#: assets/js/templately.js:1
#: react-src/app/components/profile/PurchasedItems.js:110
#: assets/js/templately.js:131489
msgid "View"
msgstr ""

#: assets/js/settings.js:102634
#: assets/js/settings.js:109425
#: assets/js/templately.js:1
#: react-src/app/components/sidebar/components/CloudSidebar.js:14
#: react-src/app/routesTemplate.js:67
#: assets/js/templately.js:131607
#: assets/js/templately.js:136601
msgid "My Cloud"
msgstr ""

#: assets/js/settings.js:102645
#: assets/js/settings.js:108655
#: assets/js/templately.js:1
#: react-src/app/components/sidebar/components/CloudSidebar.js:27
#: react-src/app/layouts/CloudsLayout.js:94
#: assets/js/templately.js:131620
#: assets/js/templately.js:135910
msgid "Shared with Me"
msgstr ""

#: assets/js/settings.js:102650
#: assets/js/settings.js:105708
#: assets/js/settings.js:108639
#: assets/js/templately.js:1
#: react-src/app/components/sidebar/components/CloudSidebar.js:33
#: react-src/app/header/actionParts/ProfileDrops.js:91
#: react-src/app/layouts/CloudsLayout.js:78
#: assets/js/templately.js:131626
#: assets/js/templately.js:133805
#: assets/js/templately.js:135894
msgid "My Favourites"
msgstr ""

#: assets/js/settings.js:102655
#: assets/js/settings.js:105713
#: assets/js/settings.js:108643
#: assets/js/templately.js:1
#: react-src/app/components/sidebar/components/CloudSidebar.js:39
#: react-src/app/header/actionParts/ProfileDrops.js:101
#: react-src/app/layouts/CloudsLayout.js:82
#: assets/js/templately.js:131632
#: assets/js/templately.js:133815
#: assets/js/templately.js:135898
msgid "My Downloads"
msgstr ""

#: assets/js/settings.js:102660
#: assets/js/settings.js:105716
#: assets/js/settings.js:108663
#: assets/js/templately.js:1
#: react-src/app/components/sidebar/components/CloudSidebar.js:45
#: react-src/app/header/actionParts/ProfileDrops.js:109
#: react-src/app/layouts/CloudsLayout.js:102
#: assets/js/templately.js:131638
#: assets/js/templately.js:133823
#: assets/js/templately.js:135918
msgid "Purchased Items"
msgstr ""

#: assets/js/settings.js:102685
#: assets/js/templately.js:1
#: react-src/app/components/sidebar/components/CloudSidebar.js:67
#: assets/js/templately.js:131660
msgid "You are using %d%% of your storage quota"
msgstr ""

#: assets/js/settings.js:102693
#: assets/js/templately.js:1
#: react-src/app/components/sidebar/components/CloudSidebar.js:82
#: assets/js/templately.js:131675
msgid "Your storage quota is runing out. Please upgrade your plan to upload more files."
msgstr ""

#: assets/js/settings.js:102701
#: assets/js/templately.js:1
#: react-src/app/components/sidebar/components/CloudSidebar.js:95
#: assets/js/templately.js:131688
msgid "You have reached your limit of files. Please upgrade your plan to upload more files."
msgstr ""

#: assets/js/settings.js:102716
#: assets/js/templately.js:1
#: react-src/app/components/sidebar/components/CloudSidebar.js:119
#: assets/js/templately.js:131712
msgid "My Cloud Status"
msgstr ""

#: assets/js/settings.js:102722
#: assets/js/templately.js:1
#: react-src/app/components/sidebar/components/CloudSidebar.js:141
#: assets/js/templately.js:131734
msgid "%d of %s files"
msgstr ""

#: assets/js/settings.js:102731
#: assets/js/templately.js:1
#: react-src/app/components/sidebar/components/CloudSidebar.js:162
#: assets/js/templately.js:131755
msgid "Unlimited"
msgstr ""

#: assets/js/settings.js:102844
#: assets/js/templately.js:1
#: react-src/app/components/sidebar/components/TemplatesSidebar.js:91
#: assets/js/templately.js:131851
msgid "Categories"
msgstr ""

#: assets/js/settings.js:102848
#: assets/js/templately.js:1
#: react-src/app/components/sidebar/components/TemplatesSidebar.js:99
#: assets/js/templately.js:131859
msgid "Types"
msgstr ""

#: assets/js/settings.js:103185
#: assets/js/templately.js:1
#: react-src/app/components/sidebar/components/partials/DependenciesFilter.js:120
#: assets/js/templately.js:132125
msgid "Pro Plugins Dependencies"
msgstr ""

#: assets/js/settings.js:103205
#: assets/js/templately.js:1
#: react-src/app/components/sidebar/components/partials/DependenciesFilter.js:145
#: assets/js/templately.js:132150
msgid "Free Plugins Dependencies"
msgstr ""

#: assets/js/settings.js:103314
#: assets/js/templately.js:1
#: react-src/app/components/sidebar/components/partials/FilterButtons.js:45
#: assets/js/templately.js:132243
msgid "Filter"
msgstr ""

#: assets/js/settings.js:103322
#: assets/js/templately.js:1
#: react-src/app/components/sidebar/components/partials/FilterButtons.js:55
#: assets/js/templately.js:132253
msgid "Reset"
msgstr ""

#: assets/js/settings.js:103395
#: assets/js/templately.js:1
#: react-src/app/components/sidebar/components/partials/TagsFilter.js:33
#: assets/js/templately.js:132303
msgid "Filter Items by Tags"
msgstr ""

#: assets/js/settings.js:103397
#: assets/js/templately.js:1
#: react-src/app/components/sidebar/components/partials/TagsFilter.js:37
#: assets/js/templately.js:132307
msgid "Write a keyword..."
msgstr ""

#: assets/js/settings.js:104131
#: assets/js/templately.js:1
#: react-src/app/core/Search.js:49
#: assets/js/templately.js:132799
msgid "Type and hit enter..."
msgstr ""

#: assets/js/settings.js:104861
#: assets/js/templately.js:1
#: react-src/app/display-conditions/ConditionsWrapper.js:39
#: assets/js/templately.js:133190
msgid "Add New Condition"
msgstr ""

#: assets/js/settings.js:104899
#: assets/js/templately.js:1
#: react-src/app/display-conditions/Footer.js:13
#: assets/js/templately.js:133210
msgid "Saving..."
msgstr ""

#: assets/js/settings.js:104899
#: assets/js/templately.js:1
#: react-src/app/display-conditions/Footer.js:13
#: assets/js/templately.js:133210
msgid "Save & Close"
msgstr ""

#: assets/js/settings.js:105052
#: assets/js/templately.js:1
#: react-src/app/display-conditions/rows/ConditionType.js:9
#: assets/js/templately.js:133319
msgid "Include"
msgstr ""

#: assets/js/settings.js:105055
#: assets/js/templately.js:1
#: react-src/app/display-conditions/rows/ConditionType.js:13
#: assets/js/templately.js:133323
msgid "Exclude"
msgstr ""

#: assets/js/settings.js:105368
#: assets/js/templately.js:1
#: react-src/app/header/Actions.js:45
#: assets/js/templately.js:133512
msgid "Login"
msgstr ""

#: assets/js/settings.js:105437
#: assets/js/templately.js:1
#: react-src/app/header/Logo.js:21
#: assets/js/templately.js:133570
msgid "Back to Library"
msgstr ""

#: assets/js/settings.js:105794
#: assets/js/settings.js:110070
#: assets/js/templately.js:1
#: react-src/app/header/actionParts/Switcher.js:55
#: react-src/app/settings/components/Switcher.js:48
#: assets/js/templately.js:133892
#: assets/js/templately.js:137449
msgid "Gutenberg"
msgstr ""

#: assets/js/settings.js:106540
#: assets/js/templately.js:1
#: react-src/app/hooks/useFullSiteImport.js:335
#: assets/js/templately.js:134487
msgid "Sorry for the delay!"
msgstr ""

#: assets/js/settings.js:106541
#: assets/js/templately.js:1
#: react-src/app/hooks/useFullSiteImport.js:336
#: assets/js/templately.js:134488
msgid "If it's taking longer than expected, please try again or contact <a href='%s' target='_blank'>support</a>."
msgstr ""

#: assets/js/settings.js:108635
#: assets/js/templately.js:1
#: react-src/app/layouts/CloudsLayout.js:74
#: assets/js/templately.js:135890
msgid "Profile"
msgstr ""

#: assets/js/settings.js:108659
#: assets/js/templately.js:1
#: react-src/app/layouts/CloudsLayout.js:98
#: assets/js/templately.js:135914
msgid "Save your template"
msgstr ""

#: assets/js/settings.js:108859
#: assets/js/templately.js:1
#: react-src/app/layouts/GlobalLayout.js:28
#: assets/js/templately.js:136101
msgid "Please complete your account by the verification email we sent you, If you don't get an email"
msgstr ""

#: assets/js/settings.js:108862
#: assets/js/templately.js:1
#: react-src/app/layouts/GlobalLayout.js:37
#: assets/js/templately.js:136110
msgid "click here"
msgstr ""

#: assets/js/settings.js:108862
#: assets/js/templately.js:1
#: react-src/app/layouts/GlobalLayout.js:39
#: assets/js/templately.js:136112
msgid "to resend verification mail."
msgstr ""

#: assets/js/settings.js:108867
#: assets/js/templately.js:1
#: react-src/app/layouts/GlobalLayout.js:45
#: assets/js/templately.js:136118
msgid "Verifying . . ."
msgstr ""

#: assets/js/settings.js:108867
#: assets/js/templately.js:1
#: react-src/app/layouts/GlobalLayout.js:46
#: assets/js/templately.js:136119
msgid "I've verified & Close"
msgstr ""

#: assets/js/settings.js:109214
#: assets/js/templately.js:1
#: react-src/app/revert/Modal.js:57
#: react-src/app/settings/components/Modal.js:56
#: assets/js/templately.js:136410
#: assets/js/templately.js:137370
msgid "Are you sure you want to revert to your old website?"
msgstr ""

#: assets/js/settings.js:109216
#: assets/js/templately.js:1
#: react-src/app/revert/Modal.js:58
#: react-src/app/settings/components/Modal.js:57
#: assets/js/templately.js:136411
#: assets/js/templately.js:137371
msgid "Warning: This will remove your existing settings and content with the previous one."
msgstr ""

#: assets/js/settings.js:109221
#: assets/js/templately.js:1
#: react-src/app/revert/Modal.js:65
#: react-src/app/settings/components/Modal.js:64
#: assets/js/templately.js:136418
#: assets/js/templately.js:137378
msgid "Restoring previous version of your website. This usually takes a few moments. Please wait…"
msgstr ""

#: assets/js/settings.js:109227
#: assets/js/templately.js:1
#: react-src/app/revert/Modal.js:76
#: react-src/app/settings/components/Modal.js:75
#: assets/js/templately.js:136429
#: assets/js/templately.js:137389
msgid "Processing..."
msgstr ""

#: assets/js/settings.js:109227
#: assets/js/templately.js:1
#: react-src/app/revert/Modal.js:76
#: react-src/app/settings/components/Modal.js:75
#: assets/js/templately.js:136429
#: assets/js/templately.js:137389
msgid "Yes, Revert"
msgstr ""

#: assets/js/settings.js:109283
#: assets/js/templately.js:1
#: react-src/app/revert/RevertWrapper.js:28
#: react-src/app/settings/tabs/Miscellaneous.js:26
#: assets/js/templately.js:136469
#: assets/js/templately.js:138554
msgid "Successfully Reverted"
msgstr ""

#: assets/js/settings.js:109284
#: assets/js/templately.js:1
#: react-src/app/revert/RevertWrapper.js:29
#: react-src/app/settings/tabs/Miscellaneous.js:27
#: assets/js/templately.js:136470
#: assets/js/templately.js:138555
msgid "Old website has been successfully restored."
msgstr ""

#: assets/js/settings.js:109330
#: assets/js/templately.js:1
#: react-src/app/revert/RevertWrapper.js:89
#: assets/js/templately.js:136530
msgid "Revert Now"
msgstr ""

#: assets/js/settings.js:115152
#: assets/js/templately.js:1
#: react-src/redux/sagas/application/profileSaga.js:124
#: assets/js/templately.js:141618
msgid "Successfully Synced Account"
msgstr ""

#: assets/js/settings.js:119006
#: assets/js/templately.js:1
#: react-src/utils/cloudFilesContextMenu.js:28
#: assets/js/templately.js:143675
msgid "Download Failed!!!"
msgstr ""

#: assets/js/settings.js:119010
#: assets/js/templately.js:1
#: react-src/utils/cloudFilesContextMenu.js:32
#: assets/js/templately.js:143679
msgid "Successfully Downloaded!"
msgstr ""

#: assets/js/settings.js:119046
#: assets/js/templately.js:1
#: react-src/utils/cloudFilesContextMenu.js:64
#: assets/js/templately.js:143711
msgid "Successfully Copied"
msgstr ""

#: assets/js/settings.js:119049
#: assets/js/templately.js:1
#: react-src/utils/cloudFilesContextMenu.js:68
#: assets/js/templately.js:143715
msgid "Your file has been copied to a WorkSpace called: "
msgstr ""

#: assets/js/settings.js:119049
#: assets/js/templately.js:1
#: react-src/utils/cloudFilesContextMenu.js:72
#: assets/js/templately.js:143719
msgid "Your file has been moved to a WorkSpace called: "
msgstr ""

#: assets/js/settings.js:119074
#: assets/js/templately.js:1
#: react-src/utils/cloudFilesContextMenu.js:101
#: assets/js/templately.js:143748
msgid "Something went wrong!"
msgstr ""

#: assets/js/settings.js:119078
#: assets/js/templately.js:1
#: react-src/utils/cloudFilesContextMenu.js:106
#: assets/js/templately.js:143753
msgid "Successfully Deleted!"
msgstr ""

#: assets/js/settings.js:119108
#: assets/js/templately.js:1
#: react-src/utils/cloudFilesContextMenu.js:147
#: assets/js/templately.js:143794
msgid "Download"
msgstr ""

#: assets/js/settings.js:119113
#: assets/js/templately.js:1
#: react-src/utils/cloudFilesContextMenu.js:153
#: assets/js/templately.js:143800
msgid "Copy to WorkSpace"
msgstr ""

#: assets/js/settings.js:119120
#: assets/js/templately.js:1
#: react-src/utils/cloudFilesContextMenu.js:159
#: assets/js/templately.js:143806
msgid "Move to WorkSpace"
msgstr ""

#: assets/js/settings.js:120050
#: assets/js/templately.js:1
#: react-src/utils/messages.js:18
#: assets/js/templately.js:144571
msgid "Please enter a valid email address."
msgstr ""

#: assets/js/settings.js:120057
#: assets/js/templately.js:1
#: react-src/utils/messages.js:28
#: assets/js/templately.js:144581
msgid "Please provide a valid API Key."
msgstr ""

#: assets/js/settings.js:120065
#: assets/js/templately.js:1
#: react-src/utils/messages.js:37
#: assets/js/templately.js:144590
msgid "The First Name may only contain letters, dashes and spaces."
msgstr ""

#: assets/js/settings.js:120074
#: assets/js/templately.js:1
#: react-src/utils/messages.js:47
#: assets/js/templately.js:144600
msgid "The Last Name may only contain letters, dashes and spaces."
msgstr ""

#: assets/js/settings.js:120081
#: assets/js/templately.js:1
#: react-src/utils/messages.js:57
#: assets/js/templately.js:144610
msgid "Password must contain at least 8 characters, uppercase, lowercase and numbers."
msgstr ""

#: assets/js/settings.js:120087
#: assets/js/templately.js:1
#: react-src/utils/messages.js:69
#: assets/js/templately.js:144622
msgid "Password not matched!"
msgstr ""

#: assets/js/settings.js:120134
#: assets/js/templately.js:1
#: react-src/utils/notVerifiedModal.js:16
#: assets/js/templately.js:144648
msgid "Sorry your account is not verified. Please verify your account to start inserting items."
msgstr ""

#: assets/js/settings.js:120266
#: assets/js/templately.js:1
#: react-src/utils/savedTemplatesContextMenu.js:43
#: assets/js/templately.js:144737
msgid "Successfully Pushed"
msgstr ""

#: assets/js/settings.js:120268
#: assets/js/templately.js:1
#: react-src/utils/savedTemplatesContextMenu.js:46
#: assets/js/templately.js:144740
msgid "Your file has been pushed successfully to your cloud store."
msgstr ""

#: assets/js/settings.js:120290
#: assets/js/templately.js:1
#: react-src/utils/savedTemplatesContextMenu.js:78
#: assets/js/templately.js:144772
msgid "The required dependency ‘Elementor’ is missing. Please install it before proceeding."
msgstr ""

#: assets/js/settings.js:120294
#: assets/js/templately.js:1
#: react-src/utils/savedTemplatesContextMenu.js:84
#: assets/js/templately.js:144778
msgid "Elementor not installed."
msgstr ""

#: assets/js/settings.js:120308
#: assets/js/templately.js:1
#: react-src/utils/savedTemplatesContextMenu.js:101
#: assets/js/templately.js:144795
msgid "Preview"
msgstr ""

#: assets/js/settings.js:120316
#: assets/js/templately.js:1
#: react-src/utils/savedTemplatesContextMenu.js:110
#: assets/js/templately.js:144804
msgid "Push To Cloud"
msgstr ""

#: assets/js/settings.js:120329
#: assets/js/templately.js:1
#: react-src/utils/savedTemplatesContextMenu.js:124
#: assets/js/templately.js:144818
msgid "Export"
msgstr ""

#: assets/js/settings.js:120533
#: assets/js/templately.js:1
#: react-src/utils/workSpaceContextMenu.js:16
#: assets/js/templately.js:144981
msgid "Something went wrong regarding changes in your WorkSpace. Try Later or Contact support."
msgstr ""

#: assets/js/settings.js:120536
#: assets/js/templately.js:1
#: react-src/utils/workSpaceContextMenu.js:22
#: assets/js/templately.js:144987
msgid "Successfully Saved"
msgstr ""

#: assets/js/settings.js:120537
#: assets/js/templately.js:1
#: react-src/utils/workSpaceContextMenu.js:23
#: assets/js/templately.js:144988
msgid "You have succesfully saved your WorkSpace changes."
msgstr ""

#: assets/js/settings.js:120559
#: assets/js/templately.js:1
#: react-src/utils/workSpaceContextMenu.js:45
#: assets/js/templately.js:145010
msgid "Something went wrong regarding deleting your WorkSpace. Try Later or Contact Support."
msgstr ""

#: assets/js/settings.js:120563
#: assets/js/templately.js:1
#: react-src/utils/workSpaceContextMenu.js:52
#: assets/js/templately.js:145017
msgid "Successfully Deleted"
msgstr ""

#: assets/js/settings.js:120564
#: assets/js/templately.js:1
#: react-src/utils/workSpaceContextMenu.js:53
#: assets/js/templately.js:145018
msgid "You have deleted a WorkSpace from your account."
msgstr ""

#: assets/js/settings.js:120584
#: assets/js/templately.js:1
#: react-src/utils/workSpaceContextMenu.js:76
#: assets/js/templately.js:145041
msgid "Edit"
msgstr ""

#: assets/js/templately.js:1
#: react-src/app/settings/components/Switcher.js:95
#: assets/js/templately.js:137496
msgid "Please Install Elementor."
msgstr ""

#: assets/js/templately.js:1
#: react-src/app/settings/components/Switcher.js:97
#: assets/js/templately.js:137498
msgid "Please Activate Elementor."
msgstr ""

#: assets/js/templately.js:1
#: react-src/app/settings/SettingsLayout.js:69
#: assets/js/templately.js:136687
msgid "Successfully Updated Settings"
msgstr ""

#: assets/js/templately.js:1
#: react-src/app/settings/SettingsLayout.js:82
#: assets/js/templately.js:136700
msgid "Failed to Update Settings"
msgstr ""
